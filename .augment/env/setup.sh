#!/bin/bash
set -e

echo "Setting up Chessticize Next.js development environment..."

# Update package lists
sudo apt-get update

# Install Node.js 18.x (LTS) if not already installed
if ! command -v node &> /dev/null || [[ $(node --version | cut -d'.' -f1 | cut -d'v' -f2) -lt 18 ]]; then
    echo "Installing Node.js 18.x..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Verify Node.js version
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"

# Install pnpm globally if not already installed
if ! command -v pnpm &> /dev/null; then
    echo "Installing pnpm..."
    npm install -g pnpm
fi

# Verify pnpm installation
echo "pnpm version: $(pnpm --version)"

# Add pnpm to PATH in user's profile if not already there
if ! grep -q "pnpm" "$HOME/.profile" 2>/dev/null; then
    echo 'export PATH="$HOME/.local/share/pnpm:$PATH"' >> "$HOME/.profile"
fi

# Navigate to project directory
cd /mnt/persist/workspace

# Install project dependencies
echo "Installing project dependencies..."
pnpm install

# Verify installation by checking if node_modules exists and has content
if [ ! -d "node_modules" ] || [ -z "$(ls -A node_modules)" ]; then
    echo "Error: Dependencies installation failed"
    exit 1
fi

echo "Setup completed successfully!"
echo "Project dependencies installed with pnpm"
echo "Ready to run tests with: pnpm test"