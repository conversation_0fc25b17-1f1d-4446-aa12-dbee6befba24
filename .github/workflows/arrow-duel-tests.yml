name: Arrow Duel Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'components/puzzle-sprint/**'
      - 'lib/arrow-duel-filter.ts'
      - 'hooks/useStockfish.ts'
      - 'app/puzzle-sprint/arrow-duel/**'
      - '__tests__/**/*ArrowDuel*'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'components/puzzle-sprint/**'
      - 'lib/arrow-duel-filter.ts'
      - 'hooks/useStockfish.ts'
      - 'app/puzzle-sprint/arrow-duel/**'
      - '__tests__/**/*ArrowDuel*'

jobs:
  arrow-duel-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'
        
    - name: Install pnpm
      run: npm install -g pnpm
      
    - name: Install dependencies
      run: pnpm install --frozen-lockfile
      
    - name: Run TypeScript check
      run: pnpm tsc --noEmit
      
    - name: Run ESLint
      run: pnpm lint --max-warnings 0
      
    - name: Run Arrow Duel smoke tests
      run: pnpm test:smoke
      
    - name: Run Arrow Duel integration tests
      run: pnpm test:integration
      
    - name: Test build compilation
      run: pnpm build
      
    - name: Comment on PR (if tests fail)
      if: failure() && github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '❌ Arrow Duel tests failed. Please check the build logs and fix any compilation or test errors before manual testing.'
          })
          
    - name: Comment on PR (if tests pass)
      if: success() && github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '✅ Arrow Duel tests passed! Ready for manual testing.'
          })