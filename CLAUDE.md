# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Package manager: Use pnpm (not npm)
pnpm install              # Install dependencies
pnpm dev                  # Start development server (localhost:3000)
pnpm build                # Build for production
pnpm start                # Start production server
pnpm lint                 # Run ESLint
pnpm test                 # Run tests with Vitest
pnpm test:watch           # Run tests in watch mode
pnpm test:ui              # Run tests with UI interface

# Testing commands
# Single test: pnpm test path/to/test.test.ts
# Test pattern: pnpm test -- --grep "pattern"
```

## Architecture Overview

**Framework**: Next.js 15.2.4 with App Router, TypeScript, and Tailwind CSS

### Core Structure
- **Authentication**: JWT-based with refresh tokens using cookies
- **State Management**: React Context (AuthProvider, UserProvider) + custom hooks
- **API Client**: Custom API client in `lib/auth/api-client.ts` with automatic token refresh
- **Chess Engine**: Stockfish integration using global singleton pattern with WebAssembly
- **UI Components**: shadcn/ui (Radix UI primitives) + custom chess components

### Key Directories
```
app/                    # Next.js App Router pages
├── dashboard/         # Main dashboard after login
├── game-review/       # Game analysis and insights
├── puzzle-sprint/     # Tactical training modes
└── [auth]/           # Login/register pages

components/
├── ui/               # shadcn/ui components (DON'T MODIFY)
├── auth/             # Authentication components
├── puzzle-sprint/    # Chess training components
└── game-review/      # Game analysis components

lib/
├── api/              # API clients (GraphQL, auth)
├── auth/             # Authentication logic and types
└── utils/            # Utility functions

hooks/                # Custom React hooks
contexts/             # React contexts (UserContext, etc.)
```

### Authentication Flow
1. Uses JWT access tokens + refresh tokens stored in secure cookies
2. `AuthProvider` wraps app with token management
3. `UserProvider` handles user data and chess profiles
4. `ProtectedRoute` component for route protection
5. API client automatically handles token refresh

### Chess Engine Integration
- **Stockfish**: Global singleton WebAssembly engine for analysis
- **Chess.js**: Move validation and game logic
- **react-chessboard**: Interactive chess board component
- **Touch Support**: Multi-backend drag-and-drop (HTML5 + touch)

### API Integration
- **Base URL**: Environment-aware (localhost:8080 for dev, production URL for prod)
- **GraphQL**: Custom client in `lib/api/graphql.ts` with predefined queries
- **REST**: Authentication endpoints and file uploads

## Important Patterns

### Component Organization
- UI components use shadcn/ui patterns with `cn()` utility for classes
- Chess components handle both desktop and mobile interactions
- Error boundaries wrap complex components (puzzle sessions, analysis)

### State Management
```typescript
// Context pattern for global state
const { user, isLoading } = useUserContext()
const { isAuthenticated } = useAuthContext()

// Custom hooks for specific features
const { analysis, analyzePosition } = useStockfish()
const { puzzles, loadPuzzles } = useSprintApi()
```

### API Error Handling
- `ApiError` class with status codes
- Automatic retry for network failures
- Token refresh on 401 responses
- User-friendly error messages

### Environment Configuration
```typescript
// lib/env.ts - Environment variables with validation
// lib/config.ts - API configuration with fallbacks
// Automatic environment detection (dev/prod)
```

## Testing Setup

- **Framework**: Vitest with jsdom environment
- **Testing Library**: React Testing Library + user-event
- **Setup**: `vitest.setup.ts` configures jest-dom matchers
- **Coverage**: Excludes legacy-site and worker-test directories

### Arrow Duel Specific Testing

- **Pre-deployment Tests**: `pnpm test:arrow-duel` - Comprehensive test suite
- **Smoke Tests**: `pnpm test:smoke` - Quick compilation and rendering checks
- **Integration Tests**: `pnpm test:integration` - Full Arrow Duel workflow tests
- **Test Script**: `./scripts/test-arrow-duel.sh` - Automated test runner

**Always run before manual testing:**
```bash
pnpm test:arrow-duel
```

This catches:
- TypeScript compilation errors
- Missing function references
- Component rendering failures
- Import/export issues
- Basic interaction problems

## Common Development Tasks

### Adding New API Endpoints
1. Add endpoint to `lib/config.ts` ENDPOINTS
2. Create function in appropriate API client (`lib/api/`)
3. Add error handling with `ApiError`
4. Create custom hook if needed (`hooks/`)

### Adding New Pages
1. Create in `app/` directory following App Router conventions
2. Wrap with `ProtectedRoute` if authentication required
3. Use `UserProvider` context for user data
4. Follow mobile-first responsive design

### Chess Component Development
1. Import `chess.js` for game logic
2. Use `react-chessboard` for board rendering
3. Handle both mouse and touch interactions
4. Integrate with Stockfish for analysis via `useStockfish()`

### Styling Guidelines
- Use Tailwind CSS with design system variables
- Follow existing component patterns
- Ensure mobile responsiveness (mobile-first)
- Use shadcn/ui components when possible

## Production Considerations

- **No service worker** - cache-free setup for always fresh content
- **Cross-origin headers** configured for Stockfish WebAssembly
- **TypeScript/ESLint errors** ignored during builds (temporary)
- **Image optimization** disabled for compatibility

## Environment Variables

```bash
NEXT_PUBLIC_API_BASE_URL=    # API endpoint (auto-detected if empty)
NEXT_PUBLIC_ENABLE_DEBUG_LOGS=true  # Debug logging
NODE_ENV=development         # Environment mode
```

## Legacy Components

- `legacy-site/` contains original React app (separate codebase)
- `worker-test/` contains Python backend tests (ignored by main app)
- Some components reference legacy patterns for backward compatibility