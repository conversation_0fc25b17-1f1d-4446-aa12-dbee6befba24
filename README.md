# Chessticize

A Next.js-based chess training application that helps users improve their chess tactics and performance through interactive puzzles and game analysis.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Node.js** (v18.18.0 or higher)
  - Download from [nodejs.org](https://nodejs.org/)
  - Verify installation: `node --version`

- **pnpm** (Package Manager)
  - This project uses pnpm for better performance and disk space efficiency
  - Install globally: `npm install -g pnpm`
  - Verify installation: `pnpm --version`

## Install dependencies

```bash
pnpm install
```

## Development

### Start the Development Server

```bash
pnpm dev
```

This will start the Next.js development server at `http://localhost:3000`.

### Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build the application for production
- `pnpm start` - Start the production server
- `pnpm lint` - Run ESLint to check code quality
- `pnpm test` - Run tests
- `pnpm test:watch` - Run tests in watch mode
- `pnpm test:ui` - Run tests with UI interface

### Running with Production API

To test the application locally with the production API server:

```bash
# Option 1: Set NODE_ENV to production (uses production API automatically)
NODE_ENV=production pnpm dev

# Option 2: Explicitly set the production API URL
NEXT_PUBLIC_API_BASE_URL=https://chessticize-server-9ddca5bcf137.herokuapp.com pnpm dev

# Option 3: Update .env.local file
echo "NEXT_PUBLIC_API_BASE_URL=https://chessticize-server-9ddca5bcf137.herokuapp.com" > .env.local
pnpm dev
```

The application automatically selects the appropriate API endpoint:
- **Development**: `http://localhost:8080` (local server)
- **Production**: `https://chessticize-server-9ddca5bcf137.herokuapp.com` (hosted server)

## Testing

### Main Project Testing

Currently, the main Next.js application doesn't have test scripts configured. To add testing capabilities, you can install and configure your preferred testing framework:

**For Jest + React Testing Library:**
```bash
pnpm add -D jest @testing-library/react @testing-library/jest-dom jest-environment-jsdom
```

**For Vitest (recommended for Next.js):**
```bash
pnpm add -D vitest @testing-library/react @testing-library/jest-dom jsdom
```

## Building for Production

1. **Build the application**:
   ```bash
   pnpm build
   ```

2. **Start production server**:
   ```bash
   pnpm start
   ```

The production build will be optimized and ready for deployment.

## Project Structure

```
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Dashboard page
│   ├── game-review/       # Game review functionality
│   ├── login/             # Authentication pages
│   └── ...
├── components/            # Reusable React components
│   ├── ui/               # UI components (shadcn/ui)
│   └── ...
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions
├── public/               # Static assets
├── styles/               # Global styles
└── ...
```

## Technology Stack

- **Framework**: Next.js 15.2.4
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI + shadcn/ui
- **Package Manager**: pnpm

## Environment Setup

The application uses environment-based configuration for API endpoints:

### Automatic Configuration
- **Development**: Automatically uses `http://localhost:8080`
- **Production**: Automatically uses `https://chessticize-server-9ddca5bcf137.herokuapp.com`

### Manual Configuration (Optional)
1. Copy the example environment file:
   ```bash
   cp .env.local.example .env.local
   ```

2. Edit `.env.local` to override defaults:
   ```bash
   # API Configuration (optional - uses environment defaults if empty)
   NEXT_PUBLIC_API_BASE_URL=

   # Debug Configuration
   NEXT_PUBLIC_ENABLE_DEBUG_LOGS=true
   ```

### Available Environment Variables
- `NEXT_PUBLIC_API_BASE_URL`: Override the default API endpoint
- `NEXT_PUBLIC_ENABLE_DEBUG_LOGS`: Enable debug logging (default: false in production)
- `NODE_ENV`: Environment mode (development/production/test)

## Authentication

The application includes a complete authentication system:

### Features
- **Login/Registration**: Email and password authentication with invitation codes
- **Session Management**: Automatic token refresh and persistent sessions
- **Protected Routes**: Automatic redirection for unauthenticated users
- **Secure Storage**: Token storage using secure HTTP-only cookies

### Usage
1. **Registration**: Requires a valid invitation code from the server admin
2. **Login**: Use email and password to authenticate
3. **Automatic Sessions**: Stay logged in across browser sessions
4. **Logout**: Clears all authentication tokens

### Testing Authentication
To test with the production server, you'll need:
1. A valid invitation code (contact server admin)
2. Or existing user credentials

For local development, ensure the local server is running at `http://localhost:8080`.

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and test them
4. Commit your changes: `git commit -m 'Add feature'`
5. Push to the branch: `git push origin feature-name`
6. Submit a pull request

## Troubleshooting

### Common Issues

1. **pnpm not found**: Make sure pnpm is installed globally with `npm install -g pnpm`

2. **Node version issues**: Ensure you're using Node.js v18.18.0 or higher

3. **Port already in use**: If port 3000 is busy, Next.js will automatically use the next available port

4. **Build errors**: Run `pnpm lint` to check for code issues before building

### Getting Help

- Check the [Next.js documentation](https://nextjs.org/docs)
- Open an issue on the repository for bugs or feature requests

## License

This project is private and proprietary.
