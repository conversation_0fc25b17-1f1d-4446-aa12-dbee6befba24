import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import RegisterPage from '@/app/register/page'
import { useAuthContext } from '@/components/auth/auth-provider'
import { useRouter } from 'next/navigation'
import { useChessProfiles } from '@/hooks/useChessProfiles'
import { useUserContext } from '@/hooks/useUserContext'

// Mock the auth context
vi.mock('@/components/auth/auth-provider', () => ({
  useAuthContext: vi.fn()
}))

// Mock chess profiles hook
vi.mock('@/hooks/useChessProfiles', () => ({
  useChessProfiles: vi.fn()
}))

// Mock user context hook
vi.mock('@/hooks/useUserContext', () => ({
  useUserContext: vi.fn()
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn()
}))

// Mock the config
vi.mock('@/lib/config', () => ({
  APP_CONFIG: {
    DEFAULT_LOGIN_REDIRECT: '/dashboard'
  },
  API_CONFIG: {
    BASE_URL: 'http://localhost:8080',
    ENDPOINTS: {
      REGISTER: '/api/v1/auth/register'
    }
  },
  debugLog: vi.fn()
}))

// Mock the token utils
vi.mock('@/lib/auth/tokens', () => ({
  hasValidAuth: vi.fn(() => false)
}))

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('RegisterPage', () => {
  const mockRegister = vi.fn()
  const mockClearError = vi.fn()
  const mockPush = vi.fn()
  const mockCreateChessProfile = vi.fn()
  const mockUpdateChessProfiles = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup auth context mock
    vi.mocked(useAuthContext).mockReturnValue({
      register: mockRegister,
      isLoading: false,
      error: null,
      clearError: mockClearError,
      isAuthenticated: false,
      user: null,
      login: vi.fn(),
      logout: vi.fn(),
      checkAuth: vi.fn()
    })

    // Setup chess profiles mock
    vi.mocked(useChessProfiles).mockReturnValue({
      isCreatingProfile: false,
      profileError: null,
      successMessage: null,
      createChessProfile: mockCreateChessProfile,
      clearMessages: vi.fn()
    })

    // Setup user context mock
    vi.mocked(useUserContext).mockReturnValue({
      user: null,
      isLoading: false,
      error: null,
      refreshUser: vi.fn(),
      updateChessProfiles: mockUpdateChessProfiles
    })

    // Setup router mock
    vi.mocked(useRouter).mockReturnValue({
      push: mockPush,
      replace: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
      prefetch: vi.fn()
    })
  })

  it('should render registration form with chess username fields', () => {
    render(<RegisterPage />)
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/lichess username/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/chess\.com username/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument()
  })

  it('should validate chess usernames before registration', async () => {
    // Mock successful chess.com validation
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ player_id: 12345 })
    })

    // Mock successful lichess validation
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ id: 'testuser' })
    })

    render(<RegisterPage />)
    
    // Fill in the form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/chess\.com username/i), {
      target: { value: 'testuser' }
    })
    fireEvent.change(screen.getByLabelText(/lichess username/i), {
      target: { value: 'testuser' }
    })

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /create account/i }))

    // Wait for validation to complete
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('https://api.chess.com/pub/player/testuser')
      expect(mockFetch).toHaveBeenCalledWith('https://lichess.org/api/user/testuser')
    })

    // Should call register after successful validation
    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      })
    })
  })

  it('should show error for invalid chess.com username', async () => {
    // Mock failed chess.com validation
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({}) // No player_id
    })

    render(<RegisterPage />)

    // Fill in the chess.com username field and trigger validation
    const chessComInput = screen.getByLabelText(/chess\.com username/i)
    fireEvent.change(chessComInput, {
      target: { value: 'nonexistent' }
    })

    // Trigger validation by blurring the field
    fireEvent.blur(chessComInput)

    // Wait for validation error to appear
    await waitFor(() => {
      expect(screen.getByText(/chess\.com account does not exist/i)).toBeInTheDocument()
    })

    // Validation should have been called
    expect(mockFetch).toHaveBeenCalledWith('https://api.chess.com/pub/player/nonexistent')
  })

  it('should allow registration without chess usernames', async () => {
    render(<RegisterPage />)
    
    // Fill in only required fields
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /create account/i }))

    // Should call register without validation
    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      })
    })

    // Should not call fetch for validation
    expect(mockFetch).not.toHaveBeenCalled()
  })

  it('should create chess profiles after successful registration', async () => {
    // Mock successful chess.com validation
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ player_id: 12345 })
    })

    // Mock successful lichess validation
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ id: 'testuser' })
    })

    // Mock successful chess profile creation
    const mockLichessProfile = { id: '1', platform: 'lichess.org', username: 'testuser' }
    const mockChessComProfile = { id: '2', platform: 'chess.com', username: 'testuser' }

    mockCreateChessProfile
      .mockResolvedValueOnce(mockLichessProfile)
      .mockResolvedValueOnce(mockChessComProfile)

    render(<RegisterPage />)

    // Fill in the form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/chess\.com username/i), {
      target: { value: 'testuser' }
    })
    fireEvent.change(screen.getByLabelText(/lichess username/i), {
      target: { value: 'testuser' }
    })

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /create account/i }))

    // Wait for registration to complete
    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      })
    })

    // Wait for chess profiles to be created
    await waitFor(() => {
      expect(mockCreateChessProfile).toHaveBeenCalledWith('lichess.org', 'testuser')
      expect(mockCreateChessProfile).toHaveBeenCalledWith('chess.com', 'testuser')
    })

    // Should update user context with created profiles
    await waitFor(() => {
      expect(mockUpdateChessProfiles).toHaveBeenCalledWith([mockLichessProfile, mockChessComProfile])
    })
  })

  it('should handle chess profile creation errors gracefully', async () => {
    // Mock successful registration but failed profile creation
    mockCreateChessProfile.mockRejectedValueOnce(new Error('Profile creation failed'))

    render(<RegisterPage />)

    // Fill in the form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/lichess username/i), {
      target: { value: 'testuser' }
    })

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /create account/i }))

    // Wait for registration to complete
    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalled()
    })

    // Profile creation should be attempted but fail gracefully
    await waitFor(() => {
      expect(mockCreateChessProfile).toHaveBeenCalledWith('lichess.org', 'testuser')
    })

    // Registration should still succeed even if profile creation fails
    // (No error should be thrown to the user)
  })
})
