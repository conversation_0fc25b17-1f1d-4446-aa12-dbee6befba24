/**
 * Stockfish Filter Unit Tests
 * Tests the core filtering logic for Arrow Duel puzzle generation
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { ArrowDuelFilter, type SprintPuzzle, type ArrowDuelPuzzle } from '@/lib/arrow-duel-filter'

// Mock the Stockfish hook
vi.mock('@/hooks/useStockfish', () => {
  const mockStockfishEngine = {
    uci: vi.fn(),
    listen: null as any,
    terminate: vi.fn()
  }
  
  return {
    getGlobalStockfishEngine: vi.fn().mockResolvedValue(mockStockfishEngine)
  }
})

describe('ArrowDuelFilter', () => {
  let filter: ArrowDuelFilter
  let consoleSpy: any
  let mockStockfishEngine: any

  beforeEach(async () => {
    // Get the mocked engine
    const { getGlobalStockfishEngine } = await import('@/hooks/useStockfish')
    mockStockfishEngine = await getGlobalStockfishEngine()
    
    filter = new ArrowDuelFilter()
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.clearAllMocks()
  })

  afterEach(() => {
    consoleSpy.mockRestore()
    filter.clearCache()
  })

  describe('Evaluation Threshold Logic', () => {
    it('should accept puzzles with significant evaluation difference (> 1.0 pawns)', async () => {
      // Test position: Queen blunder (Qh5 vs Nf3)
      const testPuzzle: SprintPuzzle = {
        puzzle_id: 'test-blunder-1',
        fen: 'rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2',
        solution_moves: ['Qh5'], // This hangs the queen (blunder)
        rating: 1200,
        themes: ['blunder'],
        sequence_in_sprint: 1
      }

      // Mock Stockfish analysis
      setupMockAnalysis(mockStockfishEngine, [
        { evaluation: 50, bestMove: 'g1f3', depth: 18 },    // Initial position (slight advantage) - UCI format
        { evaluation: -900, bestMove: 'g8f6', depth: 18 }  // After Qh5 (queen hangs) - UCI format
      ])

      const result = await filter.filterPuzzle(testPuzzle)

      expect(result).not.toBeNull()
      expect(result!.bestMove).toBe('g1f3')
      expect(result!.blunderMove).toBe('Qh5')
      expect(Math.abs(result!.evaluationDiff)).toBeGreaterThan(100) // > 1.0 pawns
      expect(result!.analysisDepth).toBeGreaterThan(0) // Should have analysis depth
      expect(typeof result!.initialEval).toBe('number') // Should have debug info
    })

    it('should reject puzzles with small evaluation difference (< 1.0 pawns)', async () => {
      // Test position: Opening moves with similar value
      const testPuzzle: SprintPuzzle = {
        puzzle_id: 'test-equal-1',
        fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
        solution_moves: ['e2e3'], // Slightly inferior to e2e4
        rating: 800,
        themes: ['opening'],
        sequence_in_sprint: 1
      }

      // Mock Stockfish analysis - small difference
      setupMockAnalysis(mockStockfishEngine, [
        { evaluation: 30, bestMove: 'e2e4', depth: 10 },   // e4 is best
        { evaluation: -10, bestMove: 'd2d4', depth: 10 }   // e3 is slightly worse
      ])

      const result = await filter.filterPuzzle(testPuzzle)

      expect(result).toBeNull() // Should be rejected due to small eval difference
    })

    it('should handle mate scores correctly', async () => {
      // Test position: Simple legal position with mate threat
      const testPuzzle: SprintPuzzle = {
        puzzle_id: 'test-mate-1',
        fen: '6k1/5ppp/8/8/8/8/5PPP/4R1K1 w - - 0 1',
        solution_moves: ['g2g3'], // Weakens king safety
        rating: 1500,
        themes: ['mate'],
        sequence_in_sprint: 1
      }

      // Mock Stockfish analysis with mate scores
      setupMockAnalysis(mockStockfishEngine, [
        { evaluation: 0, bestMove: 'e1e8', depth: 10 },     // Mate threat
        { evaluation: -10000, bestMove: 'g8h7', depth: 10 } // After g3, mate follows
      ])

      const result = await filter.filterPuzzle(testPuzzle)

      expect(result).not.toBeNull()
      expect(Math.abs(result!.evaluationDiff)).toBeGreaterThan(1000) // Mate should be huge difference
    })
  })

  describe('Legal Move Validation', () => {
    it('should reject puzzles with illegal moves', async () => {
      const testPuzzle: SprintPuzzle = {
        puzzle_id: 'test-illegal-1',
        fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
        solution_moves: ['e2e5'], // Illegal move (pawn can't jump 3 squares)
        rating: 1000,
        themes: ['tactics'],
        sequence_in_sprint: 1
      }

      // No need to mock Stockfish since illegal move should be caught early
      const result = await filter.filterPuzzle(testPuzzle)

      expect(result).toBeNull() // Should be rejected due to illegal move
    }, 1000) // Short timeout since this should fail quickly

    it('should reject when Stockfish suggests illegal moves', async () => {
      const testPuzzle: SprintPuzzle = {
        puzzle_id: 'test-stockfish-illegal-1',
        fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
        solution_moves: ['e2e4'], // Legal move
        rating: 1000,
        themes: ['opening'],
        sequence_in_sprint: 1
      }

      // Mock Stockfish suggesting illegal move
      setupMockAnalysis(mockStockfishEngine, [
        { evaluation: 30, bestMove: 'e2e5', depth: 10 }, // Illegal suggestion
        { evaluation: -20, bestMove: 'd2d4', depth: 10 }
      ])

      const result = await filter.filterPuzzle(testPuzzle)

      expect(result).toBeNull() // Should be rejected due to illegal best move
    })
  })

  describe('Error Handling', () => {
    it('should handle Stockfish timeout gracefully', async () => {
      const testPuzzle: SprintPuzzle = {
        puzzle_id: 'test-timeout-1',
        fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
        solution_moves: ['e2e4'],
        rating: 1000,
        themes: ['opening'],
        sequence_in_sprint: 1
      }

      // Mock timeout scenario - no analysis response
      mockStockfishEngine.listen = vi.fn()
      mockStockfishEngine.uci = vi.fn(() => {
        // Simulate timeout by never calling the message handler
      })

      const result = await filter.filterPuzzle(testPuzzle)

      expect(result).toBeNull() // Should handle timeout gracefully (no usable result)
    }, 10000) // Increase timeout for this test

    it('should use partial result on timeout when available', async () => {
      const testPuzzle: SprintPuzzle = {
        puzzle_id: 'test-timeout-partial-1',
        fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
        solution_moves: ['e2e4'],
        rating: 1000,
        themes: ['opening'],
        sequence_in_sprint: 1
      }

      // Mock scenario where we get partial analysis before timeout
      let callCount = 0
      mockStockfishEngine.uci = vi.fn((command: string) => {
        if (command.startsWith('go depth')) {
          // Send partial analysis after a delay
          setTimeout(() => {
            if (mockStockfishEngine.listen) {
              // Send info line with partial result
              mockStockfishEngine.listen('info depth 5 score cp 25 pv e2e4')
              // Don't send bestmove - simulate timeout before completion
            }
          }, 100)
        }
      })

      const result = await filter.filterPuzzle(testPuzzle)

      // Should get partial result instead of null due to graceful timeout handling
      expect(result).not.toBeNull()
      if (result) {
        expect(result.bestMove).toBe('e2e4')
        expect(result.analysisDepth).toBeLessThan(12) // Should be partial depth
      }
    }, 10000)

    it('should handle malformed puzzle data', async () => {
      const testPuzzle: SprintPuzzle = {
        puzzle_id: 'test-malformed-1',
        fen: '', // Empty FEN
        solution_moves: [],
        rating: 1000,
        themes: ['tactics'],
        sequence_in_sprint: 1
      }

      const result = await filter.filterPuzzle(testPuzzle)

      expect(result).toBeNull() // Should handle malformed data gracefully
    })
  })

  describe('Candidate Move Generation', () => {
    it('should randomize candidate move order', async () => {
      const testPuzzle: SprintPuzzle = {
        puzzle_id: 'test-randomize-1',
        fen: 'rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2',
        solution_moves: ['Qh5'],
        rating: 1200,
        themes: ['blunder'],
        sequence_in_sprint: 1
      }

      setupMockAnalysis(mockStockfishEngine, [
        { evaluation: 50, bestMove: 'g1f3', depth: 10 },
        { evaluation: -900, bestMove: 'g8f6', depth: 10 }
      ])

      // Run multiple times to check randomization
      const results: string[][] = []
      for (let i = 0; i < 10; i++) {
        const result = await filter.filterPuzzle(testPuzzle)
        if (result) {
          results.push([...result.candidateMoves])
        }
      }

      // Should have at least some variation in ordering
      const uniqueOrders = new Set(results.map(r => r.join('-')))
      expect(uniqueOrders.size).toBeGreaterThan(1) // At least 2 different orderings
    })
  })

  describe('Performance and Caching', () => {
    it('should cache analysis results', async () => {
      const testPuzzle: SprintPuzzle = {
        puzzle_id: 'test-cache-1',
        fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
        solution_moves: ['e2e4'],
        rating: 1000,
        themes: ['opening'],
        sequence_in_sprint: 1
      }

      setupMockAnalysis(mockStockfishEngine, [
        { evaluation: 30, bestMove: 'e2e4', depth: 10 },
        { evaluation: -20, bestMove: 'd2d4', depth: 10 }
      ])

      // First call
      await filter.filterPuzzle(testPuzzle)
      const firstCallCount = mockStockfishEngine.uci.mock.calls.length

      // Second call with same position
      await filter.filterPuzzle(testPuzzle)
      const secondCallCount = mockStockfishEngine.uci.mock.calls.length

      // Should use cache for duplicate positions
      expect(secondCallCount).toBeLessThan(firstCallCount * 2)
    })
  })
})

// Helper function to setup mock Stockfish analysis
function setupMockAnalysis(mockEngine: any, analyses: Array<{evaluation: number, bestMove: string, depth: number}>) {
  let analysisIndex = 0
  
  mockEngine.listen = null
  mockEngine.uci = vi.fn((command: string) => {
    // Mock MultiPV setting changes (no-op but needs to be handled)
    if (command.startsWith('setoption name MultiPV')) {
      return
    }
    
    if (command.startsWith('go depth')) {
      // Simulate analysis completion
      setTimeout(() => {
        if (mockEngine.listen && analysisIndex < analyses.length) {
          const analysis = analyses[analysisIndex++]
          
          // Send info line with evaluation
          if (analysis.evaluation >= 0) {
            mockEngine.listen(`info depth ${analysis.depth} score cp ${analysis.evaluation} pv ${analysis.bestMove}`)
          } else if (Math.abs(analysis.evaluation) >= 1000) {
            // Mate score
            const mateIn = analysis.evaluation > 0 ? 1 : -1
            mockEngine.listen(`info depth ${analysis.depth} score mate ${mateIn} pv ${analysis.bestMove}`)
          } else {
            mockEngine.listen(`info depth ${analysis.depth} score cp ${analysis.evaluation} pv ${analysis.bestMove}`)
          }
          
          // Send bestmove
          mockEngine.listen(`bestmove ${analysis.bestMove}`)
        }
      }, 10) // Small delay to simulate analysis
    }
  })
}