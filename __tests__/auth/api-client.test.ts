/**
 * Tests for authenticated API client
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { apiRequest, apiGet, apiPost, refreshAuthToken } from '@/lib/auth/api-client'
import * as tokens from '@/lib/auth/tokens'

// Mock the tokens module
vi.mock('@/lib/auth/tokens', () => ({
  getAuthToken: vi.fn(),
  getSessionToken: vi.fn(),
  updateAuthToken: vi.fn(),
  clearTokens: vi.fn(),
}))

// Mock fetch
global.fetch = vi.fn()

const mockTokens = vi.mocked(tokens)
const mockFetch = vi.mocked(fetch)

describe('API Client', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('apiRequest', () => {
    it('should make request without auth when skipAuth is true', async () => {
      const mockResponse = new Response('{"success": true}', { status: 200 })
      mockFetch.mockResolvedValue(mockResponse)

      await apiRequest('/test', {}, true)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/test'),
        expect.objectContaining({
          headers: expect.not.objectContaining({
            Authorization: expect.any(String)
          })
        })
      )
    })

    it('should add auth header when auth token exists', async () => {
      const mockResponse = new Response('{"success": true}', { status: 200 })
      mockFetch.mockResolvedValue(mockResponse)
      mockTokens.getAuthToken.mockReturnValue('test-token')

      await apiRequest('/test')

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/test'),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'Bearer test-token'
          })
        })
      )
    })

    it('should handle 401 error and attempt token refresh', async () => {
      const unauthorizedResponse = new Response('Unauthorized', { status: 401 })
      const successResponse = new Response('{"success": true}', { status: 200 })
      const refreshResponse = new Response('{"token": "new-token"}', { status: 200 })

      mockFetch
        .mockResolvedValueOnce(unauthorizedResponse) // First request fails with 401
        .mockResolvedValueOnce(refreshResponse)      // Refresh request succeeds
        .mockResolvedValueOnce(successResponse)      // Retry request succeeds

      mockTokens.getSessionToken.mockReturnValue('session-token')
      mockTokens.getAuthToken.mockReturnValue('old-token')

      const result = await apiRequest('/test')

      expect(result.status).toBe(200)
      expect(mockTokens.updateAuthToken).toHaveBeenCalledWith('new-token', 60)
    })
  })

  describe('refreshAuthToken', () => {
    it('should refresh token successfully', async () => {
      const mockResponse = new Response('{"token": "new-token"}', { status: 200 })
      mockFetch.mockResolvedValue(mockResponse)
      mockTokens.getSessionToken.mockReturnValue('session-token')

      const result = await refreshAuthToken()

      expect(result).toBe(true)
      expect(mockTokens.updateAuthToken).toHaveBeenCalledWith('new-token', 60)
    })

    it('should return false when no session token exists', async () => {
      mockTokens.getSessionToken.mockReturnValue(null)

      const result = await refreshAuthToken()

      expect(result).toBe(false)
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('should return false when refresh request fails', async () => {
      const mockResponse = new Response('Unauthorized', { status: 401 })
      mockFetch.mockResolvedValue(mockResponse)
      mockTokens.getSessionToken.mockReturnValue('session-token')

      const result = await refreshAuthToken()

      expect(result).toBe(false)
      expect(mockTokens.updateAuthToken).not.toHaveBeenCalled()
    })
  })

  describe('convenience methods', () => {
    beforeEach(() => {
      const mockResponse = new Response('{"success": true}', { status: 200 })
      mockFetch.mockResolvedValue(mockResponse)
    })

    it('should make GET request', async () => {
      await apiGet('/test')

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/test'),
        expect.objectContaining({
          method: 'GET'
        })
      )
    })

    it('should make POST request with data', async () => {
      const data = { test: 'data' }
      await apiPost('/test', data)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/test'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(data)
        })
      )
    })
  })
})
