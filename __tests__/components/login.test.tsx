/**
 * Tests for login component
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import LoginPage from '@/app/login/page'
import { AuthProvider } from '@/components/auth/auth-provider'

// Mock the auth context
const mockLogin = vi.fn()
const mockClearError = vi.fn()
let mockAuthState = {
  login: mockLogin,
  isLoading: false,
  error: null,
  clearError: mockClearError,
  isAuthenticated: false,
}

vi.mock('@/components/auth/auth-provider', () => ({
  useAuthContext: () => mockAuthState,
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}))

// Mock Next.js router
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock token functions
let mockHasValidAuth = false
vi.mock('@/lib/auth/tokens', () => ({
  hasValidAuth: vi.fn(() => mockHasValidAuth),
}))

describe('LoginPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock auth state
    mockAuthState = {
      login: mockLogin,
      isLoading: false,
      error: null,
      clearError: mockClearError,
      isAuthenticated: false,
    }
    mockHasValidAuth = false
  })

  const renderLoginPage = () => {
    return render(<LoginPage />)
  }

  it('should render login form', () => {
    renderLoginPage()

    expect(screen.getByText('Chessticize')).toBeInTheDocument()
    expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
    expect(screen.getByLabelText('Email')).toBeInTheDocument()
    expect(screen.getByLabelText('Password')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Sign in' })).toBeInTheDocument()
  })

  it('should handle form submission with valid data', async () => {
    const user = userEvent.setup()
    renderLoginPage()

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Password')
    const submitButton = screen.getByRole('button', { name: 'Sign in' })

    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    await user.click(submitButton)

    expect(mockClearError).toHaveBeenCalled()
    expect(mockLogin).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123',
    })
  })

  it('should not submit form with empty fields', async () => {
    const user = userEvent.setup()
    renderLoginPage()

    const submitButton = screen.getByRole('button', { name: 'Sign in' })
    await user.click(submitButton)

    expect(mockLogin).not.toHaveBeenCalled()
  })

  it('should display error message', () => {
    // Update mock auth state to include error
    mockAuthState.error = 'Invalid credentials'

    renderLoginPage()

    expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
  })

  it('should show loading state when auth is loading', () => {
    mockAuthState.isLoading = true

    renderLoginPage()

    expect(screen.getByText(/signing you in.../i)).toBeInTheDocument()
    expect(screen.queryByLabelText(/email/i)).not.toBeInTheDocument()
  })

  it('should show loading when tokens exist but not yet authenticated', () => {
    mockHasValidAuth = true
    mockAuthState.isAuthenticated = false
    mockAuthState.isLoading = false

    renderLoginPage()

    expect(screen.getByText(/signing you in.../i)).toBeInTheDocument()
    expect(screen.queryByLabelText(/email/i)).not.toBeInTheDocument()
  })

  it('should redirect when authenticated and show loading during redirect', () => {
    mockAuthState.isAuthenticated = true
    mockAuthState.isLoading = false

    renderLoginPage()

    expect(mockPush).toHaveBeenCalledWith('/dashboard')
    expect(screen.getByText('Redirecting to dashboard...')).toBeInTheDocument()
  })

  it('should have link to register page', () => {
    renderLoginPage()

    const registerLink = screen.getByRole('link', { name: 'Sign up' })
    expect(registerLink).toHaveAttribute('href', '/register')
  })
})
