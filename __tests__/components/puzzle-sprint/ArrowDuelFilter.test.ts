import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ArrowDuelFilter, type SprintPuzzle, type ArrowDuelPuzzle } from '@/lib/arrow-duel-filter'

// Mock Stockfish engine
const mockStockfishEngine = {
  uci: vi.fn(),
  listen: vi.fn()
}

// Mock the global Stockfish engine getter
vi.mock('@/hooks/useStockfish', () => ({
  getGlobalStockfishEngine: vi.fn().mockResolvedValue(mockStockfishEngine)
}))

describe('ArrowDuelFilter', () => {
  let filter: ArrowDuelFilter
  
  beforeEach(() => {
    filter = new ArrowDuelFilter()
    vi.clearAllMocks()
  })

  afterEach(() => {
    filter.clearCache()
  })

  const mockPuzzle: SprintPuzzle = {
    puzzle_id: 'test-puzzle-1',
    fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
    solution_moves: ['e2e4'],
    rating: 1500,
    themes: ['opening'],
    sequence_in_sprint: 1
  }

  describe('filterPuzzle', () => {
    it('should return null for puzzles with insufficient evaluation difference', async () => {
      // Mock Stockfish to return similar evaluations
      let callCount = 0
      mockStockfishEngine.listen = vi.fn((line: string) => {
        if (line.startsWith('info')) {
          // First call: initial position analysis
          if (callCount === 0) {
            mockStockfishEngine.listen('info depth 15 score cp 25 pv e2e4')
            callCount++
          }
          // Second call: after puzzle move analysis  
          else if (callCount === 1) {
            mockStockfishEngine.listen('info depth 15 score cp -20 pv e7e5')
            callCount++
          }
        } else if (line.startsWith('bestmove')) {
          if (callCount === 1) {
            mockStockfishEngine.listen('bestmove e2e4')
          } else if (callCount === 2) {
            mockStockfishEngine.listen('bestmove e7e5')
          }
        }
      })

      const result = await filter.filterPuzzle(mockPuzzle)
      expect(result).toBeNull()
    })

    it('should return ArrowDuelPuzzle for puzzles with sufficient evaluation difference', async () => {
      // Mock Stockfish to return significantly different evaluations
      let callCount = 0
      mockStockfishEngine.listen = vi.fn((line: string) => {
        if (line.startsWith('info')) {
          // First call: initial position analysis (good move)
          if (callCount === 0) {
            mockStockfishEngine.listen('info depth 15 score cp 300 pv d2d4')
            callCount++
          }
          // Second call: after puzzle move analysis (bad move)
          else if (callCount === 1) {
            mockStockfishEngine.listen('info depth 15 score cp 50 pv e7e5')
            callCount++
          }
        } else if (line.startsWith('bestmove')) {
          if (callCount === 1) {
            mockStockfishEngine.listen('bestmove d2d4')
          } else if (callCount === 2) {
            mockStockfishEngine.listen('bestmove e7e5')
          }
        }
      })

      const result = await filter.filterPuzzle(mockPuzzle)
      
      expect(result).not.toBeNull()
      expect(result).toMatchObject({
        puzzle_id: 'test-puzzle-1',
        bestMove: 'd2d4',
        blunderMove: 'e2e4',
        candidateMoves: ['e2e4', 'd2d4']
      })
      expect(Math.abs(result!.evaluationDiff)).toBeGreaterThanOrEqual(250)
    })

    it('should handle illegal moves gracefully', async () => {
      const invalidPuzzle: SprintPuzzle = {
        ...mockPuzzle,
        solution_moves: ['invalid_move']
      }

      const result = await filter.filterPuzzle(invalidPuzzle)
      expect(result).toBeNull()
    })

    it('should handle empty solution moves', async () => {
      const emptyPuzzle: SprintPuzzle = {
        ...mockPuzzle,
        solution_moves: []
      }

      const result = await filter.filterPuzzle(emptyPuzzle)
      expect(result).toBeNull()
    })
  })

  describe('filterPuzzles', () => {
    it('should filter multiple puzzles and return only valid candidates', async () => {
      const puzzles: SprintPuzzle[] = [
        mockPuzzle,
        { ...mockPuzzle, puzzle_id: 'test-puzzle-2' },
        { ...mockPuzzle, puzzle_id: 'test-puzzle-3' }
      ]

      // Mock to return one valid candidate
      let callCount = 0
      mockStockfishEngine.listen = vi.fn((line: string) => {
        if (line.startsWith('info')) {
          // Alternate between good and bad evaluation differences
          const isGoodCandidate = Math.floor(callCount / 2) === 1
          if (callCount % 2 === 0) {
            // Initial position
            mockStockfishEngine.listen(`info depth 15 score cp ${isGoodCandidate ? 300 : 25} pv d2d4`)
          } else {
            // After puzzle move
            mockStockfishEngine.listen(`info depth 15 score cp ${isGoodCandidate ? 50 : 20} pv e7e5`)
          }
          callCount++
        } else if (line.startsWith('bestmove')) {
          mockStockfishEngine.listen('bestmove d2d4')
        }
      })

      const results = await filter.filterPuzzles(puzzles)
      
      expect(results).toHaveLength(1)
      expect(results[0].puzzle_id).toBe('test-puzzle-2')
    })

    it('should log filter statistics', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      const puzzles: SprintPuzzle[] = [mockPuzzle]
      
      // Mock to return no valid candidates
      mockStockfishEngine.listen = vi.fn((line: string) => {
        if (line.startsWith('info')) {
          mockStockfishEngine.listen('info depth 15 score cp 25 pv e2e4')
        } else if (line.startsWith('bestmove')) {
          mockStockfishEngine.listen('bestmove e2e4')
        }
      })

      await filter.filterPuzzles(puzzles)
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Arrow Duel Filter: Processing'),
        expect.any(String)
      )
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Arrow Duel Filter Results:'),
        expect.objectContaining({
          totalPuzzles: 1,
          filteredPuzzles: 0,
          filterRate: expect.stringContaining('%')
        })
      )
      
      consoleSpy.mockRestore()
    })
  })

  describe('cache functionality', () => {
    it('should cache evaluation results', async () => {
      const fen = 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'
      
      // First call should trigger analysis
      mockStockfishEngine.listen = vi.fn((line: string) => {
        if (line.startsWith('info')) {
          mockStockfishEngine.listen('info depth 15 score cp 25 pv e2e4')
        } else if (line.startsWith('bestmove')) {
          mockStockfishEngine.listen('bestmove e2e4')
        }
      })

      await filter.filterPuzzle(mockPuzzle)
      const firstCallCount = mockStockfishEngine.uci.mock.calls.length

      // Second call with same position should use cache
      await filter.filterPuzzle(mockPuzzle)
      const secondCallCount = mockStockfishEngine.uci.mock.calls.length

      // Should not have made additional UCI calls for cached position
      expect(secondCallCount).toBe(firstCallCount)
    })

    it('should clear cache when requested', () => {
      const stats = filter.getCacheStats()
      expect(stats.size).toBe(0)
      
      filter.clearCache()
      
      const statsAfterClear = filter.getCacheStats()
      expect(statsAfterClear.size).toBe(0)
    })
  })
})

// Integration test for Arrow Duel end-to-end flow
describe('Arrow Duel Integration', () => {
  let filter: ArrowDuelFilter

  beforeEach(() => {
    filter = new ArrowDuelFilter()
    vi.clearAllMocks()
  })

  afterEach(() => {
    filter.clearCache()
  })

  it('should handle complete Arrow Duel puzzle flow', async () => {
    const puzzles: SprintPuzzle[] = [
      {
        puzzle_id: 'integration-test-1',
        fen: 'r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 1',
        solution_moves: ['Bxf7+'], // This should be the "blunder" move
        rating: 1600,
        themes: ['fork', 'sacrifice'],
        sequence_in_sprint: 1
      }
    ]

    // Mock Stockfish to find a better move than the puzzle solution
    let analysisStep = 0
    mockStockfishEngine.listen = vi.fn((line: string) => {
      if (line.startsWith('info')) {
        if (analysisStep === 0) {
          // Initial position: Ng5 is better than Bxf7+
          mockStockfishEngine.listen('info depth 15 score cp 150 pv Ng5')
          analysisStep++
        } else if (analysisStep === 1) {
          // After Bxf7+: position is worse
          mockStockfishEngine.listen('info depth 15 score cp -200 pv Kxf7')
          analysisStep++
        }
      } else if (line.startsWith('bestmove')) {
        if (analysisStep === 1) {
          mockStockfishEngine.listen('bestmove Ng5')
        } else if (analysisStep === 2) {
          mockStockfishEngine.listen('bestmove Kxf7')
        }
      }
    })

    const results = await filter.filterPuzzles(puzzles)

    expect(results).toHaveLength(1)

    const arrowDuelPuzzle = results[0] as ArrowDuelPuzzle
    expect(arrowDuelPuzzle.candidateMoves).toEqual(['Bxf7+', 'Ng5'])
    expect(arrowDuelPuzzle.bestMove).toBe('Ng5')
    expect(arrowDuelPuzzle.blunderMove).toBe('Bxf7+')
    expect(arrowDuelPuzzle.evaluationDiff).toBeGreaterThanOrEqual(250)
  })

  it('should handle filter failure gracefully', async () => {
    const puzzles: SprintPuzzle[] = [
      {
        puzzle_id: 'no-good-candidates',
        fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
        solution_moves: ['e2e4'],
        rating: 1200,
        themes: ['opening'],
        sequence_in_sprint: 1
      }
    ]

    // Mock Stockfish to return similar evaluations (no good Arrow Duel candidate)
    mockStockfishEngine.listen = vi.fn((line: string) => {
      if (line.startsWith('info')) {
        mockStockfishEngine.listen('info depth 15 score cp 25 pv e2e4')
      } else if (line.startsWith('bestmove')) {
        mockStockfishEngine.listen('bestmove e2e4')
      }
    })

    const results = await filter.filterPuzzles(puzzles)
    expect(results).toHaveLength(0)
  })
})
