import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { SprintSession } from '@/components/puzzle-sprint/SprintSession'
import { getArrowDuelEloType } from '@/lib/sprint-config'

// Mock the sprint API hooks
const mockStartSprint = vi.fn()
const mockGetNextPuzzles = vi.fn()
const mockSubmitResults = vi.fn()
const mockEndSprint = vi.fn()

vi.mock('@/hooks/useSprintApi', () => ({
  useSprintManager: () => ({
    startSprint: mockStartSprint,
    getNextPuzzles: mockGetNextPuzzles,
    submitResults: mockSubmitResults,
    endSprint: mockEndSprint,
    isLoading: false,
    error: null
  })
}))

// Mock the Arrow Duel filter
const mockFilterPuzzles = vi.fn()
vi.mock('@/lib/arrow-duel-filter', () => ({
  arrowDuelFilter: {
    filterPuzzles: mockFilterPuzzles
  },
  ArrowDuelFilter: vi.fn().mockImplementation(() => ({
    filterPuzzles: mockFilterPuzzles
  }))
}))

// Mock ChessBoard component
vi.mock('@/components/puzzle-sprint/ChessBoard', () => ({
  ChessBoard: ({ mode, candidateMoves, onMoveChosen, onPuzzleComplete }: any) => (
    <div data-testid="chess-board">
      <div data-testid="mode">{mode}</div>
      <div data-testid="candidate-moves">{JSON.stringify(candidateMoves)}</div>
      {mode === 'arrowduel' && candidateMoves && (
        <div>
          <button 
            data-testid="choose-blunder"
            onClick={() => onMoveChosen(candidateMoves[0], false)}
          >
            Choose Blunder
          </button>
          <button 
            data-testid="choose-correct"
            onClick={() => onMoveChosen(candidateMoves[1], true)}
          >
            Choose Correct
          </button>
        </div>
      )}
      {mode === 'puzzle' && (
        <div>
          <button 
            data-testid="solve-puzzle"
            onClick={() => onPuzzleComplete(true, ['e2e4'])}
          >
            Solve Puzzle
          </button>
        </div>
      )}
    </div>
  )
}))

// Mock other components
vi.mock('@/components/puzzle-sprint/SprintTimer', () => ({
  SprintTimer: () => <div data-testid="sprint-timer">Timer</div>
}))

vi.mock('@/components/puzzle-sprint/PuzzleLoader', () => ({
  PuzzleLoader: () => <div data-testid="puzzle-loader">Loading...</div>
}))

describe('Arrow Duel Sprint Integration', () => {
  const mockOnExit = vi.fn()
  const mockOnComplete = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Default successful sprint start
    mockStartSprint.mockResolvedValue({
      session_id: 'arrow-duel-session-123',
      time_limit_seconds: 300,
      target_puzzles: 10,
      max_mistakes: 2,
      user_elo: { rating: 1500, rating_deviation: 50, is_provisional: false }
    })

    // Default puzzle response
    mockGetNextPuzzles.mockResolvedValue({
      puzzles: [
        {
          puzzle_id: 'arrow-duel-puzzle-1',
          fen: 'r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 1',
          solution_moves: ['Bxf7+'],
          rating: 1600,
          themes: ['fork'],
          sequence_in_sprint: 1,
          bestMove: 'Ng5',
          blunderMove: 'Bxf7+',
          evaluationDiff: 350,
          candidateMoves: ['Bxf7+', 'Ng5']
        }
      ]
    })

    // Default filter response
    mockFilterPuzzles.mockResolvedValue([
      {
        puzzle_id: 'arrow-duel-puzzle-1',
        fen: 'r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 1',
        solution_moves: ['Bxf7+'],
        rating: 1600,
        themes: ['fork'],
        sequence_in_sprint: 1,
        bestMove: 'Ng5',
        blunderMove: 'Bxf7+',
        evaluationDiff: 350,
        candidateMoves: ['Bxf7+', 'Ng5']
      }
    ])

    mockSubmitResults.mockResolvedValue({ processed_count: 1 })
  })

  it('should initialize Arrow Duel sprint correctly', async () => {
    render(
      <SprintSession
        eloType={getArrowDuelEloType()}
        onExit={mockOnExit}
        onComplete={mockOnComplete}
      />
    )

    await waitFor(() => {
      expect(mockStartSprint).toHaveBeenCalledWith(getArrowDuelEloType(), expect.any(AbortSignal))
    })

    await waitFor(() => {
      expect(mockGetNextPuzzles).toHaveBeenCalledWith(
        'arrow-duel-session-123',
        expect.any(Number),
        'arrowduel',
        expect.any(AbortSignal)
      )
    })
  })

  it('should display Arrow Duel mode in ChessBoard', async () => {
    render(
      <SprintSession
        eloType={getArrowDuelEloType()}
        onExit={mockOnExit}
        onComplete={mockOnComplete}
      />
    )

    await waitFor(() => {
      expect(screen.getByTestId('mode')).toHaveTextContent('arrowduel')
    })

    await waitFor(() => {
      const candidateMoves = JSON.parse(screen.getByTestId('candidate-moves').textContent || '[]')
      expect(candidateMoves).toEqual(['Bxf7+', 'Ng5'])
    })
  })

  it('should handle correct Arrow Duel move selection', async () => {
    render(
      <SprintSession
        eloType={getArrowDuelEloType()}
        onExit={mockOnExit}
        onComplete={mockOnComplete}
      />
    )

    await waitFor(() => {
      expect(screen.getByTestId('choose-correct')).toBeInTheDocument()
    })

    fireEvent.click(screen.getByTestId('choose-correct'))

    await waitFor(() => {
      expect(mockSubmitResults).toHaveBeenCalledWith(
        'arrow-duel-session-123',
        expect.arrayContaining([
          expect.objectContaining({
            puzzle_id: 'arrow-duel-puzzle-1',
            was_correct: true,
            attempt_type: 'arrow_duel',
            candidate_moves: ['Bxf7+', 'Ng5'],
            chosen_move: 'Ng5',
            user_moves: ['Ng5']
          })
        ]),
        true
      )
    })
  })

  it('should handle incorrect Arrow Duel move selection', async () => {
    render(
      <SprintSession
        eloType={getArrowDuelEloType()}
        onExit={mockOnExit}
        onComplete={mockOnComplete}
      />
    )

    await waitFor(() => {
      expect(screen.getByTestId('choose-blunder')).toBeInTheDocument()
    })

    fireEvent.click(screen.getByTestId('choose-blunder'))

    await waitFor(() => {
      expect(mockSubmitResults).toHaveBeenCalledWith(
        'arrow-duel-session-123',
        expect.arrayContaining([
          expect.objectContaining({
            puzzle_id: 'arrow-duel-puzzle-1',
            was_correct: false,
            attempt_type: 'arrow_duel',
            candidate_moves: ['Bxf7+', 'Ng5'],
            chosen_move: 'Bxf7+',
            user_moves: ['Bxf7+']
          })
        ]),
        true
      )
    })
  })

  it('should handle puzzle filtering failure gracefully', async () => {
    // Mock filter to return no valid puzzles
    mockFilterPuzzles.mockResolvedValue([])
    
    // Mock getNextPuzzles to return regular puzzles
    mockGetNextPuzzles.mockResolvedValue({
      puzzles: [
        {
          puzzle_id: 'regular-puzzle-1',
          fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
          solution_moves: ['e2e4'],
          rating: 1200,
          themes: ['opening'],
          sequence_in_sprint: 1
        }
      ]
    })

    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

    render(
      <SprintSession
        eloType={getArrowDuelEloType()}
        onExit={mockOnExit}
        onComplete={mockOnComplete}
      />
    )

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Arrow Duel: No puzzles passed filter')
      )
    })

    consoleSpy.mockRestore()
  })

  it('should retry puzzle filtering when insufficient puzzles are found', async () => {
    // First call returns no puzzles, second call returns valid puzzles
    mockFilterPuzzles
      .mockResolvedValueOnce([])
      .mockResolvedValueOnce([
        {
          puzzle_id: 'arrow-duel-puzzle-retry',
          fen: 'r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 1',
          solution_moves: ['Bxf7+'],
          rating: 1600,
          themes: ['fork'],
          sequence_in_sprint: 1,
          bestMove: 'Ng5',
          blunderMove: 'Bxf7+',
          evaluationDiff: 350,
          candidateMoves: ['Bxf7+', 'Ng5']
        }
      ])

    render(
      <SprintSession
        eloType={getArrowDuelEloType()}
        onExit={mockOnExit}
        onComplete={mockOnComplete}
      />
    )

    await waitFor(() => {
      expect(mockGetNextPuzzles).toHaveBeenCalledTimes(2) // Initial call + retry
    })

    await waitFor(() => {
      expect(screen.getByTestId('mode')).toHaveTextContent('arrowduel')
    })
  })

  it('should track Arrow Duel specific metrics', async () => {
    render(
      <SprintSession
        eloType={getArrowDuelEloType()}
        onExit={mockOnExit}
        onComplete={mockOnComplete}
      />
    )

    await waitFor(() => {
      expect(screen.getByTestId('choose-correct')).toBeInTheDocument()
    })

    // Make a correct choice
    fireEvent.click(screen.getByTestId('choose-correct'))

    // Verify that the sprint tracks success/mistake counts correctly
    await waitFor(() => {
      expect(mockSubmitResults).toHaveBeenCalledWith(
        expect.any(String),
        expect.arrayContaining([
          expect.objectContaining({
            was_correct: true,
            attempt_type: 'arrow_duel'
          })
        ]),
        true
      )
    })
  })
})
