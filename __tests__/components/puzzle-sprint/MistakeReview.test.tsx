import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { MistakeReview } from '@/components/puzzle-sprint/MistakeReview'

// Mock the hooks
vi.mock('@/hooks/useSprintApi', () => ({
  useSprintPuzzles: () => ({
    getSprintPuzzles: vi.fn().mockResolvedValue({
      puzzles: [
        {
          puzzle_id: 'test-puzzle-1',
          sequence_in_sprint: 1,
          fen: 'rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1',
          solution_moves: ['Nf6', 'Nc3'],
          themes: ['fork'],
          rating: 1200,
          attempt_status: 'failed',
          user_moves: ['e4'],
          was_correct: false,
          time_taken_ms: 5000,
          attempted_at: '2024-01-01T12:00:00Z'
        }
      ]
    }),
    isLoading: false,
    error: null
  })
}))

// Mock the ChessBoard component
vi.mock('@/components/puzzle-sprint/ChessBoard', () => ({
  ChessBoard: React.forwardRef(({ fen, onPositionChange, ...props }: any, ref: any) => {
    React.useImperativeHandle(ref, () => ({
      getCurrentFen: () => fen,
      makeMove: vi.fn(),
      resetPosition: vi.fn(),
      setPosition: vi.fn()
    }))
    
    return (
      <div data-testid="chess-board" data-fen={fen}>
        Mocked ChessBoard
      </div>
    )
  })
}))

// Mock the InteractiveAnalysis component
vi.mock('@/components/puzzle-sprint/InteractiveAnalysis', () => ({
  InteractiveAnalysis: ({ 
    fen, 
    onMoveBack, 
    onResetToPuzzleStart, 
    canMoveBack, 
    canReset,
    ...props 
  }: any) => (
    <div data-testid="interactive-analysis" data-fen={fen}>
      <button 
        onClick={onMoveBack} 
        disabled={!canMoveBack}
        data-testid="move-back-btn"
      >
        Move Back
      </button>
      <button 
        onClick={onResetToPuzzleStart} 
        disabled={!canReset}
        data-testid="reset-puzzle-btn"
      >
        Reset to Puzzle Start
      </button>
      Mocked InteractiveAnalysis
    </div>
  )
}))

// Mock chess.js
vi.mock('chess.js', () => ({
  Chess: vi.fn().mockImplementation((fen) => ({
    fen: () => fen || 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
    move: vi.fn().mockReturnValue({
      san: 'Nf6',
      from: 'g8',
      to: 'f6'
    })
  }))
}))

describe('MistakeReview', () => {
  const mockProps = {
    sessionId: 'test-session-123',
    onClose: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render the mistake review component', async () => {
    render(<MistakeReview {...mockProps} />)

    await waitFor(() => {
      expect(screen.getByText('Review Mistakes')).toBeInTheDocument()
    })
  })

  it('should show analysis mode when analyze button is clicked', async () => {
    render(<MistakeReview {...mockProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Analyze')).toBeInTheDocument()
    })
    
    const analyzeButton = screen.getByText('Analyze')
    fireEvent.click(analyzeButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('interactive-analysis')).toBeInTheDocument()
    })
  })

  it('should enable move back button when move history exists', async () => {
    render(<MistakeReview {...mockProps} />)
    
    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Analyze')).toBeInTheDocument()
    })
    
    // Enter analysis mode
    const analyzeButton = screen.getByText('Analyze')
    fireEvent.click(analyzeButton)
    
    await waitFor(() => {
      const moveBackBtn = screen.getByTestId('move-back-btn')
      // Initially should be disabled (no move history)
      expect(moveBackBtn).toBeDisabled()
    })
  })

  it('should enable reset button when solution moves exist', async () => {
    render(<MistakeReview {...mockProps} />)
    
    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Analyze')).toBeInTheDocument()
    })
    
    // Enter analysis mode
    const analyzeButton = screen.getByText('Analyze')
    fireEvent.click(analyzeButton)
    
    await waitFor(() => {
      const resetBtn = screen.getByTestId('reset-puzzle-btn')
      // Should be enabled since mock data has solution moves
      expect(resetBtn).not.toBeDisabled()
    })
  })

  it('should handle move back functionality', async () => {
    render(<MistakeReview {...mockProps} />)
    
    // Wait for component to load and enter analysis mode
    await waitFor(() => {
      expect(screen.getByText('Analyze')).toBeInTheDocument()
    })
    
    const analyzeButton = screen.getByText('Analyze')
    fireEvent.click(analyzeButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('interactive-analysis')).toBeInTheDocument()
    })
    
    // The move back button should exist (even if disabled initially)
    const moveBackBtn = screen.getByTestId('move-back-btn')
    expect(moveBackBtn).toBeInTheDocument()
  })

  it('should handle reset to puzzle start functionality', async () => {
    render(<MistakeReview {...mockProps} />)
    
    // Wait for component to load and enter analysis mode
    await waitFor(() => {
      expect(screen.getByText('Analyze')).toBeInTheDocument()
    })
    
    const analyzeButton = screen.getByText('Analyze')
    fireEvent.click(analyzeButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('interactive-analysis')).toBeInTheDocument()
    })
    
    // Click the reset button
    const resetBtn = screen.getByTestId('reset-puzzle-btn')
    fireEvent.click(resetBtn)
    
    // The button should have been clicked (functionality is mocked)
    expect(resetBtn).toBeInTheDocument()
  })

  it('should clear move history when exiting analysis mode', async () => {
    render(<MistakeReview {...mockProps} />)
    
    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Analyze')).toBeInTheDocument()
    })
    
    // Enter analysis mode
    const analyzeButton = screen.getByText('Analyze')
    fireEvent.click(analyzeButton)
    
    await waitFor(() => {
      expect(screen.getByText('Exit Analysis')).toBeInTheDocument()
    })
    
    // Exit analysis mode
    const exitButton = screen.getByText('Exit Analysis')
    fireEvent.click(exitButton)
    
    await waitFor(() => {
      expect(screen.getByText('Analyze')).toBeInTheDocument()
    })
  })
})
