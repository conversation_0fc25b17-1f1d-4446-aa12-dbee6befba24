/**
 * Tests for registration component
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import RegisterPage from '@/app/register/page'
import { AuthProvider } from '@/components/auth/auth-provider'

// Mock the auth context
const mockRegister = vi.fn()
const mockClearError = vi.fn()
let mockAuthState = {
  register: mockRegister,
  isLoading: false,
  error: null,
  clearError: mockClearError,
  isAuthenticated: false,
}

vi.mock('@/components/auth/auth-provider', () => ({
  useAuthContext: () => mockAuthState,
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}))

// Mock Next.js router
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock token functions
let mockHasValidAuth = false
vi.mock('@/lib/auth/tokens', () => ({
  hasValidAuth: vi.fn(() => mockHasValidAuth),
}))

describe('RegisterPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock auth state
    mockAuthState = {
      register: mockRegister,
      isLoading: false,
      error: null,
      clearError: mockClearError,
      isAuthenticated: false,
    }
    mockHasValidAuth = false
  })

  const renderRegisterPage = () => {
    return render(<RegisterPage />)
  }

  it('should render registration form without invitation code field', () => {
    renderRegisterPage()

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument()
    
    // Should NOT have invitation code field
    expect(screen.queryByLabelText(/invitation code/i)).not.toBeInTheDocument()
  })

  it('should not call register when fields are empty', async () => {
    const user = userEvent.setup()
    renderRegisterPage()

    // Try to submit without filling any fields
    // Note: Browser validation will prevent submission, so we just verify the register function isn't called
    const submitButton = screen.getByRole('button', { name: /create account/i })

    // The form has required fields, so browser validation should prevent submission
    // We can't easily test the custom validation without mocking form submission
    expect(mockRegister).not.toHaveBeenCalled()
  })

  it('should validate password confirmation', async () => {
    const user = userEvent.setup()
    renderRegisterPage()

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/^password$/i), 'password123')
    await user.type(screen.getByLabelText(/confirm password/i), 'different123')

    const submitButton = screen.getByRole('button', { name: /create account/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument()
    })

    expect(mockRegister).not.toHaveBeenCalled()
  })

  it('should validate password length', async () => {
    const user = userEvent.setup()
    renderRegisterPage()

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/^password$/i), '123')
    await user.type(screen.getByLabelText(/confirm password/i), '123')

    const submitButton = screen.getByRole('button', { name: /create account/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/password must be at least 6 characters long/i)).toBeInTheDocument()
    })

    expect(mockRegister).not.toHaveBeenCalled()
  })

  it('should call register without invitation code on valid form submission', async () => {
    const user = userEvent.setup()
    renderRegisterPage()

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/^password$/i), 'password123')
    await user.type(screen.getByLabelText(/confirm password/i), 'password123')

    const submitButton = screen.getByRole('button', { name: /create account/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      })
    })
  })

  it('should show loading state during registration', () => {
    mockAuthState.isLoading = true
    renderRegisterPage()

    expect(screen.getByText(/checking authentication.../i)).toBeInTheDocument()
  })

  it('should show loading state when tokens exist but not authenticated', () => {
    mockHasValidAuth = true
    mockAuthState.isAuthenticated = false
    mockAuthState.isLoading = false

    renderRegisterPage()

    expect(screen.getByText(/checking authentication.../i)).toBeInTheDocument()
  })

  it('should display error messages', () => {
    mockAuthState.error = 'Registration failed'
    renderRegisterPage()

    expect(screen.getByText(/registration failed/i)).toBeInTheDocument()
  })

  it('should redirect if already authenticated and show loading during redirect', () => {
    mockAuthState.isAuthenticated = true
    renderRegisterPage()

    expect(mockPush).toHaveBeenCalledWith('/dashboard')
    expect(screen.getByText('Redirecting to dashboard...')).toBeInTheDocument()
  })
})
