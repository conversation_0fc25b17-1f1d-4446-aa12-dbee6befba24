import { renderHook, act } from '@testing-library/react'
import { useGameReviewStats } from '@/hooks/useGameReviewStats'
import { vi } from 'vitest'
import { apiPost } from '@/lib/auth/api-client'

// Mock the API client
vi.mock('@/lib/auth/api-client', () => ({
  apiPost: vi.fn()
}))

describe('useGameReviewStats', () => {
  const mockApiPost = vi.mocked(apiPost)

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useGameReviewStats())

    expect(result.current.opponentMistakesData).toBeNull()
    expect(result.current.myMistakesData).toBeNull()
    expect(result.current.summary).toBeNull()
    expect(result.current.isLoading).toBe(false)
    expect(result.current.errors).toEqual([])
    expect(typeof result.current.loadStats).toBe('function')
  })

  it('should handle loading state correctly', async () => {
    const mockResponse = {
      ok: true,
      json: vi.fn().mockResolvedValue({
        data: {
          myPuzzleStats: {
            total_count: 100,
            unique_game_count: 50,
            tag_counts: [],
            theme_counts: [],
            user_color_counts: [],
            game_move_buckets: [],
            move_length_counts: []
          }
        }
      })
    }

    mockApiPost.mockResolvedValue(mockResponse)

    const { result } = renderHook(() => useGameReviewStats())

    expect(result.current.isLoading).toBe(false)

    await act(async () => {
      await result.current.loadStats()
    })

    expect(result.current.isLoading).toBe(false)
    expect(result.current.opponentMistakesData).toBeTruthy()
  })

  it('should handle errors correctly', async () => {
    const mockError = new Error('API Error')
    mockApiPost.mockRejectedValue(mockError)

    const { result } = renderHook(() => useGameReviewStats())

    await act(async () => {
      await result.current.loadStats()
    })

    expect(result.current.errors.length).toBeGreaterThan(0)
    expect(result.current.errors.some(error => error.includes('API Error'))).toBe(true)
    expect(result.current.isLoading).toBe(false)
  })
})
