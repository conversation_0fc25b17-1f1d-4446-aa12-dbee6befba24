import { describe, it, expect, beforeAll } from 'vitest'
import { apiPost } from '@/lib/auth/api-client'
import { API_CONFIG } from '@/lib/config'

// Integration tests for GraphQL queries against dev server
// These tests require a running dev server with valid auth tokens
describe('GraphQL Integration Tests', () => {
  let authToken: string | null = null

  beforeAll(async () => {
    // Try to get auth token from localStorage (if running in browser context)
    // In a real integration test, you'd set up proper test credentials
    if (typeof window !== 'undefined') {
      authToken = localStorage.getItem('access_token')
    }
    
    // Skip tests if no auth token available
    if (!authToken) {
      console.warn('No auth token available - skipping GraphQL integration tests')
    }
  })

  const makeGraphQLRequest = async (query: string, variables: any = {}) => {
    if (!authToken) {
      throw new Error('No auth token available for testing')
    }

    const response = await apiPost(API_CONFIG.ENDPOINTS.GRAPHQL, {
      query,
      variables
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`GraphQL request failed: ${response.status} ${errorText}`)
    }

    const data = await response.json()
    
    if (data.errors) {
      throw new Error(`GraphQL errors: ${data.errors.map((e: any) => e.message).join(', ')}`)
    }

    return data.data
  }

  it('should fetch puzzle statistics successfully', async () => {
    if (!authToken) {
      console.warn('Skipping test - no auth token')
      return
    }

    const query = `
      query MyPuzzleStats {
        myPuzzleStats {
          tag_counts {
            tag
            count
          }
          theme_counts {
            theme
            count
          }
          user_color_counts {
            color
            count
          }
          game_move_buckets {
            name
            min_move
            max_move
            count
          }
          move_length_counts {
            length
            count
          }
          total_count
          unique_game_count
        }
      }
    `

    const data = await makeGraphQLRequest(query)
    
    expect(data).toBeDefined()
    expect(data.myPuzzleStats).toBeDefined()
    expect(typeof data.myPuzzleStats.total_count).toBe('number')
    expect(typeof data.myPuzzleStats.unique_game_count).toBe('number')
    expect(Array.isArray(data.myPuzzleStats.tag_counts)).toBe(true)
    expect(Array.isArray(data.myPuzzleStats.theme_counts)).toBe(true)
  })

  it('should fetch opponent mistakes data successfully', async () => {
    if (!authToken) {
      console.warn('Skipping test - no auth token')
      return
    }

    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days ago
    const endDate = new Date().toISOString() // today

    const query = `
      query OpponentMistakesMissedByPeriod($startDate: Time!, $endDate: Time!) {
        myPuzzles(
          filter: {
            theme: OPPONENT_MISTAKE_MISSED,
            game_start_time: $startDate,
            game_end_time: $endDate
          },
          pagination: { limit: 100 }
        ) {
          edges {
            node {
              id
              game {
                game_time
              }
            }
          }
          total_count
        }

        allOpponentMistakes: myPuzzles(
          filter: {
            theme_in: [OPPONENT_MISTAKE_CAUGHT, OPPONENT_MISTAKE_MISSED],
            game_start_time: $startDate,
            game_end_time: $endDate
          },
          pagination: { limit: 100 }
        ) {
          total_count
        }
      }
    `

    const data = await makeGraphQLRequest(query, { startDate, endDate })
    
    expect(data).toBeDefined()
    expect(data.myPuzzles).toBeDefined()
    expect(data.allOpponentMistakes).toBeDefined()
    expect(typeof data.myPuzzles.total_count).toBe('number')
    expect(typeof data.allOpponentMistakes.total_count).toBe('number')
    expect(Array.isArray(data.myPuzzles.edges)).toBe(true)
  })

  it('should fetch my mistakes data for WHITE pieces successfully', async () => {
    if (!authToken) {
      console.warn('Skipping test - no auth token')
      return
    }

    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days ago
    const endDate = new Date().toISOString() // today

    const query = `
      query MyMistakesStats($startDate: Time!, $endDate: Time!) {
        myMistakes: myPuzzles(
          filter: {
            user_color: WHITE,
            puzzle_color: BLACK,
            game_start_time: $startDate,
            game_end_time: $endDate
          },
          pagination: { limit: 100 }
        ) {
          edges {
            node {
              id
              moves
              tags
              theme
              game {
                id
                game_time
              }
            }
          }
        }
        
        myGames(
          filter: {
            start_time: $startDate
          },
          pagination: { limit: 100 }
        ) {
          total_count
          edges {
            node {
              id
              game_time
            }
          }
        }
      }
    `

    const data = await makeGraphQLRequest(query, { startDate, endDate })
    
    expect(data).toBeDefined()
    expect(data.myMistakes).toBeDefined()
    expect(data.myGames).toBeDefined()
    expect(Array.isArray(data.myMistakes.edges)).toBe(true)
    expect(typeof data.myGames.total_count).toBe('number')
  })

  it('should fetch my mistakes data for BLACK pieces successfully', async () => {
    if (!authToken) {
      console.warn('Skipping test - no auth token')
      return
    }

    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days ago
    const endDate = new Date().toISOString() // today

    const query = `
      query MyMistakesStats($startDate: Time!, $endDate: Time!) {
        myMistakes: myPuzzles(
          filter: {
            user_color: BLACK,
            puzzle_color: WHITE,
            game_start_time: $startDate,
            game_end_time: $endDate
          },
          pagination: { limit: 100 }
        ) {
          edges {
            node {
              id
              moves
              tags
              theme
              game {
                id
                game_time
              }
            }
          }
        }
        
        myGames(
          filter: {
            start_time: $startDate
          },
          pagination: { limit: 100 }
        ) {
          total_count
          edges {
            node {
              id
              game_time
            }
          }
        }
      }
    `

    const data = await makeGraphQLRequest(query, { startDate, endDate })
    
    expect(data).toBeDefined()
    expect(data.myMistakes).toBeDefined()
    expect(data.myGames).toBeDefined()
    expect(Array.isArray(data.myMistakes.edges)).toBe(true)
    expect(typeof data.myGames.total_count).toBe('number')
  })

  it('should handle GraphQL errors gracefully', async () => {
    if (!authToken) {
      console.warn('Skipping test - no auth token')
      return
    }

    const invalidQuery = `
      query InvalidQuery {
        nonExistentField {
          id
        }
      }
    `

    await expect(makeGraphQLRequest(invalidQuery)).rejects.toThrow()
  })
})
