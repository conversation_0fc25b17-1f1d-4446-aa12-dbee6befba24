/**
 * Arrow Duel Smoke Tests
 * Quick tests to catch compilation errors and basic rendering issues
 */

import React from 'react'
import { render } from '@testing-library/react'
import { vi, describe, it, expect } from 'vitest'

// Mock external dependencies
vi.mock('react-chessboard', () => ({
  Chessboard: () => <div data-testid="chessboard" />
}))

vi.mock('@/hooks/useStockfish', () => ({
  getGlobalStockfishEngine: vi.fn().mockResolvedValue({
    uci: vi.fn(),
    listen: null
  }),
  useStockfish: () => ({
    analysis: { evaluation: 0, bestMove: '', depth: 0, isAnalyzing: false, topLines: [], currentPosition: '' },
    analyzePosition: vi.fn(),
    stopAnalysis: vi.fn(),
    initEngine: vi.fn(),
    isEngineReady: true
  })
}))

describe('Arrow Duel Smoke Tests', () => {
  describe('Module Imports', () => {
    it('should import ChessBoard without errors', async () => {
      await expect(import('@/components/puzzle-sprint/ChessBoard')).resolves.toBeDefined()
    })

    it('should import ArrowDuelMoveSelector without errors', async () => {
      await expect(import('@/components/puzzle-sprint/ArrowDuelMoveSelector')).resolves.toBeDefined()
    })

    it('should import ArrowDuelFilter without errors', async () => {
      await expect(import('@/lib/arrow-duel-filter')).resolves.toBeDefined()
    })

    it('should import useStockfish without errors', async () => {
      await expect(import('@/hooks/useStockfish')).resolves.toBeDefined()
    })
  })

  describe('Component Rendering', () => {
    it('should render ChessBoard in arrow duel mode without crashing', async () => {
      const { ChessBoard } = await import('@/components/puzzle-sprint/ChessBoard')
      
      expect(() => {
        render(
          <ChessBoard
            fen="rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
            solutionMoves={['e2e4']}
            mode="arrowduel"
            candidateMoves={['d2d4', 'e2e4']}
            bestMove="e2e4"
            blunderMove="d2d4"
            onMoveChosen={vi.fn()}
          />
        )
      }).not.toThrow()
    })

    it('should render ArrowDuelMoveSelector without crashing', async () => {
      const { ArrowDuelMoveSelector } = await import('@/components/puzzle-sprint/ArrowDuelMoveSelector')
      
      expect(() => {
        render(
          <ArrowDuelMoveSelector
            candidateMoves={['d2d4', 'e2e4']}
            bestMove="e2e4"
            blunderMove="d2d4"
            onMoveChosen={vi.fn()}
          />
        )
      }).not.toThrow()
    })
  })

  describe('Type Validation', () => {
    it('should export required functions from useStockfish', async () => {
      const stockfishModule = await import('@/hooks/useStockfish')
      
      expect(stockfishModule.getGlobalStockfishEngine).toBeDefined()
      expect(stockfishModule.useStockfish).toBeDefined()
      expect(typeof stockfishModule.getGlobalStockfishEngine).toBe('function')
      expect(typeof stockfishModule.useStockfish).toBe('function')
    })

    it('should have ArrowDuelFilter class', async () => {
      const filterModule = await import('@/lib/arrow-duel-filter')
      
      expect(filterModule.ArrowDuelFilter).toBeDefined()
      expect(filterModule.arrowDuelFilter).toBeDefined()
      expect(typeof filterModule.ArrowDuelFilter).toBe('function') // Constructor
    })
  })

  describe('Configuration Validation', () => {
    it('should have valid sprint config for arrow duel', async () => {
      const sprintConfig = await import('@/lib/sprint-config')
      
      expect(sprintConfig.getArrowDuelEloType).toBeDefined()
      expect(sprintConfig.SPRINT_DISPLAY.ARROW_DUEL).toBeDefined()
      expect(sprintConfig.SPRINT_TIMINGS.ARROW_DUEL).toBeDefined()
      
      // Validate structure
      expect(sprintConfig.SPRINT_TIMINGS.ARROW_DUEL.duration).toBeTypeOf('number')
      expect(sprintConfig.SPRINT_TIMINGS.ARROW_DUEL.perPuzzle).toBeTypeOf('number')
      expect(sprintConfig.SPRINT_TIMINGS.ARROW_DUEL.eloType).toBeTypeOf('string')
    })
  })

  describe('Build Validation', () => {
    it('should have all required dependencies available', () => {
      // Test critical dependencies - these should not throw
      expect(() => require('chess.js')).not.toThrow()
      expect(() => require('react')).not.toThrow()
      expect(() => require('react-dom')).not.toThrow()
    })
  })
})