"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Puzzle, ArrowLeft, Construction } from "lucide-react"
import Link from "next/link"

export default function PuzzlesPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/game-review">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Game Review
            </Button>
          </Link>
          
          <div className="flex items-center space-x-3">
            <div className="bg-purple-100 p-3 rounded-2xl">
              <Puzzle className="h-8 w-8 text-purple-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Puzzles</h1>
              <p className="text-gray-600 mt-1">View puzzles generated from your games</p>
            </div>
          </div>
        </div>

        {/* Coming Soon Card */}
        <Card className="bg-white shadow-lg">
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-100 p-4 rounded-full">
                <Construction className="h-12 w-12 text-orange-600" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              Puzzles View Coming Soon
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              We're building a dedicated puzzles view where you'll be able to browse 
              and review all the tactical puzzles generated from your real games. 
              This will help you understand your tactical patterns and progress.
            </p>
            
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h3 className="font-semibold text-gray-900 mb-3">Planned Features:</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Browse all puzzles from your games</li>
                <li>• Filter by theme, difficulty, date</li>
                <li>• Review your puzzle solving history</li>
                <li>• Track improvement over time</li>
                <li>• Retry missed puzzles</li>
                <li>• Export puzzle collections</li>
              </ul>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link href="/puzzle-sprint">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                  Practice Puzzles Now
                </Button>
              </Link>
              <Link href="/game-review">
                <Button variant="outline">
                  Back to Game Review
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
