"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import { MobileLayout, MobileContainer, MobileCard } from "@/components/ui/mobile-layout"
import { User, Trash2, Plus, Loader2, Settings as SettingsIcon } from "lucide-react"
import { useUserContext } from "@/hooks/useUserContext"
import { useChessProfiles } from "@/hooks/useChessProfiles"
import { useChessUsernameValidation } from "@/hooks/useChessUsernameValidation"
import { ChessUsernameInput } from "@/components/ui/chess-username-input"
import { useAuthContext } from "@/components/auth/auth-provider"

export default function SettingsPage() {
  const { user, isLoading: isLoadingUser, error: userError, updateChessProfiles } = useUserContext()
  const { logout } = useAuthContext()
  const {
    isCreatingProfile,
    profileError,
    successMessage,
    createChessProfile,
    clearMessages
  } = useChessProfiles()
  
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [profileFormData, setProfileFormData] = useState({
    lichessUsername: '',
    chessComUsername: ''
  })
  const [deletingProfileId, setDeletingProfileId] = useState<string | null>(null)
  const [validatingUsernames, setValidatingUsernames] = useState(false)

  // Chess username validation
  const {
    chessComValidation,
    lichessValidation,
    validateChessComUsernameAsync,
    validateLichessUsernameAsync,
    clearValidation
  } = useChessUsernameValidation()

  const chessProfiles = user?.chess_profiles || []

  // Check which platforms already have profiles
  const hasLichessProfile = chessProfiles.some(profile => profile.platform === 'lichess.org')
  const hasChessComProfile = chessProfiles.some(profile => profile.platform === 'chess.com')

  const handleCreateProfile = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!profileFormData.lichessUsername && !profileFormData.chessComUsername) {
      return
    }

    try {
      clearMessages()

      // Validate usernames before creating profiles
      setValidatingUsernames(true)

      // Validate chess.com username if provided
      if (profileFormData.chessComUsername.trim() && !hasChessComProfile) {
        await validateChessComUsernameAsync(profileFormData.chessComUsername)
        if (chessComValidation.isValid === false) {
          throw new Error(chessComValidation.error || "Chess.com username is invalid.")
        }
      }

      // Validate lichess username if provided
      if (profileFormData.lichessUsername.trim() && !hasLichessProfile) {
        await validateLichessUsernameAsync(profileFormData.lichessUsername)
        if (lichessValidation.isValid === false) {
          throw new Error(lichessValidation.error || "Lichess username is invalid.")
        }
      }

      const createdProfiles = []

      // Create Lichess profile if username provided and no existing Lichess profile
      if (profileFormData.lichessUsername.trim() && !hasLichessProfile) {
        const lichessProfile = await createChessProfile('lichess.org', profileFormData.lichessUsername.trim())
        createdProfiles.push(lichessProfile)
      }

      // Create Chess.com profile if username provided and no existing Chess.com profile
      if (profileFormData.chessComUsername.trim() && !hasChessComProfile) {
        const chessComProfile = await createChessProfile('chess.com', profileFormData.chessComUsername.trim())
        createdProfiles.push(chessComProfile)
      }

      // Update global user state
      updateChessProfiles([...chessProfiles, ...createdProfiles])
      setShowCreateForm(false)
      setProfileFormData({ lichessUsername: '', chessComUsername: '' })

    } catch (error) {
      console.error('Error creating chess profiles:', error)
    } finally {
      setValidatingUsernames(false)
    }
  }

  const handleDeleteProfile = async (profileId: string) => {
    try {
      setDeletingProfileId(profileId)
      
      // TODO: Implement deleteChessProfile API call
      // await deleteChessProfile(profileId)
      
      // For now, just remove from local state
      const updatedProfiles = chessProfiles.filter(p => p.id !== profileId)
      updateChessProfiles(updatedProfiles)
      
    } catch (error) {
      console.error('Error deleting chess profile:', error)
    } finally {
      setDeletingProfileId(null)
    }
  }

  return (
    <MobileLayout fullHeight>
      <div className="min-h-screen bg-gray-50">
        <Navigation />

        <MobileContainer padding="lg">
          <MobileCard padding="lg" className="mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
              <div>
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2 flex items-center">
                  <SettingsIcon className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 mr-2 sm:mr-3 text-blue-600" />
                  Settings
                </h1>
                <p className="text-base sm:text-lg lg:text-xl text-gray-600">Manage your account and chess profiles</p>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  onClick={logout}
                  variant="outline"
                  className="text-red-600 border-red-300 hover:bg-red-50 hover:border-red-400 text-sm sm:text-base"
                >
                  Sign Out
                </Button>
              </div>
            </div>

          {/* Success Message */}
          {successMessage && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-green-800">{successMessage}</p>
            </div>
          )}

          {/* Error Messages */}
          {userError && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-800">User Error: {userError}</p>
            </div>
          )}

          {profileError && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-800">Profile Error: {profileError}</p>
            </div>
          )}

          {/* Loading State */}
          {isLoadingUser && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-lg text-gray-600">Loading user data...</span>
            </div>
          )}

            {/* Chess Profiles Section */}
            {!isLoadingUser && (
              <Card className="mb-6">
                <CardHeader className="pb-4">
                  <div className="flex flex-col space-y-4">
                    <div>
                      <CardTitle className="flex items-center space-x-2 text-gray-900 text-lg sm:text-xl">
                        <User className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                        <span>Chess Profiles</span>
                      </CardTitle>
                      <CardDescription className="text-gray-600 text-sm sm:text-base mt-1">
                        Connect your chess accounts to analyze your games. Usernames cannot be modified once created.
                      </CardDescription>
                    </div>
                    <Button
                      onClick={() => setShowCreateForm(true)}
                      className="bg-blue-600 hover:bg-blue-700 text-sm sm:text-base px-3 py-2 sm:px-4 sm:py-2 w-full sm:w-auto self-start"
                      disabled={isCreatingProfile || (hasLichessProfile && hasChessComProfile)}
                      title={hasLichessProfile && hasChessComProfile ? "All supported platforms already connected" : ""}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {hasLichessProfile && hasChessComProfile ? "All Platforms Connected" : "Add Profile"}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {/* Existing Chess Profiles */}
                {chessProfiles.length > 0 ? (
                  <div className="space-y-4 mb-6">
                    {chessProfiles.map((profile) => (
                      <div key={profile.id} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-gray-900 text-base sm:text-lg">{profile.platform}</div>
                            <div className="text-sm sm:text-base text-gray-600">@{profile.username}</div>
                            <div className="text-xs sm:text-sm text-gray-500 mt-1">
                              {profile.games_fetched} games fetched
                            </div>
                          </div>
                          <div className="flex items-center justify-between sm:justify-end space-x-2 sm:space-x-3">
                            <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs sm:text-sm">
                              Connected
                            </Badge>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50 h-8 w-8 sm:h-9 sm:w-9 p-0"
                                  disabled={deletingProfileId === profile.id}
                                >
                                  {deletingProfileId === profile.id ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Trash2 className="h-4 w-4" />
                                  )}
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent className="mx-4 max-w-md">
                                <AlertDialogHeader>
                                  <AlertDialogTitle className="text-base sm:text-lg">Delete Chess Profile</AlertDialogTitle>
                                  <AlertDialogDescription className="text-sm sm:text-base">
                                    Are you sure you want to delete the {profile.platform} profile for @{profile.username}?
                                    This action cannot be undone and will remove all associated game analysis data.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter className="flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                                  <AlertDialogCancel className="w-full sm:w-auto">Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteProfile(profile.id)}
                                    className="bg-red-600 hover:bg-red-700 w-full sm:w-auto"
                                  >
                                    Delete Profile
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 sm:py-8 text-gray-500">
                    <User className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-3 sm:mb-4 text-gray-300" />
                    <p className="text-base sm:text-lg font-medium">No chess profiles connected</p>
                    <p className="text-sm sm:text-base">Add your first chess profile to start analyzing your games</p>
                  </div>
                )}

                {/* Message when all platforms are connected */}
                {hasLichessProfile && hasChessComProfile && (
                  <div className="text-center py-4 sm:py-6 text-gray-500 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-center justify-center mb-2">
                      <User className="h-5 w-5 sm:h-6 sm:w-6 mr-2 text-green-600" />
                      <span className="text-base sm:text-lg font-medium text-green-800">All Platforms Connected</span>
                    </div>
                    <p className="text-xs sm:text-sm">You have connected profiles for both Lichess and Chess.com</p>
                  </div>
                )}

                {/* Create Profile Form */}
                {showCreateForm && (
                  <Card className="border-blue-200 bg-blue-50">
                    <CardHeader className="pb-3 sm:pb-4">
                      <CardTitle className="text-blue-800 text-base sm:text-lg">Add New Chess Profile</CardTitle>
                      <CardDescription className="text-blue-700 text-sm sm:text-base">
                        {!hasLichessProfile && !hasChessComProfile
                          ? "Enter your username for Lichess and/or Chess.com"
                          : !hasLichessProfile
                            ? "Enter your Lichess username"
                            : "Enter your Chess.com username"
                        }
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <form onSubmit={handleCreateProfile} className="space-y-4">
                        <div className={`grid gap-4 ${!hasLichessProfile && !hasChessComProfile ? 'grid-cols-1 sm:grid-cols-2' : 'grid-cols-1'}`}>
                          {!hasLichessProfile && (
                            <ChessUsernameInput
                              id="lichess"
                              label="Lichess Username"
                              placeholder="Your Lichess username"
                              value={profileFormData.lichessUsername}
                              onChange={(value) => setProfileFormData(prev => ({ ...prev, lichessUsername: value }))}
                              onValidate={validateLichessUsernameAsync}
                              validation={lichessValidation}
                              disabled={isCreatingProfile || validatingUsernames}
                              className="text-blue-800 text-sm sm:text-base"
                            />
                          )}
                          {!hasChessComProfile && (
                            <ChessUsernameInput
                              id="chesscom"
                              label="Chess.com Username"
                              placeholder="Your Chess.com username"
                              value={profileFormData.chessComUsername}
                              onChange={(value) => setProfileFormData(prev => ({ ...prev, chessComUsername: value }))}
                              onValidate={validateChessComUsernameAsync}
                              validation={chessComValidation}
                              disabled={isCreatingProfile || validatingUsernames}
                              className="text-blue-800 text-sm sm:text-base"
                            />
                          )}
                        </div>
                        <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                              setShowCreateForm(false)
                              setProfileFormData({ lichessUsername: '', chessComUsername: '' })
                              clearMessages()
                              clearValidation()
                            }}
                            disabled={isCreatingProfile || validatingUsernames}
                            className="w-full sm:w-auto text-sm sm:text-base"
                          >
                            Cancel
                          </Button>
                          <Button
                            type="submit"
                            className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto text-sm sm:text-base"
                            disabled={isCreatingProfile || validatingUsernames || (
                              (!hasLichessProfile && !profileFormData.lichessUsername.trim()) &&
                              (!hasChessComProfile && !profileFormData.chessComUsername.trim())
                            )}
                          >
                            {isCreatingProfile ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Creating...
                              </>
                            ) : validatingUsernames ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Validating...
                              </>
                            ) : (
                              !hasLichessProfile && !hasChessComProfile
                                ? 'Create Profiles'
                                : !hasLichessProfile
                                  ? 'Create Lichess Profile'
                                  : 'Create Chess.com Profile'
                            )}
                          </Button>
                        </div>
                      </form>
                    </CardContent>
                  </Card>
                )}
              </CardContent>
            </Card>
          )}
        </MobileCard>
        </MobileContainer>

        <Footer hideOnMobile={true} />
      </div>
    </MobileLayout>
  )
}
