'use client'

/**
 * Debug component to verify Arrow Duel analysis matches main Stockfish analysis
 * Use this to test if arrow duel best moves appear in top 3 lines of main analysis
 */

import { useState } from 'react'
import { Chess } from 'chess.js'
import { ArrowDuelFilter, ARROW_DUEL_ANALYSIS_DEPTH } from '@/lib/arrow-duel-filter'
import { useStockfish } from '@/hooks/useStockfish'

interface DebugResult {
  fen: string
  arrowDuelBestMove: string
  arrowDuelDepth: number
  arrowDuelEval: number
  mainAnalysisLines: Array<{
    multipv: number
    move: string
    evaluation: number
    depth: number
  }>
  isArrowDuelMoveInTop3: boolean
}

export default function ArrowDuelDebugger() {
  const [testFen, setTestFen] = useState('rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2')
  const [blunderMove, setBlunderMove] = useState('Qh5')
  const [result, setResult] = useState<DebugResult | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const { analyzePosition, analysis } = useStockfish()

  const runDebugAnalysis = async () => {
    setIsAnalyzing(true)
    setError(null)
    setResult(null)

    try {
      // Validate FEN and move
      const game = new Chess(testFen)
      try {
        game.move(blunderMove)
        game.undo()
      } catch (e) {
        throw new Error(`Invalid move "${blunderMove}" for position`)
      }

      // Run Arrow Duel analysis
      const filter = new ArrowDuelFilter()
      const testPuzzle = {
        puzzle_id: 'debug-test',
        fen: testFen,
        solution_moves: [blunderMove],
        rating: 1200,
        themes: ['debug'],
        sequence_in_sprint: 1
      }

      const arrowDuelResult = await filter.filterPuzzle(testPuzzle)
      if (!arrowDuelResult) {
        throw new Error('Arrow Duel filter rejected this position')
      }

      // Run main Stockfish analysis  
      await analyzePosition(testFen, 20) // Same depth as main analysis
      
      // Wait a bit for analysis to complete
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Extract main analysis results
      const mainLines = analysis.lines.map(line => ({
        multipv: line.multipv,
        move: line.bestMove,
        evaluation: line.evaluation,
        depth: line.depth
      }))

      // Check if arrow duel best move appears in top 3
      const arrowDuelMove = arrowDuelResult.bestMove
      const isInTop3 = mainLines.slice(0, 3).some(line => line.move === arrowDuelMove)

      const debugResult: DebugResult = {
        fen: testFen,
        arrowDuelBestMove: arrowDuelMove,
        arrowDuelDepth: arrowDuelResult.analysisDepth,
        arrowDuelEval: arrowDuelResult.initialEval,
        mainAnalysisLines: mainLines,
        isArrowDuelMoveInTop3: isInTop3
      }

      setResult(debugResult)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analysis failed')
    } finally {
      setIsAnalyzing(false)
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Arrow Duel Analysis Debugger</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label className="block text-sm font-medium mb-2">Test FEN Position</label>
          <textarea
            value={testFen}
            onChange={(e) => setTestFen(e.target.value)}
            className="w-full p-2 border rounded text-sm font-mono"
            rows={3}
            placeholder="Enter FEN position..."
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">Blunder Move</label>
          <input
            value={blunderMove}
            onChange={(e) => setBlunderMove(e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="e.g., Qh5"
          />
          <div className="text-xs text-gray-600 mt-1">
            Arrow Duel Depth: {ARROW_DUEL_ANALYSIS_DEPTH}
          </div>
        </div>
      </div>

      <button
        onClick={runDebugAnalysis}
        disabled={isAnalyzing}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {isAnalyzing ? 'Analyzing...' : 'Run Debug Analysis'}
      </button>

      {error && (
        <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          Error: {error}
        </div>
      )}

      {result && (
        <div className="mt-6 space-y-4">
          <div className={`p-4 rounded border-2 ${
            result.isArrowDuelMoveInTop3 
              ? 'bg-green-50 border-green-500' 
              : 'bg-red-50 border-red-500'
          }`}>
            <h3 className="font-bold text-lg">
              {result.isArrowDuelMoveInTop3 ? '✅ MATCH FOUND' : '❌ NO MATCH'}
            </h3>
            <p>
              Arrow Duel best move <strong>{result.arrowDuelBestMove}</strong> 
              {result.isArrowDuelMoveInTop3 ? ' appears' : ' does NOT appear'} in top 3 main analysis lines
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded">
              <h4 className="font-semibold mb-2">Arrow Duel Analysis</h4>
              <div className="text-sm space-y-1">
                <div>Best Move: <strong>{result.arrowDuelBestMove}</strong></div>
                <div>Evaluation: {result.arrowDuelEval}</div>
                <div>Depth: {result.arrowDuelDepth}</div>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded">
              <h4 className="font-semibold mb-2">Main Analysis Top 3</h4>
              <div className="text-sm space-y-1">
                {result.mainAnalysisLines.slice(0, 3).map((line, i) => (
                  <div key={i} className={
                    line.move === result.arrowDuelBestMove ? 'font-bold text-green-600' : ''
                  }>
                    {i + 1}. {line.move} ({line.evaluation}) d:{line.depth}
                  </div>
                ))}
              </div>
            </div>
          </div>

          <details className="bg-gray-50 p-4 rounded">
            <summary className="cursor-pointer font-medium">All Main Analysis Lines</summary>
            <div className="mt-2 text-sm space-y-1">
              {result.mainAnalysisLines.map((line, i) => (
                <div key={i}>
                  Line {line.multipv}: {line.move} (eval: {line.evaluation}, depth: {line.depth})
                </div>
              ))}
            </div>
          </details>
        </div>
      )}
    </div>
  )
}