"use client"

import React, { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ChessBoard, ChessBoardRef } from './ChessBoard'
import { InteractiveAnalysis } from './InteractiveAnalysis'
import { useSprintPuzzles, SprintPuzzleAttempt, PuzzleAttemptRequest } from '@/hooks/useSprintApi'
import { Chess } from 'chess.js'
import {
  ChevronLeft,
  ChevronRight,
  X,
  Eye,
  Clock,
  Target,
  AlertTriangle
} from 'lucide-react'

interface MistakeReviewProps {
  sessionId: string
  eloType: string
  failedAttempts?: PuzzleAttemptRequest[]
  onClose: () => void
}

// Analysis line interface for arrows
interface AnalysisLine {
  moves: string[] // SAN moves for display
  uciMoves?: string[] // UCI moves for engine commands
  evaluation: number
  depth: number
  pv: string // Principal variation
  multipv?: number // MultiPV line number (1, 2, 3)
  isMate?: boolean // Whether this is a mate score
  mateIn?: number // Mate in N moves (positive for white, negative for black)
}

export function MistakeReview({ sessionId, eloType, failedAttempts, onClose }: MistakeReviewProps) {
  const { getSprintPuzzles, isLoading, error } = useSprintPuzzles()
  const [mistakes, setMistakes] = useState<SprintPuzzleAttempt[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [analysisMode, setAnalysisMode] = useState<'retry' | 'analyze'>('retry')
  const [analysisFen, setAnalysisFen] = useState<string>('')
  const [puzzleSolved, setPuzzleSolved] = useState(false)
  const [wrongMoveSquares, setWrongMoveSquares] = useState<Record<string, any>>({})
  const [moveHistory, setMoveHistory] = useState<string[]>([]) // Track FEN history for move back
  const [puzzleStartFen, setPuzzleStartFen] = useState<string>('') // Store puzzle start position
  const [analysisLines, setAnalysisLines] = useState<AnalysisLine[]>([]) // Store analysis data for arrows
  const [blunderArrow, setBlunderArrow] = useState<AnalysisLine | null>(null) // Red arrow for blunder move in arrow duel
  const analysisBoardRef = useRef<ChessBoardRef>(null)
  const chessBoardRef = useRef<ChessBoardRef>(null)

  // Load mistakes on component mount
  useEffect(() => {
    if (failedAttempts && failedAttempts.length > 0) {
      // Use client-side failed attempts if available
      
      // Convert PuzzleAttemptRequest to SprintPuzzleAttempt format
      // Note: We'll need to fetch puzzle data (FEN, solution_moves, etc.) from server
      // But we have the complete attempt data with Arrow Duel info
      const convertedMistakes: SprintPuzzleAttempt[] = failedAttempts.map(attempt => ({
        puzzle_id: attempt.puzzle_id,
        sequence_in_sprint: attempt.sequence_in_sprint,
        fen: '', // Will be populated from server data
        solution_moves: [], // Will be populated from server data
        rating: 0, // Will be populated from server data
        themes: [], // Will be populated from server data
        user_moves: attempt.user_moves,
        was_correct: attempt.was_correct,
        time_taken_ms: attempt.time_taken_ms,
        attempted_at: attempt.attempted_at,
        attempt_type: attempt.attempt_type,
        candidate_moves: attempt.candidate_moves,
        chosen_move: attempt.chosen_move
      }))
      
      setMistakes(convertedMistakes)
      // Still need to fetch puzzle details from server to get FEN, solution_moves, etc.
      loadPuzzleDetails(convertedMistakes)
    } else {
      // Fallback to server-side loading
      loadMistakes()
    }
    
    async function loadMistakes() {
      const response = await getSprintPuzzles(sessionId, { status: 'failed' })
      if (response && response.puzzles.length > 0) {
        setMistakes(response.puzzles)
      }
    }
    
    async function loadPuzzleDetails(convertedMistakes: SprintPuzzleAttempt[]) {
      // Fetch puzzle details to populate missing fields
      const response = await getSprintPuzzles(sessionId, { status: 'failed' })
      if (response && response.puzzles.length > 0) {
        // Merge server puzzle data with client attempt data
        const mergedMistakes = convertedMistakes.map(mistake => {
          const serverPuzzle = response.puzzles.find(p => p.puzzle_id === mistake.puzzle_id)
          if (serverPuzzle) {
            return {
              ...mistake,
              fen: serverPuzzle.fen,
              solution_moves: serverPuzzle.solution_moves,
              rating: serverPuzzle.rating,
              themes: serverPuzzle.themes
            }
          }
          return mistake
        })
        setMistakes(mergedMistakes)
      }
    }
  }, [sessionId, getSprintPuzzles, failedAttempts])

  const currentMistake = mistakes[currentIndex]
  
  // Determine if this is an Arrow Duel session from ELO type
  const isArrowDuelSession = eloType.includes('arrowduel')
  

  // Calculate consistent board orientation for the current mistake
  const getBoardOrientation = (fenString: string, isArrowDuel: boolean): 'white' | 'black' => {
    const activeColor = fenString.split(' ')[1]
    
    if (isArrowDuel) {
      // For arrow duel, the player making the move should be at the bottom
      // If it's white's turn in FEN, white should be at bottom (black orientation)
      // If it's black's turn in FEN, black should be at bottom (white orientation) 
      return activeColor === 'w' ? 'white' : 'black'
    } else {
      // For regular puzzles, we want the player (who needs to move) at the bottom
      // If it's black's turn in FEN, black should be at bottom (white orientation)
      // If it's white's turn in FEN, white should be at bottom (black orientation)
      return activeColor === 'b' ? 'white' : 'black'
    }
  }

  const currentOrientation = currentMistake ? getBoardOrientation(currentMistake.fen, isArrowDuelSession) : 'white'

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
      setAnalysisMode('retry')
      setPuzzleSolved(false)
      setWrongMoveSquares({})
      setAnalysisLines([]) // Clear analysis arrows
      setBlunderArrow(null) // Clear blunder arrow
    }
  }

  const handleNext = () => {
    if (currentIndex < mistakes.length - 1) {
      setCurrentIndex(currentIndex + 1)
      setAnalysisMode('retry')
      setPuzzleSolved(false)
      setWrongMoveSquares({})
      setAnalysisLines([]) // Clear analysis arrows
      setBlunderArrow(null) // Clear blunder arrow
    }
  }

  const handlePuzzleComplete = (success: boolean, userMoves: string[]) => {
    if (success) {
      setPuzzleSolved(true)
    }
  }

  const handleWrongMove = (fromSquare: string, toSquare: string) => {
    // Show red highlight on wrong move squares
    setWrongMoveSquares({
      [fromSquare]: { backgroundColor: 'rgba(239, 68, 68, 0.6)' },
      [toSquare]: { backgroundColor: 'rgba(239, 68, 68, 0.6)' }
    })

    // Clear the highlight after a delay
    setTimeout(() => {
      setWrongMoveSquares({})
    }, 800)
  }

  const handleAnalyze = () => {
    if (analysisMode === 'analyze') {
      // Exit analysis mode
      setAnalysisMode('retry')
      setPuzzleSolved(false)
      setMoveHistory([])
      setPuzzleStartFen('')
      setAnalysisLines([]) // Clear analysis arrows
    } else {
      // Enter analysis mode - start from current board position
      setAnalysisMode('analyze')

      if (currentMistake) {
        // Initialize move history with both the original FEN and the position after first move
        const originalFen = currentMistake.fen
        const initialHistory = [originalFen]

        // For arrow duel puzzles, analyze the initial position
        // For regular puzzles, analyze the position after the first move
        if (isArrowDuelSession) {
          // Arrow duel: analyze the initial position (where player makes the choice)
          setAnalysisFen(originalFen)
          if (analysisBoardRef.current) {
            analysisBoardRef.current.setPosition(originalFen)
          }

          // Create red arrow for the blunder move 
          // Determine correct moves using fallback logic
          const correctedBlunderMove = currentMistake.blunder_move || currentMistake.solution_moves?.[0]
          const correctedBestMove = currentMistake.best_move || (() => {
            if (currentMistake.candidate_moves && currentMistake.solution_moves?.[0]) {
              const blunderMove = currentMistake.solution_moves[0]
              return currentMistake.candidate_moves.find(move => move !== blunderMove)
            }
            return currentMistake.candidate_moves?.[1]
          })()
          
          console.log('🔍 Arrow duel move analysis:', {
            puzzleId: currentMistake.puzzle_id,
            candidateMoves: currentMistake.candidate_moves,
            solutionMove: currentMistake.solution_moves?.[0],
            correctedBlunderMove: correctedBlunderMove,
            correctedBestMove: correctedBestMove,
            logic: 'blunder = solution_moves[0], best = other candidate'
          })
          
          if (correctedBlunderMove) {
            const blunderMove = correctedBlunderMove
            
            // Create a red arrow analysis line for the blunder move
            const blunderArrowLine: AnalysisLine = {
              moves: [blunderMove],
              uciMoves: [blunderMove],
              evaluation: -500, // Negative evaluation to indicate it's bad
              depth: 1,
              pv: blunderMove,
              multipv: 99, // Special multipv to indicate this is a blunder arrow
              isMate: false
            }
            
            setBlunderArrow(blunderArrowLine)
          }
        } else {
          // Clear blunder arrow for non-arrow duel puzzles
          setBlunderArrow(null)
        }
        
        if (currentMistake.solution_moves.length > 0 && !isArrowDuelSession) {
          // Regular puzzle: analyze position after first move
          try {
            const chess = new Chess(originalFen)
            const firstMove = currentMistake.solution_moves[0]
            const move = chess.move(firstMove)
            if (move) {
              const afterFirstMoveFen = chess.fen()
              initialHistory.push(afterFirstMoveFen)
              // Start analysis from the position after the first move
              setAnalysisFen(afterFirstMoveFen)
              if (analysisBoardRef.current) {
                analysisBoardRef.current.setPosition(afterFirstMoveFen)
              }
            } else {
              // If first move fails, start from original position
              setAnalysisFen(originalFen)
            }
          } catch (error) {
            console.error('Failed to calculate position after first move:', error)
            setAnalysisFen(originalFen)
          }
        } else {
          // No solution moves, start from original position
          setAnalysisFen(originalFen)
        }

        setMoveHistory(initialHistory)
        setPuzzleStartFen(originalFen)
      }
    }
  }

  const handlePositionChange = (newFen: string, lastMove?: string) => {
    setAnalysisFen(newFen)
    // Add to move history if this is a new position (not from move back)
    setMoveHistory(prev => {
      // Only add if it's not the same as the last position
      if (prev.length === 0 || prev[prev.length - 1] !== newFen) {
        return [...prev, newFen]
      }
      return prev
    })
  }

  const handleMakeMove = (move: string) => {
    // Call the analysis board's makeMove method through the ref
    if (analysisBoardRef.current) {
      analysisBoardRef.current.makeMove(move)
    }
  }

  const handleAnalysisUpdate = (newAnalysisLines: AnalysisLine[]) => {
    setAnalysisLines(newAnalysisLines)
  }



  const handleMoveBack = () => {
    if (moveHistory.length > 1) {
      // Remove the current position and go back to the previous one
      const newHistory = moveHistory.slice(0, -1)
      const previousFen = newHistory[newHistory.length - 1]

      setMoveHistory(newHistory)
      setAnalysisFen(previousFen)

      // Update the board position through the ref
      if (analysisBoardRef.current) {
        analysisBoardRef.current.setPosition(previousFen)
      }
    }
  }

  const handleResetToPuzzleStart = () => {
    if (currentMistake) {
      const originalFen = currentMistake.fen

      // Reset move history to include both original FEN and position after first move (if exists)
      const initialHistory = [originalFen]
      let targetFen = originalFen // Default to original FEN

      // For arrow duel puzzles, always reset to the initial position
      // For regular puzzles, reset to position after first move
      if (isArrowDuelSession) {
        targetFen = originalFen // Arrow duel: reset to initial position
        
        // Recreate the blunder arrow on reset
        if (currentMistake.blunder_move) {
          const blunderMove = currentMistake.blunder_move // Actual blunder move from puzzle data
          const blunderArrowLine: AnalysisLine = {
            moves: [blunderMove],
            uciMoves: [blunderMove],
            evaluation: -500,
            depth: 1,
            pv: blunderMove,
            multipv: 99,
            isMate: false
          }
          setBlunderArrow(blunderArrowLine)
        } else if (currentMistake.solution_moves && currentMistake.solution_moves.length > 0) {
          // Fallback: use old logic for backward compatibility
          const blunderMove = currentMistake.solution_moves[0]
          const blunderArrowLine: AnalysisLine = {
            moves: [blunderMove],
            uciMoves: [blunderMove],
            evaluation: -500,
            depth: 1,
            pv: blunderMove,
            multipv: 99,
            isMate: false
          }
          setBlunderArrow(blunderArrowLine)
        }
      } else {
        // Clear blunder arrow for non-arrow duel puzzles
        setBlunderArrow(null)
      }
      
      if (currentMistake.solution_moves.length > 0 && !isArrowDuelSession) {
        try {
          const chess = new Chess(originalFen)
          const firstMove = currentMistake.solution_moves[0]
          const move = chess.move(firstMove)
          if (move) {
            const afterFirstMoveFen = chess.fen()
            initialHistory.push(afterFirstMoveFen)
            targetFen = afterFirstMoveFen // Reset to FEN + 1 move
          }
        } catch (error) {
          console.error('Failed to calculate position after first move:', error)
        }
      }

      setMoveHistory(initialHistory)
      setAnalysisFen(targetFen)

      // Update the board position
      if (analysisBoardRef.current) {
        analysisBoardRef.current.setPosition(targetFen)
      }
    }
  }

  const formatTime = (ms?: number): string => {
    if (!ms) return 'N/A'
    const seconds = Math.round(ms / 1000)
    return `${seconds}s`
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center max-w-md mx-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your mistakes...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center max-w-md mx-4">
          <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Failed to load mistakes</p>
          <p className="text-gray-600 text-sm mb-4">{error}</p>
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      </div>
    )
  }

  if (mistakes.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center max-w-md mx-4">
          <Target className="h-8 w-8 text-green-600 mx-auto mb-4" />
          <p className="text-gray-900 font-medium mb-2">No mistakes to review!</p>
          <p className="text-gray-600 text-sm mb-4">
            You didn't make any mistakes in this sprint.
          </p>
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-3 sm:px-6 sm:py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="flex items-center gap-2 text-lg sm:text-xl font-bold text-gray-900">
                <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
                Review Mistakes
              </h1>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                Puzzle {currentIndex + 1} of {mistakes.length}
              </p>
            </div>
            <Button onClick={onClose} variant="outline" size="sm">
              <X className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Close</span>
              <span className="sm:hidden">Exit</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col lg:flex-row max-w-7xl mx-auto w-full">
        {/* Chess Board Section */}
        <div className="flex-1 flex flex-col min-h-0 lg:min-w-0">
          <div className="flex-1 p-4 sm:p-6 flex justify-center bg-white" style={{ paddingTop: '2rem' }}>
            {currentMistake && (
              <div className="w-full h-full max-w-2xl max-h-full">
                {analysisMode === 'analyze' ? (
                  <ChessBoard
                    ref={analysisBoardRef}
                    key={`analysis-${currentMistake.puzzle_id}`}
                    fen={analysisFen || currentMistake.fen}
                    orientation={currentOrientation}
                    onPositionChange={handlePositionChange}
                    onMoveAnalysis={handleMakeMove}
                    mode="analysis"
                    disabled={false}
                    analysisLines={
                      // Only show blunder arrow if we're at the original position
                      blunderArrow && (analysisFen === currentMistake.fen || !analysisFen)
                        ? [blunderArrow, ...analysisLines] 
                        : analysisLines
                    }
                    showAnalysisArrows={true}
                  />
                ) : isArrowDuelSession ? (
                  // Arrow Duel mode with candidate moves
                  currentMistake.candidate_moves && currentMistake.candidate_moves.length >= 2 ? (
                    <ChessBoard
                      ref={chessBoardRef}
                      key={`arrowduel-${currentMistake.puzzle_id}`}
                      fen={currentMistake.fen}
                      orientation={currentOrientation}
                      mode="arrowduel"
                      disabled={false}
                      candidateMoves={currentMistake.candidate_moves as [string, string]}
                      bestMove={currentMistake.best_move || (() => {
                        // Fallback: determine best move by excluding the puzzle solution (blunder)
                        if (currentMistake.candidate_moves && currentMistake.solution_moves?.[0]) {
                          const blunderMove = currentMistake.solution_moves[0]
                          return currentMistake.candidate_moves.find(move => move !== blunderMove)
                        }
                        return currentMistake.candidate_moves?.[1] // Last resort fallback
                      })()}
                      blunderMove={currentMistake.blunder_move || currentMistake.solution_moves?.[0]} // Blunder is always the puzzle solution
                      onMoveChosen={(chosenMove, isCorrect) => {
                        if (isCorrect) {
                          setPuzzleSolved(true)
                        }
                      }}
                    />
                  ) : (
                    // Fallback when candidate moves are missing
                    <div className="space-y-4">
                      <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-2">
                          <AlertTriangle className="h-4 w-4 text-orange-600" />
                          <span className="text-sm font-medium text-orange-800">Arrow Duel Data Missing</span>
                        </div>
                        <p className="text-xs text-orange-700">
                          The candidate moves data for this Arrow Duel mistake is not available. 
                          You can still analyze the position where the mistake was made.
                        </p>
                      </div>
                      <ChessBoard
                        ref={chessBoardRef}
                        key={`arrowduel-fallback-${currentMistake.puzzle_id}`}
                        fen={currentMistake.fen}
                        solutionMoves={currentMistake.solution_moves}
                        onPuzzleComplete={handlePuzzleComplete}
                        onMoveAttempt={(move, isCorrect) => {
                          if (!isCorrect) {
                            const fromSquare = move.slice(0, 2)
                            const toSquare = move.slice(2, 4)
                            handleWrongMove(fromSquare, toSquare)
                          }
                        }}
                        disabled={false}
                        undoWrongMoves={true}
                        mode="puzzle"
                        orientation={currentOrientation}
                      />
                    </div>
                  )
                ) : (
                  <ChessBoard
                    ref={chessBoardRef}
                    key={`puzzle-${currentMistake.puzzle_id}`}
                    fen={currentMistake.fen}
                    solutionMoves={currentMistake.solution_moves}
                    onPuzzleComplete={handlePuzzleComplete}
                    onMoveAttempt={(move, isCorrect) => {
                      if (!isCorrect) {
                        // Extract from/to squares from the move
                        const fromSquare = move.slice(0, 2)
                        const toSquare = move.slice(2, 4)
                        handleWrongMove(fromSquare, toSquare)
                      }
                    }}
                    disabled={false}
                    undoWrongMoves={true}
                    mode="puzzle"
                    orientation={currentOrientation}
                  />
                )}
              </div>
            )}
          </div>
        </div>

        {/* Info Panel */}
        <div className="w-full lg:w-96 xl:w-[28rem] flex-shrink-0 border-t lg:border-t-0 lg:border-l bg-white lg:bg-gray-50 overflow-y-auto">
          <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
            {/* Puzzle Info */}
            {currentMistake && (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-3 xl:grid-cols-1 gap-2 sm:gap-3 xl:gap-2">
                  <div className="flex items-center justify-between sm:flex-col sm:items-start xl:flex-row xl:items-center">
                    <span className="text-xs sm:text-sm font-medium text-gray-700">Rating</span>
                    <Badge variant="secondary" className="text-xs sm:text-sm">{currentMistake.rating}</Badge>
                  </div>
                  <div className="flex items-center justify-between sm:flex-col sm:items-start xl:flex-row xl:items-center">
                    <span className="text-xs sm:text-sm font-medium text-gray-700">Time Taken</span>
                    <Badge variant="outline" className="flex items-center gap-1 text-xs sm:text-sm">
                      <Clock className="h-3 w-3" />
                      {formatTime(currentMistake.time_taken_ms)}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between sm:flex-col sm:items-start xl:flex-row xl:items-center">
                    <span className="text-xs sm:text-sm font-medium text-gray-700">Sequence</span>
                    <Badge variant="outline" className="text-xs sm:text-sm">#{currentMistake.sequence_in_sprint}</Badge>
                  </div>
                </div>

                {/* Arrow Duel Mode Info */}
                {isArrowDuelSession && analysisMode === 'retry' && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">🏹 Arrow Duel Review</span>
                    </div>
                    <p className="text-xs text-blue-700">
                      Choose the better move between the two arrows. This is your chance to make the right choice and avoid the blunder.
                    </p>
                  </div>
                )}

                {/* Themes */}
                {currentMistake.themes.length > 0 && (
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-700 mb-2">Themes</p>
                    <div className="flex flex-wrap gap-1">
                      {currentMistake.themes.map((theme, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {theme}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}

            {/* Puzzle Status */}
            {puzzleSolved && analysisMode === 'retry' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    {isArrowDuelSession ? 'Correct Choice!' : 'Puzzle Solved!'}
                  </span>
                </div>
                <p className="text-xs text-green-700 mt-1">
                  {isArrowDuelSession 
                    ? 'Excellent! You chose the better move and avoided the blunder.' 
                    : 'Great job! You found the correct solution.'
                  }
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="grid grid-cols-1 gap-2">
              <Button
                onClick={handleAnalyze}
                variant={analysisMode === 'analyze' ? 'default' : 'outline'}
                className="w-full text-xs sm:text-sm"
                size="sm"
              >
                <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                {analysisMode === 'analyze' ? 'Exit Analysis' : 'Analyze'}
              </Button>
            </div>

            {/* Analysis Panel - Only show in analysis mode */}
            {analysisMode === 'analyze' && currentMistake && (
              <div className="border-t pt-3 sm:pt-4">
                <InteractiveAnalysis
                  fen={analysisFen || currentMistake.fen}
                  onMakeMove={handleMakeMove}
                  onMoveBack={handleMoveBack}
                  onResetToPuzzleStart={handleResetToPuzzleStart}
                  canMoveBack={moveHistory.length > 1}
                  canReset={currentMistake.solution_moves.length > 0}
                  onAnalysisUpdate={handleAnalysisUpdate}
                />
              </div>
            )}

            {/* Navigation */}
            <div className="flex gap-2 pt-2 border-t">
              <Button
                onClick={handlePrevious}
                variant="outline"
                className="flex-1 text-xs sm:text-sm"
                disabled={currentIndex === 0}
                size="sm"
              >
                <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                <span className="hidden sm:inline">Previous</span>
                <span className="sm:hidden">Prev</span>
              </Button>
              <Button
                onClick={handleNext}
                variant="outline"
                className="flex-1 text-xs sm:text-sm"
                disabled={currentIndex === mistakes.length - 1}
                size="sm"
              >
                <span className="hidden sm:inline">Next</span>
                <span className="sm:hidden">Next</span>
                <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
