# GraphQL Query Examples

This document provides examples of GraphQL queries that can be used with the Chessticize API. These examples demonstrate how to filter, sort, and paginate games and puzzles to find specific training material.

## Authentication

All GraphQL queries require authentication. You need to include a valid JWT token in the `Authorization` header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Base URL

The GraphQL API is available at:

```
https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query
```

## Running the Examples

You can run these examples using any GraphQL client like Insomnia, Postman, or curl.

For curl examples, replace `YOUR_JWT_TOKEN` with your actual JWT token.

## GraphQL Schema

The GraphQL schema provides the following main query types:

- **myGames**: Query your games with filtering, pagination, and sorting
- **myPuzzles**: Query your puzzles with filtering, pagination, and sorting
- **game**: Get a specific game by ID
- **puzzle**: Get a specific puzzle by ID

### Connection-based Pagination

All list queries return a connection object with the following structure:

```graphql
{
  edges: [
    {
      node: { ... },  # The actual object (game or puzzle)
      cursor: String  # Opaque cursor for pagination
    }
  ],
  page_info: {
    has_next_page: Boolean
    has_previous_page: Boolean
    start_cursor: String
    end_cursor: String
  },
  total_count: Int  # Total number of items matching the query
}
```

## Example Queries

### 1. Get Puzzle Statistics

**Use Case**: Get statistics about your puzzles, including tag counts, theme counts, user color counts, game move distribution, and move length distribution.

**Query**:
```graphql
{
  myPuzzleStats {
    tag_counts {
      tag
      count
    }
    theme_counts {
      theme
      count
    }
    user_color_counts {
      color
      count
    }
    game_move_buckets {
      name
      min_move
      max_move
      count
    }
    move_length_counts {
      length
      count
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzleStats { tag_counts { tag count } theme_counts { theme count } user_color_counts { color count } game_move_buckets { name min_move max_move count } move_length_counts { length count } total_count } }"
  }'
```

### 2. Get Puzzle Statistics with Filtering

**Use Case**: Get statistics about your puzzles where you played as white, within a specific time range.

**Query**:
```graphql
{
  myPuzzleStats(
    filter: {
      user_color: WHITE,
      game_start_time: "2023-01-01T00:00:00Z",
      game_end_time: "2023-12-31T23:59:59Z"
    }
  ) {
    tag_counts {
      tag
      count
    }
    theme_counts {
      theme
      count
    }
    user_color_counts {
      color
      count
    }
    game_move_buckets {
      name
      min_move
      max_move
      count
    }
    move_length_counts {
      length
      count
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzleStats(filter: { user_color: WHITE, game_start_time: \"2023-01-01T00:00:00Z\", game_end_time: \"2023-12-31T23:59:59Z\" }) { tag_counts { tag count } theme_counts { theme count } user_color_counts { color count } game_move_buckets { name min_move max_move count } move_length_counts { length count } total_count } }"
  }'
```

### 3. Get Puzzle Statistics for Last 50 Puzzles

**Use Case**: Get statistics about your most recent 50 puzzles.

**Query**:
```graphql
{
  myPuzzleStats(
    pagination: {
      offset: 0,
      limit: 50
    }
  ) {
    tag_counts {
      tag
      count
    }
    theme_counts {
      theme
      count
    }
    user_color_counts {
      color
      count
    }
    game_move_buckets {
      name
      min_move
      max_move
      count
    }
    move_length_counts {
      length
      count
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzleStats(pagination: { offset: 0, limit: 50 }) { tag_counts { tag count } theme_counts { theme count } user_color_counts { color count } game_move_buckets { name min_move max_move count } move_length_counts { length count } total_count } }"
  }'
```

### 4. Filter Puzzles by Game Time Control and Rated Status

**Use Case**: Find puzzles from rated games with a specific time control.

**Query**:
```graphql
{
  myPuzzles(
    filter: {
      time_control: "10+0",
      rated: true
    },
    pagination: {
      offset: 0,
      limit: 10
    }
  ) {
    edges {
      node {
        id
        theme
        user_color
        game_move
        fen
        moves
      }
      cursor
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzles(filter: { time_control: \"10+0\", rated: true }, pagination: { offset: 0, limit: 10 }) { edges { node { id theme user_color game_move fen moves } cursor } total_count } }"
  }'
```

### 5. Filter Games by Time Control and Rated Status

**Use Case**: Find games with a specific time control that are rated.

**Query**:
```graphql
{
  myGames(
    filter: {
      time_control: "10+0",
      rated: true
    },
    pagination: {
      offset: 0,
      limit: 10
    }
  ) {
    edges {
      node {
        id
        platform
        time_control
        rated
        user_color
        winner
        result
      }
      cursor
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myGames(filter: { time_control: \"10+0\", rated: true }, pagination: { offset: 0, limit: 10 }) { edges { node { id platform time_control rated user_color winner result } cursor } total_count } }"
  }'
```

### 6. Get Game Stats

**Use Case**: Get statistics about your games.

**Query**:
```graphql
{
  myGameStats {
    platform_counts {
      platform
      count
    }
    user_color_counts {
      color
      count
    }
    result_counts {
      result
      count
    }
    time_control_counts {
      time_control
      count
    }
    rated_counts {
      rated
      count
    }
    average_opponent_rating
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myGameStats { platform_counts { platform count } user_color_counts { color count } result_counts { result count } time_control_counts { time_control count } rated_counts { rated count } average_opponent_rating total_count } }"
  }'
```

### 7. Get Game Stats Filtered by Time Control and Rated Status

**Use Case**: Get statistics about your rated games with a specific time control.

**Query**:
```graphql
{
  myGameStats(filter: {time_control: "10+0", rated: true}) {
    platform_counts {
      platform
      count
    }
    user_color_counts {
      color
      count
    }
    result_counts {
      result
      count
    }
    time_control_counts {
      time_control
      count
    }
    rated_counts {
      rated
      count
    }
    average_opponent_rating
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myGameStats(filter: { time_control: \"10+0\", rated: true }) { platform_counts { platform count } user_color_counts { color count } result_counts { result count } time_control_counts { time_control count } rated_counts { rated count } average_opponent_rating total_count } }"
  }'
```

### 8. Get Game Stats with Pagination

**Use Case**: Get statistics about a specific subset of your games using pagination.

**Query**:
```graphql
{
  myGameStats(
    filter: {
      time_control: "10+0",
      rated: true
    },
    pagination: {
      offset: 0,
      limit: 10
    }
  ) {
    platform_counts {
      platform
      count
    }
    user_color_counts {
      color
      count
    }
    result_counts {
      result
      count
    }
    time_control_counts {
      time_control
      count
    }
    rated_counts {
      rated
      count
    }
    average_opponent_rating
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myGameStats(filter: { time_control: \"10+0\", rated: true }, pagination: { offset: 0, limit: 10 }) { platform_counts { platform count } user_color_counts { color count } result_counts { result count } time_control_counts { time_control count } rated_counts { rated count } average_opponent_rating total_count } }"
  }'
```

### 9. Get Puzzle Stats Filtered by Time Control, Rated Status, and Time Range

**Use Case**: Get statistics about puzzles from rated games with a specific time control and within a specific time range.

**Query**:
```graphql
{
  myPuzzleStats(
    filter: {
      time_control: "10+0",
      rated: true,
      game_start_time: "2023-01-01T00:00:00Z",
      game_end_time: "2023-12-31T23:59:59Z"
    }
  ) {
    tag_counts {
      tag
      count
    }
    theme_counts {
      theme
      count
    }
    user_color_counts {
      color
      count
    }
    game_move_buckets {
      name
      min_move
      max_move
      count
    }
    move_length_counts {
      length
      count
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzleStats(filter: { time_control: \"10+0\", rated: true, game_start_time: \"2023-01-01T00:00:00Z\", game_end_time: \"2023-12-31T23:59:59Z\" }) { tag_counts { tag count } theme_counts { theme count } user_color_counts { color count } game_move_buckets { name min_move max_move count } move_length_counts { length count } total_count } }"
  }'
```

### 10. Get Grouped Puzzle Stats by Time Period (Time-Based Aggregation)

**Use Case**: Get puzzle statistics grouped by time periods (e.g., by day, week, or month) to see trends over time.

**Query**:
```graphql
{
  myGroupedPuzzleStats(
    filter: {
      game_start_time: "2023-01-01T00:00:00Z",
      game_end_time: "2023-03-31T23:59:59Z"
    },
    pagination: {
      offset: 0,
      limit: 10
    },
    group_unit: WEEK,
    group_length: 1
  ) {
    nodes {
      start_time
      end_time
      stats {
        tag_counts {
          tag
          count
        }
        theme_counts {
          theme
          count
        }
        user_color_counts {
          color
          count
        }
        total_count
      }
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myGroupedPuzzleStats(filter: { game_start_time: \"2023-01-01T00:00:00Z\", game_end_time: \"2023-03-31T23:59:59Z\" }, pagination: { offset: 0, limit: 10 }, group_unit: WEEK, group_length: 1) { nodes { start_time end_time stats { tag_counts { tag count } theme_counts { theme count } user_color_counts { color count } total_count } } total_count } }"
  }'
```

### 10a. Get Grouped Puzzle Stats with Custom Group Length

**Use Case**: Get puzzle statistics grouped by custom time periods (e.g., every 3 days or every 2 weeks).

**Query**:
```graphql
{
  myGroupedPuzzleStats(
    filter: {
      game_start_time: "2023-01-01T00:00:00Z",
      game_end_time: "2023-03-31T23:59:59Z"
    },
    pagination: {
      offset: 0,
      limit: 10
    },
    group_unit: DAY,
    group_length: 3
  ) {
    nodes {
      start_time
      end_time
      stats {
        tag_counts {
          tag
          count
        }
        theme_counts {
          theme
          count
        }
        total_count
      }
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myGroupedPuzzleStats(filter: { game_start_time: \"2023-01-01T00:00:00Z\", game_end_time: \"2023-03-31T23:59:59Z\" }, pagination: { offset: 0, limit: 10 }, group_unit: DAY, group_length: 3) { nodes { start_time end_time stats { tag_counts { tag count } theme_counts { theme count } total_count } } total_count } }"
  }'
```

### 11. Get Grouped Game Stats by Time Period (Time-Based Aggregation)

**Note**: The `group_unit` parameter specifies the unit of time (DAY, WEEK, or MONTH), and the `group_length` parameter allows you to specify how many units to include in each group. For example, `group_unit: DAY, group_length: 3` will group stats by 3-day periods, and `group_unit: WEEK, group_length: 2` will group stats by 2-week periods.

**Use Case**: Get game statistics grouped by time periods (e.g., by day, week, or month) to see trends over time.

**Query**:
```graphql
{
  myGroupedGameStats(
    filter: {
      start_time: "2023-01-01T00:00:00Z",
      end_time: "2023-03-31T23:59:59Z"
    },
    pagination: {
      offset: 0,
      limit: 10
    },
    group_unit: MONTH,
    group_length: 1
  ) {
    nodes {
      start_time
      end_time
      stats {
        platform_counts {
          platform
          count
        }
        user_color_counts {
          color
          count
        }
        result_counts {
          result
          count
        }
        time_control_counts {
          time_control
          count
        }
        average_opponent_rating
        total_count
      }
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myGroupedGameStats(filter: { start_time: \"2023-01-01T00:00:00Z\", end_time: \"2023-03-31T23:59:59Z\" }, pagination: { offset: 0, limit: 10 }, group_unit: MONTH, group_length: 1) { nodes { start_time end_time stats { platform_counts { platform count } user_color_counts { color count } result_counts { result count } time_control_counts { time_control count } average_opponent_rating total_count } } total_count } }"
  }'
```

### 12. Find Missed Blunders in Early Game

**Use Case**: Find all puzzles where you are white and your opponent made a blunder that you missed, within the first 10 moves.

**Query**:
```graphql
{
  myPuzzles(
    filter: {
      user_color: WHITE,
      theme: OPPONENT_BLUNDER_MISSED,
      game_move_min: 0,
      game_move_max: 10
    },
    pagination: {
      offset: 0,
      limit: 10
    },
    sort: {
      field: "CREATED_AT",
      direction: DESC
    }
  ) {
    edges {
      node {
        id
        theme
        user_color
        game_move
        fen
        moves
        prev_cp
        cp
        tags
      }
      cursor
    }
    page_info {
      has_next_page
      has_previous_page
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzles(filter: { user_color: WHITE, theme: OPPONENT_BLUNDER_MISSED, game_move_min: 0, game_move_max: 10 }, pagination: { offset: 0, limit: 10 }, sort: { field: \"CREATED_AT\", direction: DESC }) { edges { node { id theme user_color game_move fen moves prev_cp cp tags } cursor } page_info { has_next_page has_previous_page } total_count } }"
  }'
```

**Expected Response**:
```json
{
  "data": {
    "myPuzzles": {
      "edges": [
        {
          "node": {
            "id": "puzzle-id-1",
            "theme": "opponent_blunder_missed",
            "user_color": "white",
            "game_move": 8,
            "fen": "r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3",
            "moves": ["d2d4", "e5d4"],
            "prev_cp": 0,
            "cp": -300,
            "tags": ["opening", "tactical"]
          },
          "cursor": "cursor-1"
        }
      ],
      "page_info": {
        "has_next_page": false,
        "has_previous_page": false
      },
      "total_count": 1
    }
  }
}
```

### 2. Find Games with Fork Puzzles

**Use Case**: Find all games along with the puzzles where you are black and the puzzles have a CP change of more than 200 with tag "fork".

**Query**:
```graphql
{
  myGames(
    filter: {
      user_color: BLACK
    },
    pagination: {
      offset: 0,
      limit: 10
    },
    sort: {
      field: "GAME_TIME",
      direction: DESC
    }
  ) {
    edges {
      node {
        id
        platform
        chess_username
        user_color
        game_time
        time_control
        pgn
        puzzles(filter: {
          cp_change_min: 200,
          tags: ["fork"]
        }) {
          id
          theme
          fen
          moves
          cp
          prev_cp
          tags
        }
      }
      cursor
    }
    page_info {
      has_next_page
      has_previous_page
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myGames(filter: { user_color: BLACK }, pagination: { offset: 0, limit: 10 }, sort: { field: \"GAME_TIME\", direction: DESC }) { edges { node { id platform chess_username user_color game_time time_control pgn puzzles(filter: { cp_change_min: 200, tags: [\"fork\"] }) { id theme fen moves cp prev_cp tags } } cursor } page_info { has_next_page has_previous_page } total_count } }"
  }'
```

**Expected Response**:
```json
{
  "data": {
    "myGames": {
      "edges": [
        {
          "node": {
            "id": "game-id-1",
            "platform": "lichess.org",
            "chess_username": "your_username",
            "user_color": "black",
            "game_time": "2023-06-15T14:30:00Z",
            "time_control": "10+0",
            "pgn": "1. e4 e5 2. Nf3 Nc6 3. Bc4 Nf6 ...",
            "puzzles": [
              {
                "id": "puzzle-id-2",
                "theme": "opponent_mistake_caught",
                "fen": "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4",
                "moves": ["d2d4", "e5d4"],
                "cp": 250,
                "prev_cp": 0,
                "tags": ["middlegame", "fork"]
              }
            ]
          },
          "cursor": "cursor-1"
        }
      ],
      "page_info": {
        "has_next_page": false,
        "has_previous_page": false
      },
      "total_count": 1
    }
  }
}
```

### 3. Find Tactical Puzzles with Large CP Swings

**Use Case**: Find puzzles with a large centipawn swing (300+) to practice tactical awareness.

**Query**:
```graphql
{
  myPuzzles(
    filter: {
      cp_change_min: 300
    },
    pagination: {
      offset: 0,
      limit: 20
    },
    sort: {
      field: "CP",
      direction: DESC
    }
  ) {
    edges {
      node {
        id
        theme
        fen
        moves
        prev_cp
        cp
        tags
        game {
          id
          platform
          game_time
          time_control
          rated
          # pgn is only loaded when explicitly requested
        }
      }
      cursor
    }
    page_info {
      has_next_page
      has_previous_page
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzles(filter: { cp_change_min: 300 }, pagination: { offset: 0, limit: 20 }, sort: { field: \"CP\", direction: DESC }) { edges { node { id theme fen moves prev_cp cp tags game { id platform game_time time_control rated } } cursor } page_info { has_next_page has_previous_page } total_count } }"
  }'
```

**Expected Response**:
```json
{
  "data": {
    "myPuzzles": {
      "edges": [
        {
          "node": {
            "id": "puzzle-id-3",
            "theme": "opponent_blunder_caught",
            "fen": "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4",
            "moves": ["d2d4", "e5d4"],
            "prev_cp": -100,
            "cp": 500,
            "tags": ["middlegame", "tactical"],
            "game": {
              "id": "game-id-2",
              "platform": "chess.com",
              "pgn": "1. e4 e5 2. Nf3 Nc6 3. Bc4 Nf6 ..."
            }
          },
          "cursor": "cursor-1"
        }
      ],
      "page_info": {
        "has_next_page": false,
        "has_previous_page": false
      },
      "total_count": 1
    }
  }
}
```

### 4. Find Endgame Puzzles

**Use Case**: Find endgame puzzles to improve your endgame skills.

**Query**:
```graphql
{
  myPuzzles(
    filter: {
      tags: ["endgame"]
    },
    pagination: {
      offset: 0,
      limit: 15
    },
    sort: {
      field: "CREATED_AT",
      direction: DESC
    }
  ) {
    edges {
      node {
        id
        theme
        fen
        moves
        prev_cp
        cp
        tags
      }
      cursor
    }
    page_info {
      has_next_page
      has_previous_page
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzles(filter: { tags: [\"endgame\"] }, pagination: { offset: 0, limit: 15 }, sort: { field: \"CREATED_AT\", direction: DESC }) { edges { node { id theme fen moves prev_cp cp tags } cursor } page_info { has_next_page has_previous_page } total_count } }"
  }'
```

**Expected Response**:
```json
{
  "data": {
    "myPuzzles": {
      "edges": [
        {
          "node": {
            "id": "puzzle-id-4",
            "theme": "opponent_mistake_caught",
            "fen": "4k3/8/8/8/8/8/4P3/4K3 w - - 0 1",
            "moves": ["e2e4", "e8d8"],
            "prev_cp": 0,
            "cp": 150,
            "tags": ["endgame", "king"]
          },
          "cursor": "cursor-1"
        }
      ],
      "page_info": {
        "has_next_page": false,
        "has_previous_page": false
      },
      "total_count": 1
    }
  }
}
```

### 5. Find Puzzles from Recent Games

**Use Case**: Find puzzles from games played in the last week to review recent mistakes.

**Query**:
```graphql
{
  myPuzzles(
    filter: {
      game_start_time: "2023-06-01T00:00:00Z"
    },
    pagination: {
      offset: 0,
      limit: 10
    },
    sort: {
      field: "CREATED_AT",
      direction: DESC
    }
  ) {
    edges {
      node {
        id
        theme
        fen
        moves
        game {
          id
          platform
          game_time
          # Note: pgn is not available here when accessing through a puzzle
        }
      }
      cursor
    }
    page_info {
      has_next_page
      has_previous_page
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzles(filter: { game_start_time: \"2023-06-01T00:00:00Z\" }, pagination: { offset: 0, limit: 10 }, sort: { field: \"CREATED_AT\", direction: DESC }) { edges { node { id theme fen moves game { id platform game_time } } cursor } page_info { has_next_page has_previous_page } total_count } }"
  }'
```

**Expected Response**:
```json
{
  "data": {
    "myPuzzles": {
      "edges": [
        {
          "node": {
            "id": "puzzle-id-5",
            "theme": "own_mistake_punished",
            "fen": "rnbqkbnr/pppp1ppp/8/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R b KQkq - 1 2",
            "moves": ["d7d5", "e4d5"],
            "game": {
              "id": "game-id-3",
              "platform": "lichess.org",
              "game_time": "2023-06-05T18:30:00Z",
              "pgn": "1. e4 e5 2. Nf3 d5 ..."
            }
          },
          "cursor": "cursor-1"
        }
      ],
      "page_info": {
        "has_next_page": false,
        "has_previous_page": false
      },
      "total_count": 1
    }
  }
}
```


## Pagination

All list queries support pagination using the `pagination` parameter:

```graphql
pagination: {
  offset: 0,  # Skip this many items
  limit: 10   # Return at most this many items
}
```

The response includes pagination information:

```graphql
page_info: {
  has_next_page: true,      # Whether there are more items
  has_previous_page: false, # Whether there are previous items
  start_cursor: "cursor1",  # Cursor for the first item in the result
  end_cursor: "cursor10"    # Cursor for the last item in the result
}
```

## Sorting

All list queries support sorting using the `sort` parameter:

```graphql
sort: {
  field: "CREATED_AT",  # Field to sort by
  direction: DESC       # Sort direction (ASC or DESC)
}
```

For games, the available sort fields are:
- `GAME_TIME`
- `CREATED_AT`

For puzzles, the available sort fields are:
- `CREATED_AT`
- `CP`

## Additional Examples

### 6. PGN Availability and Optimization

**Important Note**: PGN is only accessible when querying games directly through the `myGames` query or the `game(id: ID!)` query. When accessing a Game object through a Puzzle's `game` field, the PGN field is omitted to optimize performance.

**Use Case**: When querying games directly, you can choose whether to include the PGN field. The PGN field is large and is only loaded when explicitly requested, improving performance.

**Query without PGN**:
```graphql
{
  myGames(pagination: {offset: 0, limit: 10}) {
    edges {
      node {
        id
        platform
        game_time
        time_control
        rated
        # pgn field is not included, so it won't be loaded from the database
      }
      cursor
    }
    total_count
  }
}
```

**Query with PGN**:
```graphql
{
  myGames(pagination: {offset: 0, limit: 10}) {
    edges {
      node {
        id
        platform
        game_time
        time_control
        rated
        pgn  # This will trigger loading the PGN from the database
      }
      cursor
    }
    total_count
  }
}
```

**cURL Command without PGN**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myGames(pagination: {offset: 0, limit: 10}) { edges { node { id platform game_time time_control rated } cursor } total_count } }"
  }'
```

**cURL Command with PGN**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myGames(pagination: {offset: 0, limit: 10}) { edges { node { id platform game_time time_control rated pgn } cursor } total_count } }"
  }'
```

### 7. Find Puzzles Where You Escaped from a Blunder

**Use Case**: Find puzzles where you made a blunder but managed to escape, to learn from these situations.

**Query**:
```graphql
{
  myPuzzles(
    filter: {
      theme: OWN_BLUNDER_ESCAPED
    },
    pagination: {
      offset: 0,
      limit: 10
    }
  ) {
    edges {
      node {
        id
        theme
        fen
        moves
        prev_cp
        cp
        tags
        game {
          id
          platform
          game_time
          time_control
          rated
          # pgn is only loaded when explicitly requested
        }
      }
      cursor
    }
    page_info {
      has_next_page
      has_previous_page
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzles(filter: { theme: OWN_BLUNDER_ESCAPED }, pagination: { offset: 0, limit: 10 }) { edges { node { id theme fen moves prev_cp cp tags game { id platform game_time time_control rated } } cursor } page_info { has_next_page has_previous_page } total_count } }"
  }'
```

### 7. Find Puzzles with Specific Tags Combination

**Use Case**: Find puzzles that have either "middlegame" or "pin" tags to practice pin tactics or middlegame positions (tags use OR relation).

**Query**:
```graphql
{
  myPuzzles(
    filter: {
      tags: ["middlegame", "pin"]
    },
    pagination: {
      offset: 0,
      limit: 10
    },
    sort: {
      field: "CREATED_AT",
      direction: DESC
    }
  ) {
    edges {
      node {
        id
        theme
        fen
        moves
        prev_cp
        cp
        tags
      }
      cursor
    }
    page_info {
      has_next_page
      has_previous_page
    }
    total_count
  }
}
```

**cURL Command**:
```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/graphql/query \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query { myPuzzles(filter: { tags: [\"middlegame\", \"pin\"] }, pagination: { offset: 0, limit: 10 }, sort: { field: \"CREATED_AT\", direction: DESC }) { edges { node { id theme fen moves prev_cp cp tags } cursor } page_info { has_next_page has_previous_page } total_count } }"
  }'
```

## Schema Reference

Below is a comprehensive reference of the GraphQL schema used in the Chessticize API.

### Main Types

#### Game

```graphql
type Game {
  id: ID!                      # Unique identifier for the game
  user_id: ID!                 # ID of the user who owns the game
  platform: ChessPlatform!     # Chess platform (CHESS_COM or LICHESS)
  chess_username: String!      # Username on the chess platform
  user_color: Color!           # User's color in the game (WHITE or BLACK)
  game_time: Time!             # When the game was played
  pgn: String!                 # PGN notation of the game
  time_control: String!        # Time control format (e.g., "10+0")
  rated: Boolean!              # Whether the game was rated
  url: String                  # URL to the game on the platform (optional)
  white_player: String!        # JSON string with white player info
  black_player: String!        # JSON string with black player info
  winner: Winner!              # Who won the game (WHITE, BLACK, or NONE)
  result: GameResult!          # How the game ended (MATE, RESIGN, DRAW, ABANDONED, OUT_OF_TIME)
  created_at: Time!            # When the game was created in the system
  updated_at: Time!            # When the game was last updated
  puzzles(filter: PuzzleFilter): [Puzzle!]  # Puzzles generated from this game
}
```

#### Puzzle

```graphql
type Puzzle {
  id: ID!                      # Unique identifier for the puzzle
  game_id: ID!                 # ID of the game this puzzle is from
  user_id: ID!                 # ID of the user who owns the puzzle
  game_move: Int!              # Move number in the game
  fen: String!                 # FEN notation of the position
  moves: [String!]!            # List of moves in the puzzle
  prev_cp: Int!                # Centipawn evaluation before the puzzle
  cp: Int!                     # Centipawn evaluation after the puzzle
  theme: PuzzleTheme!          # Theme of the puzzle
  user_color: Color!           # User's color in the puzzle
  puzzle_color: Color!         # Color to move in the puzzle
  zugzwang: Boolean!           # Whether the position is a zugzwang
  tags: [String!]!             # Tags for the puzzle
  created_at: Time!            # When the puzzle was created
  updated_at: Time!            # When the puzzle was last updated
  game: Game                   # The game this puzzle is from
}
```

### Filters

#### GameFilter

```graphql
input GameFilter {
  platform: ChessPlatform      # Filter by chess platform
  chess_username: String       # Filter by chess username
  start_time: Time             # Filter games from this timestamp
  end_time: Time               # Filter games up to this timestamp
  time_control: String         # Filter by time control
  rated: Boolean               # Filter by rated status
  result: GameResult           # Filter by game result
  user_color: Color            # Filter by user's color
}
```

#### PuzzleFilter

```graphql
input PuzzleFilter {
  tags: [String!]              # Filter by tags (OR relation between tags)
  game_start_time: Time        # Filter puzzles from games after this time
  game_end_time: Time          # Filter puzzles from games before this time
  theme: PuzzleTheme           # Filter by puzzle theme
  user_color: Color            # Filter by user's color
  puzzle_color: Color          # Filter by puzzle color
  game_move_min: Int           # Filter by minimum game move
  game_move_max: Int           # Filter by maximum game move
  prev_cp_min: Int             # Filter by minimum previous centipawn
  prev_cp_max: Int             # Filter by maximum previous centipawn
  cp_change_min: Int           # Filter by minimum centipawn change
  cp_change_max: Int           # Filter by maximum centipawn change
}
```

### Pagination and Sorting

```graphql
input OffsetPaginationInput {
  offset: Int                  # Number of items to skip
  limit: Int                   # Maximum number of items to return
}

input SortInput {
  field: String!               # Field to sort by
  direction: SortDirection!    # Sort direction (ASC or DESC)
}
```

### Enums

#### ChessPlatform

```graphql
enum ChessPlatform {
  CHESS_COM                    # chess.com
  LICHESS                      # lichess.org
}
```

#### Color

```graphql
enum Color {
  WHITE
  BLACK
}
```

#### Winner

```graphql
enum Winner {
  WHITE                        # White player won
  BLACK                        # Black player won
  NONE                         # No winner (draw or abandoned)
}
```

#### GameResult

```graphql
enum GameResult {
  MATE                         # Game ended by checkmate
  RESIGN                       # Game ended by resignation
  DRAW                         # Game ended in a draw
  ABANDONED                    # Game was abandoned
  OUT_OF_TIME                  # Game ended by timeout
}
```

#### PuzzleTheme

```graphql
enum PuzzleTheme {
  OPPONENT_MISTAKE_CAUGHT      # You caught your opponent's mistake
  OPPONENT_MISTAKE_MISSED      # You missed your opponent's mistake
  OPPONENT_BLUNDER_CAUGHT      # You caught your opponent's blunder
  OPPONENT_BLUNDER_MISSED      # You missed your opponent's blunder
  OWN_MISTAKE_PUNISHED         # Your mistake was punished
  OWN_MISTAKE_ESCAPED          # You escaped after making a mistake
  OWN_BLUNDER_PUNISHED         # Your blunder was punished
  OWN_BLUNDER_ESCAPED          # You escaped after making a blunder
}
```

#### SortDirection

```graphql
enum SortDirection {
  ASC                          # Ascending order
  DESC                         # Descending order
}
```

#### GameSortField

```graphql
enum GameSortField {
  GAME_TIME                    # Sort by when the game was played
  CREATED_AT                   # Sort by when the game was created
}
```

#### PuzzleSortField

```graphql
enum PuzzleSortField {
  CREATED_AT                   # Sort by when the puzzle was created
  CP                           # Sort by centipawn evaluation
}
```

### Queries

```graphql
type Query {
  # Get a game by ID
  game(id: ID!): Game

  # Get a puzzle by ID
  puzzle(id: ID!): Puzzle

  # Get the current user's games with filtering, pagination, and sorting
  myGames(
    filter: GameFilter
    pagination: OffsetPaginationInput
    sort: SortInput
  ): GameConnection!

  # Get the current user's puzzles with filtering, pagination, and sorting
  myPuzzles(
    filter: PuzzleFilter
    pagination: OffsetPaginationInput
    sort: SortInput
  ): PuzzleConnection!
}
```

### Connection Types (for Pagination)

```graphql
type GameConnection {
  edges: [GameEdge!]!          # List of game edges
  page_info: PageInfo!         # Pagination information
  total_count: Int!            # Total number of games matching the query
}

type GameEdge {
  node: Game!                  # The game object
  cursor: String!              # Opaque cursor for pagination
}

type PuzzleConnection {
  edges: [PuzzleEdge!]!        # List of puzzle edges
  page_info: PageInfo!         # Pagination information
  total_count: Int!            # Total number of puzzles matching the query
}

type PuzzleEdge {
  node: Puzzle!                # The puzzle object
  cursor: String!              # Opaque cursor for pagination
}

type PageInfo {
  has_next_page: Boolean!      # Whether there are more items
  has_previous_page: Boolean!  # Whether there are previous items
  start_cursor: String         # Cursor for the first item in the result
  end_cursor: String           # Cursor for the last item in the result
}
```

You can also use introspection queries with any GraphQL client to explore the schema:

```graphql
{
  __schema {
    types {
      name
      kind
      description
      fields {
        name
        description
        type {
          name
          kind
          ofType {
            name
            kind
          }
        }
      }
    }
  }
}
```
