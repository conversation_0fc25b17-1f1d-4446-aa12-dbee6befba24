# Puzzle Statistics Query and Report Generator

This directory contains tools to fetch comprehensive puzzle statistics from the Chessticize GraphQL API and generate detailed markdown reports.

## Files

- **`puzzle_stats_query.graphql`** - The main GraphQL query that fetches all requested statistics
- **`generate_puzzle_stats_report.py`** - Python script to execute the query and generate a markdown report
- **`test_puzzle_stats.sh`** - Shell script for quick testing with curl
- **`sample_puzzle_stats_report.md`** - Example of what the generated report looks like

## Query Features

The GraphQL query fetches puzzle statistics grouped by 1-week periods for the last 6 months, covering three categories:

### 1. User's Mistakes (Full Analysis)
- **Themes**: `OWN_BLUNDER_PUNISHED`, `OWN_BLUNDER_ESCAPED`, `OWN_MISTAKE_PUNISHED`, `OWN_MISTAKE_ESCAPED`
- **Stats Included**: All available statistics
  - Total count and unique game count
  - Average puzzle move length
  - Theme distribution
  - Tag counts (tactical patterns)
  - User color distribution
  - Game move buckets (opening/middlegame/endgame)
  - Move length distribution

### 2. Missed Opportunities (Full Analysis)
- **Themes**: `OPPONENT_BLUNDER_MISSED`, `OPPONENT_MISTAKE_MISSED`
- **Stats Included**: All available statistics (same as above)

### 3. Caught Opportunities (Summary)
- **Themes**: `OPPONENT_BLUNDER_CAUGHT`, `OPPONENT_MISTAKE_CAUGHT`
- **Stats Included**: Limited to essential metrics
  - Total count and unique game count
  - Average puzzle move length
  - Tag counts only

## Usage

### Option 1: Python Script (Recommended)
```bash
# Set your JWT token
export CHESSTICIZE_JWT_TOKEN="your-jwt-token-here"

# Run the Python script
python3 generate_puzzle_stats_report.py
```

This will generate:
- `puzzle_stats_report.md` - Formatted markdown report
- `puzzle_stats_raw.json` - Raw JSON response for debugging

### Option 2: Shell Script (Quick Test)
```bash
# Run with your JWT token
./test_puzzle_stats.sh "your-jwt-token-here"
```

This will generate:
- `puzzle_stats_result.json` - Raw JSON response

### Option 3: Direct GraphQL Query
Use the query in `puzzle_stats_query.graphql` directly with your GraphQL client, updating the date ranges as needed.

## Date Range

The queries automatically calculate the last 6 months from the current date. The date range is:
- **Start**: 6 months ago from today
- **End**: Current date/time
- **Grouping**: Weekly (1-week periods)

## Authentication

You need a valid JWT token for the Chessticize API. Set it as:
- Environment variable: `CHESSTICIZE_JWT_TOKEN`
- Command line argument for the shell script
- Update the `JWT_TOKEN` variable in the Python script

## Output Format

The markdown report includes:
- Executive summary with weekly statistics tables
- Detailed breakdowns for weeks with data
- Theme distributions, tactical patterns, color analysis
- Game phase analysis (opening/middlegame/endgame)
- Puzzle complexity analysis (move length distribution)

## Requirements

- **Python**: `requests` library for the Python script
- **Shell**: `curl` and `jq` for the shell script
- **API**: Running Chessticize server with GraphQL endpoint

## Example Output

See `sample_puzzle_stats_report.md` for an example of the generated report format.

## Notes

- The query uses the new multiple themes filtering capability
- All statistics are grouped by week for trend analysis
- The report helps identify patterns in chess mistakes and improvement opportunities
- Raw JSON data is preserved for further analysis if needed
