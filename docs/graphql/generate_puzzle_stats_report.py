#!/usr/bin/env python3
"""
Script to fetch puzzle statistics from Chessticize GraphQL API and generate a markdown report.
"""

import json
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, List
import os

# Configuration
API_URL = "http://localhost:8080/api/v1/graphql/query"
JWT_TOKEN = os.getenv("CHESSTICIZE_JWT_TOKEN", "your-jwt-token-here")

def load_graphql_query(filename: str) -> str:
    """Load GraphQL query from file."""
    with open(filename, 'r') as f:
        return f.read()

def execute_graphql_query(query: str, token: str) -> Dict[str, Any]:
    """Execute GraphQL query against the API."""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    payload = {
        "query": query
    }
    
    response = requests.post(API_URL, json=payload, headers=headers)
    response.raise_for_status()
    
    return response.json()

def format_date(date_str: str) -> str:
    """Format ISO date string to readable format."""
    try:
        dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        return dt.strftime("%Y-%m-%d")
    except:
        return date_str

def generate_stats_table(nodes: List[Dict], title: str, include_full_stats: bool = True) -> str:
    """Generate markdown table for statistics."""
    if not nodes:
        return f"### {title}\n\nNo data available for this period.\n\n"
    
    markdown = f"### {title}\n\n"
    
    # Basic stats table
    markdown += "| Week | Total Count | Unique Games | Avg Move Length |\n"
    markdown += "|------|-------------|--------------|----------------|\n"
    
    for node in nodes:
        start_date = format_date(node['start_time'])
        end_date = format_date(node['end_time'])
        stats = node['stats']
        
        week_range = f"{start_date} to {end_date}"
        total_count = stats.get('total_count', 0)
        unique_games = stats.get('unique_game_count', 0)
        avg_move_length = f"{stats.get('average_move_length', 0):.2f}"
        
        markdown += f"| {week_range} | {total_count} | {unique_games} | {avg_move_length} |\n"
    
    if include_full_stats:
        # Add detailed breakdowns for weeks with data
        for node in nodes:
            stats = node['stats']
            if stats.get('total_count', 0) > 0:
                start_date = format_date(node['start_time'])
                end_date = format_date(node['end_time'])
                markdown += f"\n#### Week {start_date} to {end_date} - Detailed Breakdown\n\n"
                
                # Theme counts
                if 'theme_counts' in stats and stats['theme_counts']:
                    markdown += "**Theme Distribution:**\n"
                    for theme in stats['theme_counts']:
                        markdown += f"- {theme['theme'].replace('_', ' ').title()}: {theme['count']}\n"
                    markdown += "\n"
                
                # Tag counts (top 10)
                if 'tag_counts' in stats and stats['tag_counts']:
                    markdown += "**Top Tags:**\n"
                    sorted_tags = sorted(stats['tag_counts'], key=lambda x: x['count'], reverse=True)[:10]
                    for tag in sorted_tags:
                        markdown += f"- {tag['tag']}: {tag['count']}\n"
                    markdown += "\n"
                
                # User color distribution
                if 'user_color_counts' in stats and stats['user_color_counts']:
                    markdown += "**User Color Distribution:**\n"
                    for color in stats['user_color_counts']:
                        markdown += f"- {color['color'].title()}: {color['count']}\n"
                    markdown += "\n"
                
                # Game move buckets
                if 'game_move_buckets' in stats and stats['game_move_buckets']:
                    markdown += "**Game Phase Distribution:**\n"
                    for bucket in stats['game_move_buckets']:
                        if bucket['count'] > 0:
                            markdown += f"- {bucket['name']} (moves {bucket['min_move']}-{bucket['max_move']}): {bucket['count']}\n"
                    markdown += "\n"
                
                # Move length distribution
                if 'move_length_counts' in stats and stats['move_length_counts']:
                    markdown += "**Puzzle Length Distribution:**\n"
                    sorted_lengths = sorted(stats['move_length_counts'], key=lambda x: x['length'])
                    for length in sorted_lengths:
                        if length['count'] > 0:
                            markdown += f"- {length['length']} moves: {length['count']}\n"
                    markdown += "\n"
    
    return markdown

def generate_markdown_report(data: Dict[str, Any]) -> str:
    """Generate complete markdown report from GraphQL response."""
    if 'errors' in data:
        return f"# Error\n\nGraphQL query failed: {data['errors']}\n"
    
    query_data = data.get('data', {})
    
    # Generate report header
    report_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    markdown = f"""# Chess Puzzle Statistics Report
*Generated on {report_date}*

This report shows weekly puzzle statistics for the last 6 months, categorized by mistake types.

## Summary

This analysis covers three key areas:
1. **User's Mistakes**: All puzzles where the user made mistakes (regardless of whether opponent caught them)
2. **Missed Opportunities**: Opponent mistakes that the user failed to catch
3. **Caught Opportunities**: Opponent mistakes that the user successfully caught

---

"""
    
    # Add each section
    user_mistakes = query_data.get('userMistakes', {}).get('nodes', [])
    markdown += generate_stats_table(user_mistakes, "User's Mistakes (Full Analysis)", True)
    
    missed_opportunities = query_data.get('opponentMistakesMissed', {}).get('nodes', [])
    markdown += generate_stats_table(missed_opportunities, "Missed Opportunities (Full Analysis)", True)
    
    caught_opportunities = query_data.get('opponentMistakesCaught', {}).get('nodes', [])
    markdown += generate_stats_table(caught_opportunities, "Caught Opportunities (Summary)", False)
    
    # Add footer
    markdown += """---

## Notes

- **Total Count**: Number of puzzles in each category
- **Unique Games**: Number of distinct games that generated puzzles
- **Avg Move Length**: Average number of moves in puzzles for that week
- **Theme Distribution**: Breakdown by puzzle theme types
- **Top Tags**: Most common tactical patterns
- **User Color Distribution**: Whether user was playing white or black
- **Game Phase Distribution**: Which part of the game the puzzle occurred in
- **Puzzle Length Distribution**: How many moves each puzzle required

*This report helps identify patterns in chess mistakes and improvement opportunities.*
"""
    
    return markdown

def main():
    """Main function to generate the report."""
    try:
        # Load and execute query
        query = load_graphql_query("puzzle_stats_query.graphql")
        
        # Update the date range to be exactly 6 months from now
        end_date = datetime.now()
        start_date = end_date - timedelta(days=180)  # Approximately 6 months
        
        # Replace the hardcoded dates in the query
        query = query.replace("2024-11-30T00:00:00Z", start_date.strftime("%Y-%m-%dT00:00:00Z"))
        query = query.replace("2025-05-30T23:59:59Z", end_date.strftime("%Y-%m-%dT23:59:59Z"))
        
        print("Executing GraphQL query...")
        result = execute_graphql_query(query, JWT_TOKEN)
        
        # Generate markdown report
        print("Generating markdown report...")
        markdown_content = generate_markdown_report(result)
        
        # Save to file
        output_file = "puzzle_stats_report.md"
        with open(output_file, 'w') as f:
            f.write(markdown_content)
        
        print(f"Report generated successfully: {output_file}")
        
        # Also save raw JSON for debugging
        with open("puzzle_stats_raw.json", 'w') as f:
            json.dump(result, f, indent=2)
        
        print("Raw JSON data saved to: puzzle_stats_raw.json")
        
    except Exception as e:
        print(f"Error generating report: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
