query WeeklyPuzzleStatsLast6Months {
  # User's mistakes (both caught and not caught by opponent) - Full stats
  userMistakes: myGroupedPuzzleStats(
    filter: {
      themes: [OWN_BLUNDER_PUNISHED, OWN_BLUNDER_ESCAPED, OWN_MISTAKE_PUNISHED, OWN_MISTAKE_ESCAPED]
      game_start_time: "2024-11-30T00:00:00Z"
      game_end_time: "2025-05-30T23:59:59Z"
    }
    group_unit: WEEK
    group_length: 1
  ) {
    nodes {
      start_time
      end_time
      stats {
        total_count
        unique_game_count
        average_move_length
        theme_counts {
          theme
          count
        }
        tag_counts {
          tag
          count
        }
        user_color_counts {
          color
          count
        }
        game_move_buckets {
          name
          min_move
          max_move
          count
        }
        move_length_counts {
          length
          count
        }
      }
    }
  }
  
  # Opponent's mistakes that user did NOT catch - Full stats
  opponentMistakesMissed: myGroupedPuzzleStats(
    filter: {
      themes: [OPPONENT_BLUNDER_MISSED, OPPONENT_MISTAKE_MISSED]
      game_start_time: "2024-11-30T00:00:00Z"
      game_end_time: "2025-05-30T23:59:59Z"
    }
    group_unit: WEEK
    group_length: 1
  ) {
    nodes {
      start_time
      end_time
      stats {
        total_count
        unique_game_count
        average_move_length
        theme_counts {
          theme
          count
        }
        tag_counts {
          tag
          count
        }
        user_color_counts {
          color
          count
        }
        game_move_buckets {
          name
          min_move
          max_move
          count
        }
        move_length_counts {
          length
          count
        }
      }
    }
  }
  
  # Opponent's mistakes that user DID catch - Limited stats
  opponentMistakesCaught: myGroupedPuzzleStats(
    filter: {
      themes: [OPPONENT_BLUNDER_CAUGHT, OPPONENT_MISTAKE_CAUGHT]
      game_start_time: "2024-11-30T00:00:00Z"
      game_end_time: "2025-05-30T23:59:59Z"
    }
    group_unit: WEEK
    group_length: 1
  ) {
    nodes {
      start_time
      end_time
      stats {
        total_count
        unique_game_count
        average_move_length
        tag_counts {
          tag
          count
        }
      }
    }
  }
}
