# Chess Puzzle Statistics Report
*Generated on 2025-05-30 21:45:00*

This report shows weekly puzzle statistics for the last 6 months, categorized by mistake types.

## Summary

This analysis covers three key areas:
1. **User's Mistakes**: All puzzles where the user made mistakes (regardless of whether opponent caught them)
2. **Missed Opportunities**: Opponent mistakes that the user failed to catch
3. **Caught Opportunities**: Opponent mistakes that the user successfully caught

---

### User's Mistakes (Full Analysis)

| Week | Total Count | Unique Games | Avg Move Length |
|------|-------------|--------------|----------------|
| 2024-12-02 to 2024-12-08 | 15 | 8 | 3.2 |
| 2024-12-09 to 2024-12-15 | 22 | 12 | 2.8 |
| 2024-12-16 to 2024-12-22 | 18 | 10 | 3.5 |
| 2024-12-23 to 2024-12-29 | 12 | 7 | 2.9 |
| 2025-01-06 to 2025-01-12 | 25 | 14 | 3.1 |

#### Week 2024-12-02 to 2024-12-08 - Detailed Breakdown

**Theme Distribution:**
- Own Blunder Punished: 8
- Own Mistake Punished: 5
- Own Blunder Escaped: 2

**Top Tags:**
- fork: 6
- pin: 4
- skewer: 3
- discovered-attack: 2

**User Color Distribution:**
- White: 9
- Black: 6

**Game Phase Distribution:**
- Opening (moves 1-15): 3
- Middlegame (moves 16-40): 10
- Endgame (moves 41+): 2

**Puzzle Length Distribution:**
- 2 moves: 5
- 3 moves: 7
- 4 moves: 2
- 5 moves: 1

### Missed Opportunities (Full Analysis)

| Week | Total Count | Unique Games | Avg Move Length |
|------|-------------|--------------|----------------|
| 2024-12-02 to 2024-12-08 | 8 | 6 | 2.5 |
| 2024-12-09 to 2024-12-15 | 12 | 9 | 2.8 |
| 2024-12-16 to 2024-12-22 | 10 | 7 | 3.1 |
| 2024-12-23 to 2024-12-29 | 6 | 4 | 2.7 |
| 2025-01-06 to 2025-01-12 | 14 | 10 | 2.9 |

#### Week 2024-12-02 to 2024-12-08 - Detailed Breakdown

**Theme Distribution:**
- Opponent Blunder Missed: 5
- Opponent Mistake Missed: 3

**Top Tags:**
- hanging-piece: 4
- fork: 2
- pin: 1
- back-rank-mate: 1

**User Color Distribution:**
- White: 4
- Black: 4

**Game Phase Distribution:**
- Opening (moves 1-15): 1
- Middlegame (moves 16-40): 6
- Endgame (moves 41+): 1

**Puzzle Length Distribution:**
- 2 moves: 6
- 3 moves: 2

### Caught Opportunities (Summary)

| Week | Total Count | Unique Games | Avg Move Length |
|------|-------------|--------------|----------------|
| 2024-12-02 to 2024-12-08 | 18 | 12 | 2.8 |
| 2024-12-09 to 2024-12-15 | 24 | 16 | 3.0 |
| 2024-12-16 to 2024-12-22 | 20 | 13 | 2.9 |
| 2024-12-23 to 2024-12-29 | 15 | 9 | 3.2 |
| 2025-01-06 to 2025-01-12 | 28 | 18 | 2.7 |

---

## Notes

- **Total Count**: Number of puzzles in each category
- **Unique Games**: Number of distinct games that generated puzzles
- **Avg Move Length**: Average number of moves in puzzles for that week
- **Theme Distribution**: Breakdown by puzzle theme types
- **Top Tags**: Most common tactical patterns
- **User Color Distribution**: Whether user was playing white or black
- **Game Phase Distribution**: Which part of the game the puzzle occurred in
- **Puzzle Length Distribution**: How many moves each puzzle required

*This report helps identify patterns in chess mistakes and improvement opportunities.*
