#!/bin/bash

# Test script to execute the puzzle stats query
# Usage: ./test_puzzle_stats.sh [JWT_TOKEN]

set -e

# Configuration
API_URL="http://localhost:8080/api/v1/graphql/query"
JWT_TOKEN="${1:-your-jwt-token-here}"

# Calculate date range (last 6 months)
END_DATE=$(date -u +"%Y-%m-%dT23:59:59Z")
START_DATE=$(date -u -d "6 months ago" +"%Y-%m-%dT00:00:00Z")

echo "Fetching puzzle statistics from $START_DATE to $END_DATE"

# Create the GraphQL query with dynamic dates
QUERY=$(cat << EOF
{
  "query": "query WeeklyPuzzleStatsLast6Months {
    userMistakes: myGroupedPuzzleStats(
      filter: {
        themes: [OWN_BLUNDER_PUNISHED, OWN_BLUNDER_ESCAPED, OWN_MISTAKE_PUNISHED, OWN_MISTAKE_ESCAPED]
        game_start_time: \"$START_DATE\"
        game_end_time: \"$END_DATE\"
      }
      group_unit: WEEK
      group_length: 1
    ) {
      nodes {
        start_time
        end_time
        stats {
          total_count
          unique_game_count
          average_move_length
          theme_counts { theme count }
          tag_counts { tag count }
          user_color_counts { color count }
          game_move_buckets { name min_move max_move count }
          move_length_counts { length count }
        }
      }
    }
    opponentMistakesMissed: myGroupedPuzzleStats(
      filter: {
        themes: [OPPONENT_BLUNDER_MISSED, OPPONENT_MISTAKE_MISSED]
        game_start_time: \"$START_DATE\"
        game_end_time: \"$END_DATE\"
      }
      group_unit: WEEK
      group_length: 1
    ) {
      nodes {
        start_time
        end_time
        stats {
          total_count
          unique_game_count
          average_move_length
          theme_counts { theme count }
          tag_counts { tag count }
          user_color_counts { color count }
          game_move_buckets { name min_move max_move count }
          move_length_counts { length count }
        }
      }
    }
    opponentMistakesCaught: myGroupedPuzzleStats(
      filter: {
        themes: [OPPONENT_BLUNDER_CAUGHT, OPPONENT_MISTAKE_CAUGHT]
        game_start_time: \"$START_DATE\"
        game_end_time: \"$END_DATE\"
      }
      group_unit: WEEK
      group_length: 1
    ) {
      nodes {
        start_time
        end_time
        stats {
          total_count
          unique_game_count
          average_move_length
          tag_counts { tag count }
        }
      }
    }
  }"
}
EOF
)

# Execute the query
echo "Executing GraphQL query..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d "$QUERY" \
  "$API_URL" \
  | jq '.' > puzzle_stats_result.json

echo "Results saved to puzzle_stats_result.json"

# Check if Python script exists and run it
if [ -f "generate_puzzle_stats_report.py" ]; then
    echo "Generating markdown report..."
    CHESSTICIZE_JWT_TOKEN="$JWT_TOKEN" python3 generate_puzzle_stats_report.py
else
    echo "Python report generator not found. Raw JSON saved to puzzle_stats_result.json"
fi
