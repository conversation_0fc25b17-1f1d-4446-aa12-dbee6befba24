/**
 * Authentication hook
 * Provides authentication state and methods
 */

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { 
  login as apiLogin, 
  register as apiRegister, 
  getCurrentUser 
} from '@/lib/api/auth'
import { 
  storeTokens, 
  clearTokens, 
  hasValidAuth, 
  needsTokenRefresh,
  getSessionToken 
} from '@/lib/auth/tokens'
import { refreshAuthToken } from '@/lib/auth/api-client'
import { APP_CONFIG, debugLog } from '@/lib/config'
import { 
  AuthState, 
  LoginRequest, 
  RegisterRequest, 
  User 
} from '@/lib/auth/types'

export function useAuth() {
  const router = useRouter()
  const [state, setState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    error: null,
  })

  // Check authentication status on mount and when tokens change
  const checkAuth = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      if (!hasValidAuth()) {
        setState({
          isAuthenticated: false,
          isLoading: false,
          user: null,
          error: null,
        })
        return
      }

      // Check if token needs refresh
      if (needsTokenRefresh() && getSessionToken()) {
        debugLog('Token needs refresh, attempting refresh...')
        const refreshSuccess = await refreshAuthToken()
        if (!refreshSuccess) {
          debugLog('Token refresh failed, clearing auth')
          clearTokens()
          setState({
            isAuthenticated: false,
            isLoading: false,
            user: null,
            error: 'Session expired',
          })
          return
        }
      }

      // Get current user
      const user = await getCurrentUser()
      setState({
        isAuthenticated: true,
        isLoading: false,
        user,
        error: null,
      })
    } catch (error) {
      debugLog('Auth check failed:', error)
      clearTokens()
      setState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: error instanceof Error ? error.message : 'Authentication failed',
      })
    }
  }, [])

  // Login function
  const login = useCallback(async (request: LoginRequest) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      const response = await apiLogin(request)
      
      // Store tokens
      storeTokens(response.token, response.session_token, 60)
      
      // Get user data
      const user = await getCurrentUser()
      
      setState({
        isAuthenticated: true,
        isLoading: false,
        user,
        error: null,
      })

      // Redirect to dashboard
      router.push(APP_CONFIG.DEFAULT_LOGIN_REDIRECT)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed'
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }))
      throw error
    }
  }, [router])

  // Register function
  const register = useCallback(async (request: RegisterRequest) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      const response = await apiRegister(request)
      
      // Store tokens
      storeTokens(response.token, response.session_token, 60)
      
      // Get user data
      const user = await getCurrentUser()
      
      setState({
        isAuthenticated: true,
        isLoading: false,
        user,
        error: null,
      })

      // Redirect to dashboard
      router.push(APP_CONFIG.DEFAULT_LOGIN_REDIRECT)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed'
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }))
      throw error
    }
  }, [router])

  // Logout function
  const logout = useCallback(() => {
    clearTokens()
    setState({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      error: null,
    })
    router.push(APP_CONFIG.DEFAULT_LOGOUT_REDIRECT)
  }, [router])

  // Clear error function
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  // Check auth on mount
  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  return {
    ...state,
    login,
    register,
    logout,
    clearError,
    checkAuth,
  }
}
