import { useState, useMemo } from 'react'
import { apiPost } from '@/lib/auth/api-client'
import { API_CONFIG } from '@/lib/config'

// TypeScript interfaces

export interface OpponentMistakesData {
  mistakeCaughtPercentageOverTime: { period: string; percentage: number }[]
  averagePuzzleLengthOverTime: { period: string; avgLength: number }[]
  mostMissedTagsPieChart: { [tag: string]: number }
  mostCaughtTagsPieChart: { [tag: string]: number }
  tagMissRatioBarChart: { tag: string; missRatio: number }[]
  missedUserColorDistribution: { [color: string]: number }
  missedGameTimingDistribution: { name: string; min_move: number; max_move: number; count: number }[]
}

export interface MyMistakesData {
  mistakesPerGameOverTime: { period: string; ratio: number; avgPuzzleLength: number }[]
  mistakeTagsPieChart: { [tag: string]: number }
  averagePuzzleLengthOverTime: { period: string; avgLength: number }[]
  mistakeColorDistribution: { [color: string]: number }
  mistakeGameTimingDistribution: { name: string; min_move: number; max_move: number; count: number }[]
  averagePuzzleLength: number
}

// Summary stats for easier access
export interface GameReviewSummary {
  totalOpponentMistakes: number
  totalMyMistakes: number
  totalGamesAnalyzed: number
  currentCatchRate: number
  averagePuzzleLength: number
  mostProblematicPattern: string | null
  strongestPattern: string | null
}

interface UseGameReviewStatsReturn {
  opponentMistakesData: OpponentMistakesData | null
  myMistakesData: MyMistakesData | null
  summary: GameReviewSummary | null
  isLoading: boolean
  errors: string[]
  loadStats: () => Promise<void>
}

export function useGameReviewStats(): UseGameReviewStatsReturn {
  const [opponentMistakesData, setOpponentMistakesData] = useState<OpponentMistakesData | null>(null)
  const [myMistakesData, setMyMistakesData] = useState<MyMistakesData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  const [loadingStates, setLoadingStates] = useState({
    combinedStats: false
  })

  // Calculate summary stats from the loaded data
  const summary: GameReviewSummary | null = useMemo(() => {
    if (!opponentMistakesData || !myMistakesData) return null

    const totalOpponentMistakes = Object.values(opponentMistakesData.mostMissedTagsPieChart).reduce((sum, count) => sum + count, 0) +
                                  Object.values(opponentMistakesData.mostCaughtTagsPieChart).reduce((sum, count) => sum + count, 0)

    const totalMyMistakes = Object.values(myMistakesData.mistakeTagsPieChart).reduce((sum, count) => sum + count, 0)

    const currentCatchRate = opponentMistakesData.mistakeCaughtPercentageOverTime.length > 0 ?
      opponentMistakesData.mistakeCaughtPercentageOverTime[opponentMistakesData.mistakeCaughtPercentageOverTime.length - 1]?.percentage || 0 : 0

    // Find most problematic pattern (highest miss ratio)
    const mostProblematicPattern = opponentMistakesData.tagMissRatioBarChart.length > 0 ?
      opponentMistakesData.tagMissRatioBarChart.reduce((max, current) =>
        current.missRatio > max.missRatio ? current : max
      ).tag : null

    // Find strongest pattern (lowest miss ratio)
    const strongestPattern = opponentMistakesData.tagMissRatioBarChart.length > 0 ?
      opponentMistakesData.tagMissRatioBarChart.reduce((min, current) =>
        current.missRatio < min.missRatio ? current : min
      ).tag : null

    return {
      totalOpponentMistakes,
      totalMyMistakes,
      totalGamesAnalyzed: totalOpponentMistakes + totalMyMistakes, // Approximation
      currentCatchRate,
      averagePuzzleLength: myMistakesData.averagePuzzleLength,
      mostProblematicPattern,
      strongestPattern
    }
  }, [opponentMistakesData, myMistakesData])

  // Date range helpers
  const get6MonthsAgo = () => {
    const date = new Date()
    date.setMonth(date.getMonth() - 6)
    return date.toISOString()
  }

  const get3MonthsAgo = () => {
    const date = new Date()
    date.setMonth(date.getMonth() - 3)
    return date.toISOString()
  }

  const getToday = () => {
    const date = new Date()
    date.setHours(23, 59, 59, 999)
    return date.toISOString()
  }

  // Group data by weekly periods (7-day intervals)
  const groupByWeeklyPeriods = (puzzles: any[]) => {
    const groups: { [key: string]: any[] } = {}

    puzzles.forEach(puzzle => {
      // Add null safety checks
      if (!puzzle?.game?.game_time) {
        console.warn('Puzzle missing game or game_time:', puzzle)
        return
      }

      const gameTime = new Date(puzzle.game.game_time)
      if (isNaN(gameTime.getTime())) {
        console.warn('Invalid game_time:', puzzle.game.game_time)
        return
      }

      const periodStart = new Date(gameTime)
      // Get start of week (Sunday)
      periodStart.setDate(periodStart.getDate() - periodStart.getDay())
      periodStart.setHours(0, 0, 0, 0)

      const key = periodStart.toISOString().split('T')[0]
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(puzzle)
    })

    return groups
  }

  // Calculate average puzzle length
  const calculateAveragePuzzleLength = (puzzles: any[]) => {
    if (!puzzles || puzzles.length === 0) return 0

    const totalMoves = puzzles.reduce((sum, puzzle) => {
      // Add null safety checks for moves array
      if (!puzzle?.moves || !Array.isArray(puzzle.moves)) {
        return sum
      }
      return sum + puzzle.moves.length
    }, 0)

    return puzzles.length > 0 ? totalMoves / puzzles.length : 0
  }

  // GraphQL client function using existing auth infrastructure
  const makeGraphQLRequest = async (query: string, variables: any = {}) => {
    try {
      const response = await apiPost(API_CONFIG.ENDPOINTS.GRAPHQL, {
        query,
        variables
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new Error(`GraphQL request failed: ${response.status} ${response.statusText}. Response: ${errorText}`)
      }

      const data = await response.json()

      if (data.errors) {
        const errorDetails = data.errors.map((e: any) => {
          const message = e.message || 'Unknown error'
          const path = e.path ? ` (path: ${e.path.join('.')})` : ''
          return `${message}${path}`
        }).join(', ')
        throw new Error(`GraphQL errors: ${errorDetails}`)
      }

      return data.data
    } catch (error) {
      console.error('GraphQL request error:', {
        query: query.substring(0, 200) + '...',
        variables,
        error
      })
      throw error
    }
  }

  // Helper to add error without replacing existing ones
  const addError = (error: string) => {
    setErrors(prev => {
      if (!prev.includes(error)) {
        return [...prev, error]
      }
      return prev
    })
  }

  // Helper to update loading state for individual queries
  const updateLoadingState = (key: keyof typeof loadingStates, loading: boolean) => {
    setLoadingStates(prev => ({ ...prev, [key]: loading }))

    // Update overall loading state
    setIsLoading(Object.values({ ...loadingStates, [key]: loading }).some(state => state))
  }

  // Combined GraphQL query for all statistics
  const fetchAllStats = async () => {
    console.log('Fetching all game review statistics...')
    updateLoadingState('combinedStats', true)

    const startDate6Months = get6MonthsAgo()
    const startDate3Months = get3MonthsAgo()
    const endDate = getToday()

    try {
      // Single comprehensive query combining opponent mistakes and my mistakes data
      const combinedStatsQuery = `
        query CombinedGameReviewStats($startDate6M: Time!, $startDate3M: Time!, $endDate: Time!) {
          # Opponent mistakes time series data (6 months, 1 week intervals)
          opponentMistakesTimeSeries: myGroupedPuzzleStats(
            filter: {
              game_start_time: $startDate6M,
              game_end_time: $endDate,
              themes: [OPPONENT_BLUNDER_MISSED, OPPONENT_MISTAKE_MISSED, OPPONENT_BLUNDER_CAUGHT, OPPONENT_MISTAKE_CAUGHT]
            },
            group_unit: WEEK,
            group_length: 1
          ) {
            nodes {
              start_time
              end_time
              stats {
                theme_counts {
                  theme
                  count
                }
                average_move_length
              }
            }
          }

          # Opponent mistakes - missed (3 months)
          opponentMistakesMissed3M: myPuzzleStats(
            filter: {
              game_start_time: $startDate3M,
              themes: [OPPONENT_BLUNDER_MISSED, OPPONENT_MISTAKE_MISSED]
            }
          ) {
            tag_counts {
              tag
              count
            }
            game_move_buckets {
              name
              min_move
              max_move
              count
            }
            user_color_counts {
              color
              count
            }
          }

          # Opponent mistakes - caught (3 months)
          opponentMistakesCaught3M: myPuzzleStats(
            filter: {
              game_start_time: $startDate3M,
              themes: [OPPONENT_BLUNDER_CAUGHT, OPPONENT_MISTAKE_CAUGHT]
            }
          ) {
            tag_counts {
              tag
              count
            }
          }

          # My mistakes time series data (6 months, 1 week intervals)
          myMistakesTimeSeries: myGroupedPuzzleStats(
            filter: {
              game_start_time: $startDate6M,
              game_end_time: $endDate,
              themes: [OWN_BLUNDER_PUNISHED, OWN_BLUNDER_ESCAPED, OWN_MISTAKE_PUNISHED, OWN_MISTAKE_ESCAPED]
            },
            group_unit: WEEK,
            group_length: 1,
            pagination: { limit: 100 }
          ) {
            nodes {
              start_time
              end_time
              stats {
                total_count
                unique_game_count
                average_move_length
              }
            }
          }

          # My mistakes tag analysis (3 months)
          myMistakes3M: myPuzzleStats(
            filter: {
              game_start_time: $startDate3M,
              themes: [OWN_BLUNDER_PUNISHED, OWN_BLUNDER_ESCAPED, OWN_MISTAKE_PUNISHED, OWN_MISTAKE_ESCAPED]
            }
          ) {
            tag_counts {
              tag
              count
            }
            game_move_buckets {
              name
              min_move
              max_move
              count
            }
            user_color_counts {
              color
              count
            }
            total_count
            unique_game_count
            average_move_length
          }
        }
      `

      const data = await makeGraphQLRequest(combinedStatsQuery, {
        startDate6M: startDate6Months,
        startDate3M: startDate3Months,
        endDate
      })

      console.log('Combined stats data:', data)

      // Helper function to extract theme count from theme_counts array
      const getThemeCount = (themeCounts: any[], theme: string): number => {
        const themeData = themeCounts?.find((tc: any) => tc.theme === theme)
        return themeData?.count || 0
      }

      // ===== PROCESS OPPONENT MISTAKES DATA =====
      const opponentTimeSeries = data?.opponentMistakesTimeSeries?.nodes || []

      // Calculate caught percentage over time using theme_counts from grouped data
      const mistakeCaughtPercentageOverTime = opponentTimeSeries.map((node: any) => {
        const periodKey = node.start_time.split('T')[0]
        const missedCount = getThemeCount(node.stats.theme_counts, 'OPPONENT_MISTAKE_MISSED') +
                           getThemeCount(node.stats.theme_counts, 'OPPONENT_BLUNDER_MISSED')
        const caughtCount = getThemeCount(node.stats.theme_counts, 'OPPONENT_MISTAKE_CAUGHT') +
                           getThemeCount(node.stats.theme_counts, 'OPPONENT_BLUNDER_CAUGHT')
        const totalCount = missedCount + caughtCount
        const percentage = totalCount > 0 ? (caughtCount / totalCount) * 100 : 0

        return {
          period: periodKey,
          percentage
        }
      })

      // Extract average puzzle length over time from grouped data
      const averagePuzzleLengthOverTime = opponentTimeSeries.map((node: any) => {
        const periodKey = node.start_time.split('T')[0]
        const avgLength = node.stats.average_move_length || 0

        return {
          period: periodKey,
          avgLength
        }
      })

      // Process 3-month pie chart data
      const missedStats = data?.opponentMistakesMissed3M
      const caughtStats = data?.opponentMistakesCaught3M

      // Create pie chart for most missed tags
      const missedTagCounts = missedStats?.tag_counts || []
      const totalMissed = missedTagCounts.reduce((sum: number, tagCount: any) => sum + tagCount.count, 0)
      const mostMissedTagsPieChart: { [tag: string]: number } = {}
      missedTagCounts.forEach((tagCount: any) => {
        if (tagCount.tag && tagCount.count) {
          mostMissedTagsPieChart[tagCount.tag] = totalMissed > 0 ? (tagCount.count / totalMissed) * 100 : 0
        }
      })

      // Create pie chart for most caught tags
      const caughtTagCounts = caughtStats?.tag_counts || []
      const totalCaught = caughtTagCounts.reduce((sum: number, tagCount: any) => sum + tagCount.count, 0)
      const mostCaughtTagsPieChart: { [tag: string]: number } = {}
      caughtTagCounts.forEach((tagCount: any) => {
        if (tagCount.tag && tagCount.count) {
          mostCaughtTagsPieChart[tagCount.tag] = totalCaught > 0 ? (tagCount.count / totalCaught) * 100 : 0
        }
      })

      // Calculate miss ratio per tag for bar chart
      const allTags = new Set([
        ...missedTagCounts.map((tc: any) => tc.tag),
        ...caughtTagCounts.map((tc: any) => tc.tag)
      ])

      const tagMissRatioBarChart = Array.from(allTags).map(tag => {
        const missedCount = missedTagCounts.find((tc: any) => tc.tag === tag)?.count || 0
        const caughtCount = caughtTagCounts.find((tc: any) => tc.tag === tag)?.count || 0
        const totalCount = missedCount + caughtCount
        const missRatio = totalCount > 0 ? (missedCount / totalCount) * 100 : 0

        return {
          tag: tag as string,
          missRatio
        }
      }).filter(item => item.tag) // Filter out any undefined tags

      // Process opponent mistakes missed user color distribution
      const missedColorCounts = missedStats?.user_color_counts || []
      const totalMissedColors = missedColorCounts.reduce((sum: number, colorCount: any) => sum + colorCount.count, 0)
      const missedUserColorDistribution: { [color: string]: number } = {}
      missedColorCounts.forEach((colorCount: any) => {
        if (colorCount.color && colorCount.count) {
          missedUserColorDistribution[colorCount.color] = totalMissedColors > 0 ? (colorCount.count / totalMissedColors) * 100 : 0
        }
      })

      // Process opponent mistakes missed game timing distribution
      const missedGameTimingDistribution = missedStats?.game_move_buckets || []

      const opponentMistakesResult: OpponentMistakesData = {
        mistakeCaughtPercentageOverTime,
        averagePuzzleLengthOverTime,
        mostMissedTagsPieChart,
        mostCaughtTagsPieChart,
        tagMissRatioBarChart,
        missedUserColorDistribution,
        missedGameTimingDistribution
      }

      setOpponentMistakesData(opponentMistakesResult)
      console.log('Processed opponent mistakes data:', opponentMistakesResult)

      // ===== PROCESS MY MISTAKES DATA =====
      const myMistakesTimeSeries = data?.myMistakesTimeSeries?.nodes || []

      // Calculate mistakes per game ratio over time using server-grouped data
      const mistakesPerGameOverTime = myMistakesTimeSeries.map((node: any) => {
        const periodKey = node.start_time.split('T')[0]
        const mistakeCount = node.stats.total_count
        const gameCount = node.stats.unique_game_count || 1
        const ratio = mistakeCount / gameCount
        const avgPuzzleLength = node.stats.average_move_length || 0

        return {
          period: periodKey,
          ratio,
          avgPuzzleLength
        }
      })

      // Process mistake tags pie chart from aggregated stats
      const myMistakes3M = data?.myMistakes3M
      const tagCounts = myMistakes3M?.tag_counts || []

      // Convert tag counts to percentages
      const totalTags = tagCounts.reduce((sum: number, tagCount: any) => sum + tagCount.count, 0)
      const mistakeTagsPieChart: { [tag: string]: number } = {}
      tagCounts.forEach((tagCount: any) => {
        if (tagCount.tag && tagCount.count) {
          mistakeTagsPieChart[tagCount.tag] = totalTags > 0 ? (tagCount.count / totalTags) * 100 : 0
        }
      })

      // Process average puzzle length over time using sample data
      const samplePuzzles = data?.mistakesSample?.edges?.map((edge: any) => edge?.node).filter(Boolean) || []
      const puzzlesByPeriod = groupByWeeklyPeriods(samplePuzzles)
      const myMistakesAveragePuzzleLengthOverTime = Object.keys(puzzlesByPeriod).map(period => {
        const avgLength = calculateAveragePuzzleLength(puzzlesByPeriod[period])
        return { period, avgLength }
      })

      // Process my mistakes color distribution
      const myMistakeColorCounts = myMistakes3M?.user_color_counts || []
      const totalMyMistakeColors = myMistakeColorCounts.reduce((sum: number, colorCount: any) => sum + colorCount.count, 0)
      const mistakeColorDistribution: { [color: string]: number } = {}
      myMistakeColorCounts.forEach((colorCount: any) => {
        if (colorCount.color && colorCount.count) {
          mistakeColorDistribution[colorCount.color] = totalMyMistakeColors > 0 ? (colorCount.count / totalMyMistakeColors) * 100 : 0
        }
      })

      // Process my mistakes game timing distribution
      const mistakeGameTimingDistribution = myMistakes3M?.game_move_buckets || []

      // Get overall average puzzle length for 3 months
      const averagePuzzleLength = myMistakes3M?.average_move_length || 0

      const myMistakesResult: MyMistakesData = {
        mistakesPerGameOverTime,
        mistakeTagsPieChart,
        averagePuzzleLengthOverTime: myMistakesAveragePuzzleLengthOverTime,
        mistakeColorDistribution,
        mistakeGameTimingDistribution,
        averagePuzzleLength
      }

      setMyMistakesData(myMistakesResult)
      console.log('Processed my mistakes data:', myMistakesResult)

    } catch (error) {
      console.error('Error fetching combined stats:', error)
      addError(`Combined stats: ${error instanceof Error ? error.message : 'Failed to load'}`)
    } finally {
      updateLoadingState('combinedStats', false)
    }
  }

  // Load all statistics with single query
  const loadStats = async () => {
    console.log('Loading all statistics...')

    // Clear previous errors
    setErrors([])

    // Call the single combined function
    await fetchAllStats()

    console.log('All statistics loading completed')
  }

  return {
    opponentMistakesData,
    myMistakesData,
    summary,
    isLoading,
    errors,
    loadStats
  }
}
