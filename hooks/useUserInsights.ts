"use client"

import { useMemo } from 'react'
import { useUserContext } from '@/hooks/useUserContext'
import { generateUserInsights, UserInsights } from '@/lib/insights/analysis'

export interface UseUserInsightsReturn {
  insights: UserInsights | null
  isLoading: boolean
  error: string | null
  hasData: boolean
}

/**
 * Hook for processing user data into actionable insights
 */
export function useUserInsights(): UseUserInsightsReturn {
  const { user, isLoading, error } = useUserContext()
  
  const insights = useMemo(() => {
    if (!user) return null
    
    try {
      return generateUserInsights(user)
    } catch (err) {
      console.error('Error generating user insights:', err)
      return null
    }
  }, [user])
  
  const hasData = useMemo(() => {
    if (!user) return false
    
    const hasDailyStats = user.daily_stats && user.daily_stats.length > 0
    const hasSprintStats = user.sprint_daily_stats && user.sprint_daily_stats.length > 0
    
    return hasDailyStats || hasSprintStats
  }, [user])
  
  return {
    insights,
    isLoading,
    error,
    hasData
  }
}
