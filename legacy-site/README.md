# Chess Tactics Performance Visualization

A React-based web application that visualizes your chess tactics performance over time. Track how well you handle different tactical patterns in your games, including mate in one, forks, and pins.

## Package Manager

This project uses **pnpm** as the package manager for better performance and disk space efficiency. Make sure you have pnpm installed before proceeding.

## Installation

1. **Prerequisites**:
   - Node.js (v14 or higher)
   - pnpm (install with `npm install -g pnpm`)

2. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/chess-tactics-visualization.git
   cd chess-tactics-visualization
   ```

3. **Install dependencies**:
   ```bash
   pnpm install
   ```

## Getting Started

1. **Prepare your data**:
   - Place your chess games data in `public/games.json`
   - Format should be:
     ```json
     {
       "games": [
         {
           "end_time": 1234567890,
           "puzzles": [
             {
               "theme": "own_mistake_punished",
               "tags": ["fork", "pin"]
             }
           ]
         }
       ]
     }
     ```

2. **Start the development server**:
   ```bash
   pnpm start
   ```
   This will open the application in your default browser at `http://localhost:3000`

3. **Build for production**:
   ```bash
   pnpm build
   ```
   This creates an optimized production build in the `build` folder

4. **Run tests**:
   ```bash
   pnpm test
   ```

5. **Serve production build locally**:
   ```bash
   pnpm serve
   ```
   This serves the production build locally for testing

## Features

- **Interactive Visualization**: Zoom and pan through your tactical performance history
- **Multiple Tactical Patterns**: Track three key tactical patterns:
  - Mate in One
  - Forks
  - Pins

- **Three Performance Categories**:
  - Own Mistakes: Tactics you missed in your games
  - Opponent Mistakes Caught: Tactical opportunities you successfully exploited
  - Opponent Mistakes Missed: Tactical opportunities you failed to capitalize on

- **Advanced Analytics**:
  - 100-game moving average for smooth trend visualization
  - 30-day window for calculating occurrence rates
  - Adaptive time axis that adjusts detail based on zoom level

## Usage

1. **Filtering Tactics**:
   - Use the checkboxes at the top to show/hide specific tactical patterns
   - All patterns are shown by default

2. **Interacting with Graphs**:
   - **Zoom**: Use mouse wheel or pinch gesture
   - **Pan**: Click and drag after zooming in
   - **Reset**: Click "Reset Zoom" button to return to full view
   - **Details**: Hover over data points to see exact dates and percentages

3. **Reading the Graphs**:
   - Y-axis shows the occurrence rate as a percentage
   - X-axis shows the timeline with adaptive date formatting
   - Each line represents a different tactical pattern
   - Colors indicate different types of tactics and whether they were caught or missed

## Technical Details

Built with:
- React
- Chart.js with zoom plugin
- Material-UI for styling
- Date-fns for date handling

## Data Processing

- Processes chess game data from games.json
- Calculates moving averages over 100-game windows
- Filters and normalizes tactical pattern tags
- Dynamically adjusts visualization range based on data availability

## Future Improvements

- [ ] Add more tactical patterns (discovered forks, double attacks, etc.)
- [ ] Implement custom date range selection
- [ ] Add statistical analysis of improvement trends
- [ ] Export functionality for data and visualizations 
