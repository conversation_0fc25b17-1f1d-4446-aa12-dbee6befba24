// Use require if you installed node-fetch v2 or are using CommonJS
// const fetch = require('node-fetch'); 
// Or use import if your project is set up for ES Modules and Node >= 18
// import fetch from 'node-fetch'; // or rely on native fetch

// Assuming CommonJS for broader compatibility. Use native fetch if available.
const fetch = global.fetch || require('node-fetch');
const { Chess } = require('chess.js');

// --- Configuration ---
const API_URL = process.env.API_URL || "http://localhost:8000"; // Allow overriding API URL too
const TEST_USERNAME = process.env.TEST_USERNAME; // Read from environment
const TEST_PASSWORD = process.env.TEST_PASSWORD; // Read from environment
// ---------------------

let authToken = null;
let allPuzzles = [];

// Determine if credentials are provided
const credentialsProvided = TEST_USERNAME && TEST_PASSWORD;

// Conditionally describe the suite: Skip if no credentials
const describeOrSkip = credentialsProvided ? describe : describe.skip;

describeOrSkip('Puzzle Logic Validation (Requires Credentials)', () => {

  // 1. Log in and fetch puzzles before running tests
  beforeAll(async () => {
     // Add this check at the beginning of beforeAll
     if (!credentialsProvided) {
        // This shouldn't be reached if using describe.skip, but good failsafe
        console.warn("Skipping puzzle validation tests as TEST_USERNAME or TEST_PASSWORD environment variables are not set.");
        return; 
     }

    // Login
    try {
      console.log(`Attempting login for user: ${TEST_USERNAME}...`);
      const loginResponse = await fetch(`${API_URL}/api/login`, { // Use API_URL variable
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: TEST_USERNAME, password: TEST_PASSWORD })
      });

      if (!loginResponse.ok) {
        const errorText = await loginResponse.text();
        throw new Error(`Login failed: ${loginResponse.status} - ${errorText}`);
      }

      const loginData = await loginResponse.json();
      authToken = loginData.token;
      console.log("Login successful, token obtained.");

      if (!authToken) {
        throw new Error("Authentication token not received.");
      }

    } catch (error) {
      console.error("Error during login setup:", error);
      throw error; // Fail the test suite if login fails
    }

    // Fetch Games/Puzzles
    try {
      console.log("Fetching games...");
      const gamesResponse = await fetch(`${API_URL}/api/users/me/games`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (!gamesResponse.ok) {
        const errorText = await gamesResponse.text();
        throw new Error(`Fetching games failed: ${gamesResponse.status} - ${errorText}`);
      }

      const gamesData = await gamesResponse.json();
      
      // --- MODIFIED CHECK --- 
      // Check if the response is an object and contains a 'games' array
      if (typeof gamesData !== 'object' || gamesData === null || !Array.isArray(gamesData.games)) {
        console.warn('Received unexpected data structure:', gamesData);
        throw new Error('Expected an object with a `games` array from /api/users/me/games');
      }
      // --- END MODIFIED CHECK --- 

      // Flatten all puzzles from the 'games' array within the response object
      const actualGamesArray = gamesData.games;
      allPuzzles = actualGamesArray.flatMap(game => 
          (game && Array.isArray(game.puzzles)) ? game.puzzles : []
      );
      console.log(`Fetched ${actualGamesArray.length} games containing ${allPuzzles.length} puzzles.`);

      if (allPuzzles.length === 0) {
         console.warn("No puzzles found to test.");
      }

    } catch (error) {
      console.error("Error fetching puzzles:", error);
      throw error; // Fail the test suite if fetching puzzles fails
    }
  }, 30000); // Increase timeout for login + fetch

  // 2. Test each fetched puzzle
  test('All fetched puzzles should be solvable by following their moves', () => {
      // Check if puzzles were actually fetched (handles API errors or empty results)
      if (allPuzzles.length === 0) {
        console.warn("Skipping puzzle solvability checks because no puzzles were fetched or found.");
        // You could choose to fail here if desired: expect(allPuzzles.length).toBeGreaterThan(0);
        return; // Or simply return to pass the test if no puzzles is acceptable
      }

      // Iterate through the puzzles fetched in beforeAll
      for (const puzzle of allPuzzles) {
          // Basic validation of puzzle structure
          const fen = puzzle.fen || puzzle.FEN;
          const moves = puzzle.moves;
          const puzzleId = puzzle.id || puzzle.puzzleid || 'UnknownID'; // Try common ID fields

          // Add inner expects for structure validation per puzzle
          expect(fen).toBeTruthy();
          expect(moves).toBeInstanceOf(Array);
          expect(moves.length).toBeGreaterThan(0);

          const game = new Chess(fen);

          // Simulate each move from the puzzle data
          for (let i = 0; i < moves.length; i++) {
            const moveString = moves[i];
            const from = moveString.slice(0, 2);
            const to = moveString.slice(2, 4);
            const promotion = moveString.length > 4 ? moveString[4].toLowerCase() : undefined;

            let moveResult;
            try {
              moveResult = game.move({ from, to, promotion });
            } catch (e) {
               // Catch errors from chess.js if the move object is fundamentally invalid
               console.error(`Error executing move ${i+1} ('${moveString}') for Puzzle ${puzzleId} FEN: ${fen}`, e);
               moveResult = null; 
            }

            // Assertion: The move should be valid according to chess.js
            // Use fail() or add specific message to make failures clearer
            if (moveResult === null) {
                const errorMessage = `Invalid move encountered: Move ${i+1} ('${moveString}') in Puzzle ${puzzleId}. Current FEN: ${game.fen()}`;
                console.error(errorMessage);
                // Throw or use fail() to ensure the test fails clearly
                throw new Error(errorMessage); 
            }
             // expect(moveResult).not.toBeNull(); // Keep this as a backup assertion
          }
          // If the inner loop completes without throwing, this specific puzzle is solvable.
      }
      // If the outer loop completes without throwing, all puzzles are solvable.
  });
}); // End of describeOrSkip block

// Optional: Add a dummy test that runs if credentials are NOT provided
if (!credentialsProvided) {
  test('Puzzle validation tests skipped due to missing credentials', () => {
    console.warn("Set TEST_USERNAME and TEST_PASSWORD environment variables to run puzzle validation tests.");
    expect(true).toBe(true); // A simple passing test to show something ran
  });
} 