<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chess Player Strength Visualization</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .chart-container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .tag-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .tag-checkbox {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chess Player Strength Visualization</h1>
        <div class="controls">
            <div>
                <button onclick="zoomIn()">Zoom In</button>
                <button onclick="zoomOut()">Zoom Out</button>
            </div>
            <div class="tag-selector" id="tagSelector">
                <!-- Tags will be populated dynamically -->
            </div>
        </div>
        <div class="chart-container">
            <canvas id="themeChart"></canvas>
        </div>
    </div>

    <script>
        // Load and process the data
        async function loadData() {
            const response = await fetch('games.json');
            const data = await response.json();
            return data;
        }

        // Process data for visualization
        function processData(data) {
            const themes = {
                'own_mistake_punished': [],
                'own_mistake_escaped': [],
                'opponent_mistake_caught': [],
                'opponent_mistake_missed': []
            };

            // Get all unique tags
            const allTags = new Set();
            data.games.forEach(game => {
                game.puzzles.forEach(puzzle => {
                    puzzle.tags.forEach(tag => allTags.add(tag));
                });
            });

            // Process each game
            data.games.forEach(game => {
                game.puzzles.forEach(puzzle => {
                    if (themes[puzzle.theme]) {
                        themes[puzzle.theme].push({
                            timestamp: game.end_time,
                            cp: puzzle.cp,
                            tags: puzzle.tags
                        });
                    }
                });
            });

            return { themes, allTags };
        }

        // Create the chart
        function createChart(data) {
            const ctx = document.getElementById('themeChart').getContext('2d');
            const { themes } = data;

            const datasets = Object.entries(themes).map(([theme, points]) => ({
                label: theme.replace(/_/g, ' '),
                data: points.map(point => ({
                    x: point.timestamp * 1000, // Convert to milliseconds
                    y: point.cp
                })),
                borderColor: getThemeColor(theme),
                backgroundColor: getThemeColor(theme, 0.2),
                fill: true
            }));

            return new Chart(ctx, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day'
                            },
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Centipawns'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Player Strength by Theme'
                        }
                    }
                }
            });
        }

        // Get color for each theme
        function getThemeColor(theme, alpha = 1) {
            const colors = {
                'own_mistake_punished': `rgba(255, 99, 132, ${alpha})`,
                'own_mistake_escaped': `rgba(54, 162, 235, ${alpha})`,
                'opponent_mistake_caught': `rgba(75, 192, 192, ${alpha})`,
                'opponent_mistake_missed': `rgba(153, 102, 255, ${alpha})`
            };
            return colors[theme] || `rgba(0, 0, 0, ${alpha})`;
        }

        // Create tag checkboxes
        function createTagCheckboxes(tags) {
            const container = document.getElementById('tagSelector');
            tags.forEach(tag => {
                const div = document.createElement('div');
                div.className = 'tag-checkbox';
                div.innerHTML = `
                    <input type="checkbox" id="${tag}" value="${tag}" checked>
                    <label for="${tag}">${tag}</label>
                `;
                container.appendChild(div);
            });
        }

        // Zoom functions
        function zoomIn() {
            chart.zoom(1.1);
        }

        function zoomOut() {
            chart.zoom(0.9);
        }

        // Initialize
        let chart;
        async function init() {
            const data = await loadData();
            const processedData = processData(data);
            chart = createChart(processedData);
            createTagCheckboxes(processedData.allTags);

            // Add event listeners to tag checkboxes
            document.querySelectorAll('.tag-checkbox input').forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    const selectedTags = Array.from(document.querySelectorAll('.tag-checkbox input:checked'))
                        .map(input => input.value);
                    
                    chart.data.datasets.forEach(dataset => {
                        dataset.hidden = !selectedTags.some(tag => 
                            dataset.data.some(point => point.tags.includes(tag))
                        );
                    });
                    chart.update();
                });
            });
        }

        init();
    </script>
</body>
</html> 