{"name": "chess-visualization", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "chart.js": "^4.4.1", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-zoom": "^2.2.0", "chess.js": "^1.0.0-beta.6", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-chessboard": "^4.7.2", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "serve": "^14.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "jest", "eject": "react-scripts eject", "heroku-postbuild": "npm run build", "serve": "serve -s build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"dotenv": "^16.5.0", "node-fetch": "^2.7.0"}, "jest": {"setupFiles": ["./jest.setup.js"]}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.31"}}