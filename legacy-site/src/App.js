import React, { useState, useEffect } from "react";
import PuzzlesView from "./PuzzlesView";
import './components/ChessPuzzle.css';
import {
  storeTokens,
  clearTokens,
  hasValidAuth
} from './utils/tokenUtils';
import { apiPost, apiGet } from './services/apiService';

function App() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginError, setLoginError] = useState(null);
  const [showRegister, setShowRegister] = useState(false);
  const [registerEmail, setRegisterEmail] = useState("");
  const [registerPassword, setRegisterPassword] = useState("");
  const [registerRepeatPassword, setRegisterRepeatPassword] = useState("");
  const [registerInvitationCode, setRegisterInvitationCode] = useState("");
  const [registerError, setRegisterError] = useState(null);
  const [registerSuccess, setRegisterSuccess] = useState(null);
  const [showChessProfileModal, setShowChessProfileModal] = useState(false);
  const [chessComUsername, setChessComUsername] = useState("");
  const [lichessUsername, setLichessUsername] = useState("");
  const [chessComLocked, setChessComLocked] = useState(false);
  const [lichessLocked, setLichessLocked] = useState(false);
  const [profileError, setProfileError] = useState(null);
  const [profileLoading, setProfileLoading] = useState(false);

  // Check for existing authentication on mount
  useEffect(() => {
    if (hasValidAuth()) {
      setIsLoggedIn(true);
    }
  }, []);

  const handleLogin = async (e) => {
    e.preventDefault();
    try {
      const response = await apiPost('/auth/login',
        { email: username, password },
        { skipAuth: true }
      );

      if (!response.ok) {
        throw new Error("Login failed. Please check your credentials.");
      }

      const data = await response.json();

      // Store both auth token and session token
      storeTokens(data.token, data.session_token, 60); // 60 minutes for JWT

      setIsLoggedIn(true);
      setLoginError(null);
      checkChessProfiles();
    } catch (error) {
      if (error.message === 'AUTHENTICATION_FAILED') {
        setLoginError("Session expired. Please log in again.");
      } else {
        setLoginError(error.message);
      }
    }
  };

  const handleLogout = () => {
    clearTokens();
    setIsLoggedIn(false);
    setUsername("");
    setPassword("");
  };

  const handleRegister = async (e) => {
    e.preventDefault();
    setRegisterError(null);
    setRegisterSuccess(null);
    if (registerPassword !== registerRepeatPassword) {
      setRegisterError("Passwords do not match.");
      return;
    }
    if (!registerEmail || !registerPassword || !registerInvitationCode) {
      setRegisterError("All fields are required.");
      return;
    }
    try {
      const response = await apiPost('/auth/register', {
        email: registerEmail,
        password: registerPassword,
        invitation_code: registerInvitationCode
      }, { skipAuth: true });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Registration failed:", response.status, errorText);
        let errorData = {};
        try { errorData = JSON.parse(errorText); } catch {}
        setRegisterError(errorData.message || errorText || "Registration failed. Please check your details and invitation code.");
        return;
      }

      const data = await response.json();
      setRegisterSuccess("Registration successful! Logging you in...");

      // Store both auth token and session token
      storeTokens(data.token, data.session_token, 60); // 60 minutes for JWT

      setUsername(registerEmail);
      setPassword(registerPassword);
      setIsLoggedIn(true);
      setLoginError(null);
      checkChessProfiles();
    } catch (error) {
      if (error.message === 'AUTHENTICATION_FAILED') {
        setRegisterError("Session expired. Please try again.");
      } else {
        setRegisterError(error.message || "Registration failed. Please try again.");
      }
    }
  };

  // Helper to check chess profiles after login/registration
  const checkChessProfiles = async () => {
    try {
      setProfileLoading(true);
      setProfileError(null);

      const response = await apiGet('/users/me/chess-profiles');

      if (!response.ok) {
        if (response.status === 401) {
          // Authentication failed, tokens cleared by API service
          setIsLoggedIn(false);
          return;
        }
        const errorText = await response.text();
        console.error("Profile fetch failed:", response.status, errorText);
        setProfileError("Failed to fetch chess profiles.");
        setProfileLoading(false);
        return;
      }

      const data = await response.json();
      console.log('Fetched chess profiles:', data);
      const chessComProfile = Array.isArray(data) ? data.find(p => p.platform === 'chess.com') : null;
      const lichessProfile = Array.isArray(data) ? data.find(p => p.platform === 'lichess.org') : null;
      setChessComUsername(chessComProfile ? chessComProfile.username : "");
      setLichessUsername(lichessProfile ? lichessProfile.username : "");
      setChessComLocked(!!(chessComProfile && chessComProfile.username));
      setLichessLocked(!!(lichessProfile && lichessProfile.username));
      if (!chessComProfile || !lichessProfile) {
        setShowChessProfileModal(true);
      }
      setProfileLoading(false);
    } catch (error) {
      if (error.message === 'AUTHENTICATION_FAILED') {
        setIsLoggedIn(false);
      } else {
        setProfileError("Failed to fetch chess profiles.");
        setProfileLoading(false);
      }
    }
  };

  // Call checkChessProfiles after login/registration
  useEffect(() => {
    if (isLoggedIn) {
      checkChessProfiles();
    }
    // eslint-disable-next-line
  }, [isLoggedIn]);

  // Save chess profiles
  const handleSaveChessProfiles = async (e) => {
    e.preventDefault();
    setProfileError(null);
    setProfileLoading(true);
    try {
      let errorOccurred = false;
      // Validate chess.com username if not locked and provided
      if (!chessComLocked && chessComUsername) {
        // Check if chess.com account exists
        const res = await fetch(`https://api.chess.com/pub/player/${encodeURIComponent(chessComUsername)}`);
        if (!res.ok) {
          setProfileError("Failed to verify chess.com account.");
          setProfileLoading(false);
          return;
        }
        const data = await res.json();
        if (!data.player_id) {
          setProfileError("Chess.com account does not exist.");
          setProfileLoading(false);
          return;
        }
      }
      // Validate lichess username if not locked and provided
      if (!lichessLocked && lichessUsername) {
        // Check if lichess account exists
        const res = await fetch(`https://lichess.org/api/user/${encodeURIComponent(lichessUsername)}`);
        if (!res.ok) {
          setProfileError("Failed to verify lichess account.");
          setProfileLoading(false);
          return;
        }
        const data = await res.json();
        if (!data.id) {
          setProfileError("Lichess account does not exist.");
          setProfileLoading(false);
          return;
        }
      }
      // Only update chess.com if not locked and username is provided
      if (!chessComLocked && chessComUsername) {
        const response = await apiPost('/users/me/chess-profiles', {
          platform: "chess.com",
          username: chessComUsername
        });
        if (!response.ok) {
          const errorText = await response.text();
          console.error("Profile update failed (chess.com):", response.status, errorText);
          setProfileError("Failed to update chess.com profile.");
          errorOccurred = true;
        }
      }
      // Only update lichess.org if not locked and username is provided
      if (!lichessLocked && lichessUsername) {
        const response = await apiPost('/users/me/chess-profiles', {
          platform: "lichess.org",
          username: lichessUsername
        });
        if (!response.ok) {
          const errorText = await response.text();
          console.error("Profile update failed (lichess.org):", response.status, errorText);
          setProfileError("Failed to update lichess.org profile.");
          errorOccurred = true;
        }
      }
      setProfileLoading(false);
      if (!errorOccurred) {
        setShowChessProfileModal(false);
      }
    } catch (error) {
      if (error.message === 'AUTHENTICATION_FAILED') {
        setIsLoggedIn(false);
      } else {
        setProfileError("Failed to update chess profiles.");
        setProfileLoading(false);
      }
    }
  };

  if (!isLoggedIn) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        backgroundColor: '#f5f5f5'
      }}>
        {!showRegister ? (
          <form onSubmit={handleLogin} style={{
            backgroundColor: 'white',
            padding: '2rem',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            width: '100%',
            maxWidth: '400px'
          }}>
            <h2 style={{ 
              margin: '0 0 1.5rem', 
              textAlign: 'center', 
              color: 'var(--text-color)' 
            }}>Login to Chessticize</h2>
            {loginError && (
              <div style={{ 
                color: 'var(--danger-color)',
                marginBottom: '1rem',
                textAlign: 'center',
                padding: '0.5rem',
                backgroundColor: '#f8d7da',
                borderRadius: '4px'
              }}>{loginError}</div>
            )}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '0.5rem', 
                color: 'var(--text-color)', 
                fontWeight: '500' 
              }} htmlFor="username">Email:</label>
              <input
                type="email"
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid var(--border-color)',
                  borderRadius: '4px',
                  fontSize: 'clamp(14px, 3vw, 16px)'
                }}
              />
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '0.5rem', 
                color: 'var(--text-color)', 
                fontWeight: '500' 
              }} htmlFor="password">Password:</label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid var(--border-color)',
                  borderRadius: '4px',
                  fontSize: 'clamp(14px, 3vw, 16px)'
                }}
              />
            </div>
            <button 
              type="submit" 
              className="btn btn-primary"
              style={{
                width: '100%',
                marginTop: '1rem'
              }}
            >
              Login
            </button>
            <button
              type="button"
              onClick={() => setShowRegister(true)}
              style={{
                width: '100%',
                marginTop: '1rem',
                background: 'none',
                border: 'none',
                color: '#1976d2',
                textDecoration: 'underline',
                cursor: 'pointer',
                fontSize: '1em'
              }}
            >
              Register
            </button>
          </form>
        ) : (
          <form onSubmit={handleRegister} style={{
            backgroundColor: 'white',
            padding: '2rem',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            width: '100%',
            maxWidth: '400px'
          }}>
            <h2 style={{ 
              margin: '0 0 1.5rem', 
              textAlign: 'center', 
              color: 'var(--text-color)' 
            }}>Register for Chessticize</h2>
            {registerError && (
              <div style={{ 
                color: 'var(--danger-color)',
                marginBottom: '1rem',
                textAlign: 'center',
                padding: '0.5rem',
                backgroundColor: '#f8d7da',
                borderRadius: '4px'
              }}>{registerError}</div>
            )}
            {registerSuccess && (
              <div style={{ 
                color: 'var(--success-color, #388e3c)',
                marginBottom: '1rem',
                textAlign: 'center',
                padding: '0.5rem',
                backgroundColor: '#d0f5e8',
                borderRadius: '4px'
              }}>{registerSuccess}</div>
            )}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '0.5rem', 
                color: 'var(--text-color)', 
                fontWeight: '500' 
              }} htmlFor="register-email">Email:</label>
              <input
                type="email"
                id="register-email"
                value={registerEmail}
                onChange={(e) => setRegisterEmail(e.target.value)}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid var(--border-color)',
                  borderRadius: '4px',
                  fontSize: 'clamp(14px, 3vw, 16px)'
                }}
              />
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '0.5rem', 
                color: 'var(--text-color)', 
                fontWeight: '500' 
              }} htmlFor="register-password">Password:</label>
              <input
                type="password"
                id="register-password"
                value={registerPassword}
                onChange={(e) => setRegisterPassword(e.target.value)}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid var(--border-color)',
                  borderRadius: '4px',
                  fontSize: 'clamp(14px, 3vw, 16px)'
                }}
              />
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '0.5rem', 
                color: 'var(--text-color)', 
                fontWeight: '500' 
              }} htmlFor="register-repeat-password">Repeat Password:</label>
              <input
                type="password"
                id="register-repeat-password"
                value={registerRepeatPassword}
                onChange={(e) => setRegisterRepeatPassword(e.target.value)}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid var(--border-color)',
                  borderRadius: '4px',
                  fontSize: 'clamp(14px, 3vw, 16px)'
                }}
              />
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '0.5rem', 
                color: 'var(--text-color)', 
                fontWeight: '500' 
              }} htmlFor="register-invitation-code">Invitation Code:</label>
              <input
                type="text"
                id="register-invitation-code"
                value={registerInvitationCode}
                onChange={(e) => setRegisterInvitationCode(e.target.value)}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid var(--border-color)',
                  borderRadius: '4px',
                  fontSize: 'clamp(14px, 3vw, 16px)'
                }}
              />
            </div>
            <button 
              type="submit" 
              className="btn btn-primary"
              style={{
                width: '100%',
                marginTop: '1rem'
              }}
            >
              Register
            </button>
            <button
              type="button"
              onClick={() => setShowRegister(false)}
              style={{
                width: '100%',
                marginTop: '1rem',
                background: 'none',
                border: 'none',
                color: '#1976d2',
                textDecoration: 'underline',
                cursor: 'pointer',
                fontSize: '1em'
              }}
            >
              Back to Login
            </button>
          </form>
        )}
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        <PuzzlesView
          username={username}
          password={password}
          onLoginFailure={(error) => {
            setLoginError(error);
            setIsLoggedIn(false);
          }}
          onLogout={handleLogout}
        />
        {/* Chess Profile Modal */}
        {showChessProfileModal && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0,0,0,0.5)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 2000
          }}>
            <form onSubmit={handleSaveChessProfiles} style={{
              backgroundColor: 'white',
              padding: '2rem',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
              minWidth: 320,
              maxWidth: 400
            }}>
              <h2 style={{ textAlign: 'center', marginBottom: 20 }}>Add Your Chess Profiles</h2>
              <p style={{ textAlign: 'center', color: '#555', marginBottom: 20 }}>
                To import your games and puzzles, please provide your chess.com and/or lichess username. You can skip this step at any time.
              </p>
              <div style={{ marginBottom: '1rem' }}>
                <label htmlFor="chess-com-username" style={{ display: 'block', marginBottom: 4 }}>Chess.com Username</label>
                <input
                  id="chess-com-username"
                  type="text"
                  value={chessComUsername}
                  onChange={e => setChessComUsername(e.target.value)}
                  style={{ width: '100%', padding: '0.5rem', borderRadius: 4, border: '1px solid #ccc', background: chessComLocked ? '#f5f5f5' : 'white' }}
                  placeholder="Optional"
                  readOnly={chessComLocked}
                />
              </div>
              <div style={{ marginBottom: '1rem' }}>
                <label htmlFor="lichess-username" style={{ display: 'block', marginBottom: 4 }}>Lichess Username</label>
                <input
                  id="lichess-username"
                  type="text"
                  value={lichessUsername}
                  onChange={e => setLichessUsername(e.target.value)}
                  style={{ width: '100%', padding: '0.5rem', borderRadius: 4, border: '1px solid #ccc', background: lichessLocked ? '#f5f5f5' : 'white' }}
                  placeholder="Optional"
                  readOnly={lichessLocked}
                />
              </div>
              {profileError && <div style={{ color: 'red', marginBottom: 10 }}>{profileError}</div>}
              <button
                type="submit"
                className="btn btn-primary"
                style={{ width: '100%', marginBottom: 10 }}
                disabled={profileLoading}
              >
                Save
              </button>
              <button
                type="button"
                onClick={() => setShowChessProfileModal(false)}
                style={{ width: '100%', background: 'none', border: 'none', color: '#1976d2', textDecoration: 'underline', cursor: 'pointer', fontSize: '1em' }}
                disabled={profileLoading}
              >
                Skip
              </button>
            </form>
          </div>
        )}
    </div>
  );
}

export default App; 