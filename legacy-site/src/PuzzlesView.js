import React, { useState, useEffect, useRef } from "react";
import { useQuery } from './hooks/useGraphQL';
import ChessPuzzle from "./components/ChessPuzzle";
import TacticsVisualization from "./TacticsVisualization";
import { GET_PUZZLE_STATS, GET_PUZZLES, GET_GAMES } from './graphql/queries';
import './components/ChessPuzzle.css'; // Import the CSS file
import { Chess } from 'chess.js';
import { Chessboard } from 'react-chessboard';
import { formatDistanceToNow, format } from 'date-fns';

// Updated Loading overlay component
const LoadingOverlay = ({ status, progress, stockfishLoading, loadingError, onRetry }) => {
  console.log("Rendering LoadingOverlay. loadingError:", loadingError);

  return (
    <div style={{ 
      position: "fixed", 
      top: 0, 
      left: 0, 
      right: 0, 
      bottom: 0, 
      backgroundColor: "rgba(0,0,0,0.5)", 
      display: "flex", 
      justifyContent: "center", 
      alignItems: "center",
      zIndex: 1000 
    }}>
      <div style={{ 
        backgroundColor: 'var(--light-square-bg, white)',
        padding: "30px", 
        borderRadius: "8px",
        boxShadow: "0 2px 10px rgba(0,0,0,0.3)",
        width: "400px"
      }}>
        {loadingError ? (
          // --- DRASTICALLY SIMPLIFIED Error Display --- 
          <div> 
             {console.log("Rendering SIMPLIFIED ERROR state")}
             <p style={{color: 'red', fontWeight: 'bold'}}>ERROR DETECTED</p>
             <p>Message: {loadingError}</p>
             <button onClick={onRetry} className="btn btn-primary">
               Retry
             </button>
          </div>
          // --- END SIMPLIFIED Error Display --- 
        ) : (
          // Normal Loading Display (keep vars here for now)
          <>
             {console.log("Rendering NORMAL state in LoadingOverlay")}
            <p style={{ 
              margin: "0 0 15px 0", 
              fontSize: "1.2em", 
              fontWeight: "bold",
              color: 'var(--dark-square-bg, #b58863)', 
            }}>
              {status}
            </p>
            <div style={{
              width: "100%",
              height: "20px",
              backgroundColor: "#f0f0f0", // Keep light grey background for bar track
              borderRadius: "10px",
              overflow: "hidden"
            }}>
              <div style={{
                width: `${progress}%`,
                height: "100%",
                backgroundColor: 'var(--dark-square-bg, #b58863)', // Use dark square color for fill
                transition: "width 0.3s ease-in-out"
              }} />
            </div>
            <p style={{ 
              margin: "10px 0 0 0", 
              fontSize: "0.9em", 
              color: 'var(--text-color, #666)'
            }}>
              {progress}%
            </p>
            {stockfishLoading && (
              <p style={{ 
                margin: "10px 0 0 0", 
                fontSize: "0.9em", 
                color: 'var(--text-color, #666)',
              }}>
                Initializing chess engine...
              </p>
            )}
          </>
        )}
      </div>
    </div>
  );
};

// Chess.com-style result icons
function ResultIcon({ type }) {
  if (type === 'win') {
    return (
      <svg width="20" height="20" viewBox="0 0 20 20" style={{ marginLeft: 10, verticalAlign: 'middle' }}>
        <rect x="2" y="2" width="16" height="16" rx="5" fill="#43a047" />
        <rect x="9" y="5" width="2" height="10" rx="1" fill="#fff" />
        <rect x="5" y="9" width="10" height="2" rx="1" fill="#fff" />
      </svg>
    );
  }
  if (type === 'loss') {
    return (
      <svg width="20" height="20" viewBox="0 0 20 20" style={{ marginLeft: 10, verticalAlign: 'middle' }}>
        <rect x="2" y="2" width="16" height="16" rx="5" fill="#e53935" />
        <rect x="5" y="9" width="10" height="2" rx="1" fill="#fff" />
      </svg>
    );
  }
  if (type === 'draw') {
    // Draw icon: lighter gray background with white '=' sign
    return (
      <svg width="20" height="20" viewBox="0 0 20 20" style={{ marginLeft: 10, verticalAlign: 'middle' }}>
        <rect x="2" y="2" width="16" height="16" rx="5" fill="#bbb" />
        <text x="10" y="14" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#fff">=</text>
      </svg>
    );
  }
  return null;
}

const PuzzlesView = ({ username, password, onLoginFailure, onLogout }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingStatus, setLoadingStatus] = useState({ status: "Initializing...", progress: 0 });
  const [stockfishLoading, setStockfishLoading] = useState(true);
  const [loadingError, setLoadingError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [trainingSession, setTrainingSession] = useState(null);
  const [trainingResults, setTrainingResults] = useState(null);
  const [selectedGame, setSelectedGame] = useState(null);
  const [replayState, setReplayState] = useState({ fen: 'start', moveIndex: 0, moves: [] });
  const [showWeaknessOnMount, setShowWeaknessOnMount] = useState(true);
  const prevTabRef = useRef(0);

  // Query for puzzle statistics
  const { data: statsData, loading: statsLoading, error: statsError, refetch: statsRefetch } = useQuery(GET_PUZZLE_STATS);

  // Query for puzzles with the weakest theme
  const { data: puzzlesData, loading: puzzlesLoading, error: puzzlesError, refetch: puzzlesRefetch } = useQuery(GET_PUZZLES, {
    variables: {
      filter: {
        theme: "OPPONENT_MISTAKE_MISSED",
        tags: ["pin", "fork", "mateIn1", "hangingPiece"]
      },
      pagination: {
        offset: 0,
        limit: 100
      }
    },
    skip: !statsData // Skip this query until we have stats
  });

  // Query for games (for Games tab)
  const { data: gamesData, loading: gamesLoading, error: gamesError, refetch: gamesRefetch } = useQuery(GET_GAMES, {
    variables: {
      pagination: { offset: 0, limit: 20 },
      sort: { field: 'GAME_TIME', direction: 'DESC' }
    },
    skip: activeTab !== 2
  });

  // Analyze weakest tactic from stats
  const analyzeWeakestTactic = (stats) => {
    if (!stats || !stats.tag_counts) return null;
    
    // Find the tag with the highest count
    const tagCounts = stats.tag_counts;
    const weakestThemeEntry = tagCounts
      .filter(tag => ["pin", "fork", "mateIn1", "hangingPiece"].includes(tag.tag))
      .sort((a, b) => b.count - a.count)[0];
      
    return weakestThemeEntry ? {
      theme: weakestThemeEntry.tag,
      count: weakestThemeEntry.count
    } : null;
  };

  // Create training session
  const createTrainingSession = (puzzles, weakestTheme) => {
    if (!puzzles || !weakestTheme) return null;

    const puzzlesToProcess = puzzles.edges.map(edge => edge.node);

    // Filter puzzles by weakest theme
    const targetedPuzzles = puzzlesToProcess.filter(puzzle => 
      puzzle.tags && puzzle.tags.includes(weakestTheme) &&
      (puzzle.theme === "opponent_mistake_missed" || 
       puzzle.theme === "opponent_blunder_missed")
    );
    
    // Get mixed puzzles (excluding weakest theme)
    const mixedPuzzles = puzzlesToProcess.filter(puzzle => 
      puzzle.tags && !puzzle.tags.includes(weakestTheme) &&
      (puzzle.theme === "opponent_mistake_missed" || 
       puzzle.theme === "opponent_blunder_missed")
    );
    
    // Select 14 targeted puzzles (70%)
    const selectedTargeted = targetedPuzzles
      .sort(() => Math.random() - 0.5)
      .slice(0, 14);
    
    // Select 6 mixed puzzles (30%)
    const selectedMixed = mixedPuzzles
      .sort(() => Math.random() - 0.5)
      .slice(0, 6);
    
    // Combine and shuffle all puzzles
    const sessionPuzzles = [...selectedTargeted, ...selectedMixed]
      .sort(() => Math.random() - 0.5);
    
    return {
      puzzles: sessionPuzzles,
      targetedPuzzles: selectedTargeted,
      mixedPuzzles: selectedMixed
    };
  };

  // Handle training completion
  const handleTrainingComplete = (results) => {
    setTrainingResults(results);
  };

  // Handle game row click
  const handleGameClick = (game) => {
    if (!game.pgn) return;
    const chess = new Chess();
    chess.loadPgn(game.pgn);
    const moves = chess.history({ verbose: true });
    chess.reset();
    setReplayState({ fen: chess.fen(), moveIndex: 0, moves });
    setSelectedGame(game);
  };

  // Handle next/prev move
  const handleReplayMove = (direction) => {
    if (!selectedGame) return;
    const chess = new Chess();
    chess.loadPgn(selectedGame.pgn);
    let idx = replayState.moveIndex;
    if (direction === 'next' && idx < replayState.moves.length) idx++;
    if (direction === 'prev' && idx > 0) idx--;
    chess.reset();
    for (let i = 0; i < idx; i++) {
      chess.move(replayState.moves[i]);
    }
    setReplayState({ ...replayState, fen: chess.fen(), moveIndex: idx });
  };

  // Effect to handle loading states and errors
  useEffect(() => {
    if (statsLoading || puzzlesLoading) {
      setIsLoading(true);
      setLoadingStatus({ status: "Loading puzzles...", progress: 50 });
    } else if (statsError || puzzlesError) {
      setLoadingError(statsError?.message || puzzlesError?.message);
      setIsLoading(false);
    } else if (statsData && puzzlesData) {
      setIsLoading(false);
      setLoadingError(null);
    }
  }, [statsLoading, puzzlesLoading, statsError, puzzlesError, statsData, puzzlesData]);

  // Effect to create training session when data is available
  useEffect(() => {
    if (statsData && puzzlesData) {
      const weakest = analyzeWeakestTactic(statsData.myPuzzleStats);
      if (weakest && !trainingSession) {
        const session = createTrainingSession(puzzlesData.myPuzzles, weakest.theme);
        setTrainingSession(session);
      }
    }
  }, [statsData, puzzlesData, trainingSession]);

  // When user logs out or logs in, reset showWeaknessOnMount
  useEffect(() => {
    setShowWeaknessOnMount(true);
  }, [username]);

  // Only set showWeaknessOnMount to false if switching to puzzles tab from another tab
  useEffect(() => {
    if (activeTab === 0 && prevTabRef.current !== 0) {
      setShowWeaknessOnMount(false);
    }
    prevTabRef.current = activeTab;
  }, [activeTab]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Helper to group games by date label
  function groupGamesByDate(games) {
    const groups = {};
    games.forEach(game => {
      const date = game.game_time ? new Date(game.game_time) : null;
      let label = 'Unknown';
      if (date) {
        const now = new Date();
        const daysAgo = Math.floor((now - date) / (1000 * 60 * 60 * 24));
        if (daysAgo === 0) label = 'Today';
        else if (daysAgo === 1) label = 'Yesterday';
        else if (daysAgo < 7) label = `${daysAgo} days ago`;
        else label = format(date, 'MMM d, yyyy');
      }
      if (!groups[label]) groups[label] = [];
      groups[label].push(game);
    });
    return groups;
  }

  // Helper to extract username from player field
  function getPlayerUsername(player) {
    if (!player) return null;
    try {
      const obj = typeof player === 'string' ? JSON.parse(player) : player;
      return obj.username || null;
    } catch {
      return null;
    }
  }

  return (
    <div style={{ textAlign: "center", margin: "0 auto", maxWidth: "800px" }}>
      {/* Error Banner */}
      {(statsError || puzzlesError || gamesError) && (
        <div style={{
          backgroundColor: '#f8d7da',
          color: '#721c24',
          padding: '16px 20px',
          marginBottom: '20px',
          borderRadius: '6px',
          border: '1px solid #f5c6cb',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <strong style={{ fontSize: '16px' }}>⚠️ Unable to load data</strong>
              <div style={{ marginTop: '8px', fontSize: '14px', lineHeight: '1.4' }}>
                {statsError && <div>• Puzzle statistics failed to load</div>}
                {puzzlesError && <div>• Puzzle data failed to load</div>}
                {gamesError && <div>• Game data failed to load</div>}
                <div style={{ marginTop: '4px', fontStyle: 'italic', opacity: 0.8 }}>
                  This might be a temporary server issue. Please try again.
                </div>
              </div>
            </div>
            <button
              onClick={() => {
                // Try to refetch data instead of full page reload
                if (statsRefetch) statsRefetch();
                if (puzzlesRefetch) puzzlesRefetch();
                if (gamesRefetch) gamesRefetch();
              }}
              style={{
                padding: '8px 16px',
                backgroundColor: '#721c24',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: 'bold',
                marginLeft: '16px'
              }}
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {isLoading && (
        <LoadingOverlay
          status={loadingStatus.status}
          progress={loadingStatus.progress}
          stockfishLoading={stockfishLoading}
          loadingError={loadingError}
          onRetry={() => window.location.reload()}
        />
      )}
      {!isLoading && puzzlesData && (
        <>
          <div style={{
            borderBottom: "1px solid #ddd",
            marginBottom: "20px",
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <div>
              <button
                onClick={() => handleTabChange(null, 0)}
                className={`btn ${activeTab === 0 ? 'btn-primary' : 'btn-secondary'}`}
              >
                Puzzles
              </button>
              <button
                onClick={() => handleTabChange(null, 1)}
                className={`btn ${activeTab === 1 ? 'btn-primary' : 'btn-secondary'}`}
                style={{ marginLeft: '5px' }}
              >
                Statistics
              </button>
              <button
                onClick={() => handleTabChange(null, 2)}
                className={`btn ${activeTab === 2 ? 'btn-primary' : 'btn-secondary'}`}
                style={{ marginLeft: '5px' }}
              >
                Games
              </button>
            </div>
            <button
              onClick={onLogout}
              className="btn btn-secondary"
              style={{ marginLeft: 'auto' }}
            >
              Logout
            </button>
          </div>
          {activeTab === 0 && (
            <div>
              {trainingResults ? (
                <div style={{ padding: "20px" }}>
                  <h2>Training Results</h2>
                  <div style={{ marginBottom: "20px" }}>
                    <h3>Targeted Training ({trainingSession?.targetedPuzzles.length} puzzles)</h3>
                    <p>Correct: {trainingResults.targeted.correct}/{trainingSession?.targetedPuzzles.length}</p>
                    <p>Average Time: {trainingResults.targeted.avgTime.toFixed(1)}s</p>
                  </div>
                  <div>
                    <h3>Mixed Training ({trainingSession?.mixedPuzzles.length} puzzles)</h3>
                    <p>Correct: {trainingResults.mixed.correct}/{trainingSession?.mixedPuzzles.length}</p>
                    <p>Average Time: {trainingResults.mixed.avgTime.toFixed(1)}s</p>
                  </div>
                  <button
                    onClick={() => {
                      setTrainingResults(null);
                      setTrainingSession(null);
                    }}
                    style={{
                      marginTop: "20px",
                      padding: "10px 20px",
                      backgroundColor: 'var(--dark-square-bg, #b58863)',
                      color: "white",
                      border: "none",
                      borderRadius: "4px",
                      cursor: "pointer"
                    }}
                  >
                    Start New Training
                  </button>
                </div>
              ) : trainingSession ? (
                <ChessPuzzle 
                  puzzles={trainingSession.puzzles} 
                  onComplete={handleTrainingComplete}
                  stockfishLoading={setStockfishLoading}
                  showWeaknessOnMount={showWeaknessOnMount}
                />
              ) : (
                <ChessPuzzle 
                  puzzles={puzzlesData.myPuzzles.edges.map(edge => edge.node)} 
                  stockfishLoading={setStockfishLoading} 
                  showWeaknessOnMount={showWeaknessOnMount}
                />
              )}
            </div>
          )}
          {activeTab === 1 && <TacticsVisualization data={puzzlesData} />}
          {activeTab === 2 && (
            <div>
              {selectedGame ? (
                <div style={{ marginBottom: '20px' }}>
                  <button onClick={() => setSelectedGame(null)} style={{ marginBottom: '10px' }}>Back to Game List</button>
                  <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginBottom: '20px' }}>
                    <Chessboard position={replayState.fen} arePiecesDraggable={false} />
                  </div>
                  <div style={{ marginTop: '10px', textAlign: 'center' }}>
                    <button onClick={() => handleReplayMove('prev')} disabled={replayState.moveIndex === 0}>Prev</button>
                    <span style={{ margin: '0 10px' }}>Move {replayState.moveIndex} / {replayState.moves.length}</span>
                    <button onClick={() => handleReplayMove('next')} disabled={replayState.moveIndex === replayState.moves.length}>Next</button>
                  </div>
                </div>
              ) : (
                <>
                  {gamesLoading && <p>Loading games...</p>}
                  {gamesError && <p style={{ color: 'red' }}>Error loading games: {gamesError.message}</p>}
                  {gamesData && gamesData.myGames.edges.length === 0 && <p>No games found.</p>}
                  {gamesData && gamesData.myGames.edges.length > 0 && (
                    <div style={{ width: '100%', marginTop: '20px' }}>
                      {Object.entries(groupGamesByDate(gamesData.myGames.edges.map(e => e.node))).map(([dateLabel, games]) => (
                        <div key={dateLabel} style={{ marginBottom: '24px' }}>
                          <div style={{ fontWeight: 'bold', fontSize: '1.1em', marginBottom: '8px', color: '#555' }}>{dateLabel}</div>
                          {games.map(game => {
                            // Determine Chess.com-style results for both players, vertical layout
                            let whiteResult = '—', blackResult = '—';
                            let whiteColor = '#888', blackColor = '#888';
                            let iconType = null;
                            if (game.winner !== 'white' && game.winner !== 'black') {
                              whiteResult = blackResult = '½';
                              whiteColor = blackColor = '#888';
                              iconType = 'draw';
                            } else if (game.winner === 'white') {
                              whiteResult = '1';
                              blackResult = '0';
                              whiteColor = '#222';
                              blackColor = '#888';
                            } else if (game.winner === 'black') {
                              whiteResult = '0';
                              blackResult = '1';
                              whiteColor = '#888';
                              blackColor = '#222';
                            }
                            // Icon should reflect the user's result
                            if (game.winner !== 'white' && game.winner !== 'black') {
                              iconType = 'draw';
                            } else if (game.winner === game.user_color) {
                              iconType = 'win';
                            } else if (game.winner && game.winner !== game.user_color) {
                              iconType = 'loss';
                            }
                            return (
                              <div
                                key={game.id}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  background: '#fafbfc',
                                  borderRadius: '8px',
                                  boxShadow: '0 1px 2px rgba(0,0,0,0.03)',
                                  marginBottom: '8px',
                                  padding: '10px 16px',
                                  cursor: 'pointer',
                                  transition: 'background 0.2s',
                                }}
                                onClick={() => handleGameClick(game)}
                              >
                                {/* Result numbers vertical (white on top, black on bottom) with icon */}
                                <div style={{
                                  minWidth: 48,
                                  display: 'flex',
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontWeight: 'bold',
                                  fontSize: '1.1em',
                                  marginRight: 18
                                }}>
                                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                                    <span style={{ color: whiteColor }}>{whiteResult}</span>
                                    <span style={{ color: blackColor }}>{blackResult}</span>
                                  </div>
                                  <ResultIcon type={iconType} />
                                </div>
                                {/* Players */}
                                <div style={{ flex: 2, display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                                  <span style={{ fontWeight: game.user_color === 'white' ? 'bold' : 'normal' }}>
                                    {game.user_color === 'white'
                                      ? 'You (White)'
                                      : (getPlayerUsername(game.white_player) ? `${getPlayerUsername(game.white_player)} (White)` : 'Opponent (White)')}
                                  </span>
                                  <span style={{ fontWeight: game.user_color === 'black' ? 'bold' : 'normal' }}>
                                    {game.user_color === 'black'
                                      ? 'You (Black)'
                                      : (getPlayerUsername(game.black_player) ? `${getPlayerUsername(game.black_player)} (Black)` : 'Opponent (Black)')}
                                  </span>
                                </div>
                                {/* Moves */}
                                <div style={{ flex: 1, textAlign: 'center', color: '#888' }}>
                                  {game.pgn ? (game.pgn.match(/\d+\./g)?.length || '?') : '?'} moves
                                </div>
                                {/* Date */}
                                <div style={{ flex: 1, textAlign: 'center', color: '#888' }}>
                                  {game.game_time ? format(new Date(game.game_time), 'MMM d, yyyy') : ''}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </>
      )}
      {!isLoading && (!puzzlesData || puzzlesData.myPuzzles.edges.length === 0) && !loadingError && (
        <p>No puzzle data found.</p>
      )}
    </div>
  );
};

export default PuzzlesView; 