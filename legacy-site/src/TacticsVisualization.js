import React, { useState, useEffect, useMemo } from 'react';
import { useQuery } from './hooks/useGraphQL';
import { GET_PUZZLE_STATS, GET_PUZZLES, GET_GROUPED_PUZZLE_STATS } from './graphql/queries';
import { 
  Container, 
  Paper, 
  Typography, 
  Box,
  FormGroup,
  FormControlLabel,
  Checkbox,
  ThemeProvider,
  createTheme,
  Alert,
  Button
} from '@mui/material';
import { Line, Bar } from 'react-chartjs-2';
import { BarElement } from 'chart.js';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
} from 'chart.js';
import zoomPlugin from 'chartjs-plugin-zoom';
import 'chartjs-adapter-date-fns';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  zoomPlugin,
  BarElement
);

const ALLOWED_TAGS = ['mateIn1', 'fork', 'pin', 'hangingPiece'];
const WINDOW_SIZE = 100;
const THEMES = {
  'own_mistakes': 'Own Mistakes',
  'opponent_mistake_caught': 'Opponent Mistakes Caught',
  'opponent_mistake_missed': 'Opponent Mistakes Missed'
};

// Create theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function TacticsVisualization() {
  const [selectedTags, setSelectedTags] = useState(ALLOWED_TAGS);
  const [selectedThemes, setSelectedThemes] = useState(Object.keys(THEMES));
  const [visibleTrendTags, setVisibleTrendTags] = useState(ALLOWED_TAGS);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState({ start: null, end: null });
  const [processedData, setProcessedData] = useState(null);
  const [puzzleLengths, setPuzzleLengths] = useState(null);
  const [themeDistribution, setThemeDistribution] = useState(null);

  // Query for puzzle statistics
  const { data: statsData, loading: statsLoading, error: statsError } = useQuery(GET_PUZZLE_STATS);

  // Query for puzzles with the weakest theme
  const { data: puzzlesData, loading: puzzlesLoading, error: puzzlesError } = useQuery(GET_PUZZLES, {
    variables: {
      filter: {
        theme: "OPPONENT_MISTAKE_MISSED",
        tags: ALLOWED_TAGS
      },
      pagination: {
        offset: 0,
        limit: 100
      }
    },
    skip: !statsData // Skip this query until we have stats
  });

  // Fetch grouped stats (trend)
  const groupedStatsVariables = useMemo(() => {
    return {
      filter: {}, // No time filter, fetch all available data
      pagination: { offset: 0, limit: 100 }, // Increase limit to cover more periods if needed
      group_unit: 'MONTH',
      group_length: 1
    };
  }, []);
  // Log the client request variables as a JSON string
  console.log('Apollo client will send variables (JSON):', JSON.stringify(groupedStatsVariables));
  const { data: groupedStatsData, loading: groupedStatsLoading, error: groupedStatsError } = useQuery(GET_GROUPED_PUZZLE_STATS, {
    variables: groupedStatsVariables
  });
  // Log the server response as a JSON string
  console.log('Server response for grouped stats (JSON):', JSON.stringify(groupedStatsData));

  if (groupedStatsError) {
    console.error('Apollo error details:', JSON.stringify(groupedStatsError, null, 2));
  }

  // After the useQuery call for groupedStatsData
  console.log('Server response for grouped stats:', groupedStatsData);

  const processPuzzleLengths = (data) => {
    if (!data?.myPuzzles?.edges) return;

    const lengths = data.myPuzzles.edges.map(edge => edge.node.moves.length/2);

    // Create bins for histogram
    const binCount = Math.max(...lengths) - Math.min(...lengths) + 1;
    const bins = new Array(binCount).fill(0);
    const min = Math.min(...lengths);

    lengths.forEach(length => {
      const binIndex = length - min;
      bins[binIndex]++;
    });

    // Filter out empty bins and create labels
    const nonEmptyBins = bins.map((count, index) => ({
      value: min + index,
      count: count
    })).filter(bin => bin.count > 0);

    setPuzzleLengths({
      labels: nonEmptyBins.map(bin => bin.value),
      data: nonEmptyBins.map(bin => bin.count),
      total: lengths.length,
      average: (lengths.reduce((a, b) => a + b, 0) / lengths.length).toFixed(1)
    });
  };

  const processTagDistribution = (data) => {
    if (!data?.myPuzzleStats?.tag_counts) return;

    const tags = {};
    data.myPuzzleStats.tag_counts.forEach(({ tag, count }) => {
      tags[tag] = count;
    });

    // Convert to array format for chart and limit to first 16 tags
    const sortedTags = Object.entries(tags)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 16); // Only first 16 tags

    setThemeDistribution({
      labels: sortedTags.map(([tag]) => tag),
      data: sortedTags.map(([, count]) => count),
      total: Object.values(tags).reduce((a, b) => a + b, 0)
    });
  };

  useEffect(() => {
    if (statsData && puzzlesData) {
      try {
        processPuzzleLengths(puzzlesData);
        processTagDistribution(statsData);
      } catch (err) {
        console.error('TacticsVisualization: Processing error:', err);
        setError(err.message);
      }
    }
  }, [statsData, puzzlesData]);

  const calculateMovingAverage = (puzzles, allPuzzleTimestamps) => {
    // Sort puzzles by timestamp
    const sortedPuzzles = [...puzzles].sort((a, b) => 
      new Date(a.created_at) - new Date(b.created_at)
    );
    
    const movingAverages = [];
    
    // Calculate moving average for each time point
    allPuzzleTimestamps.forEach((timePoint) => {
      // Find all puzzles within the window before this time point
      const windowPuzzles = sortedPuzzles.filter(puzzle => 
        new Date(puzzle.created_at) <= timePoint &&
        new Date(puzzle.created_at) > new Date(timePoint - (30 * 24 * 60 * 60 * 1000)) // 30 days in milliseconds
      );
      
      // Only calculate if we have puzzles in the window
      if (windowPuzzles.length > 0) {
        movingAverages.push({
          timestamp: timePoint,
          percentage: (windowPuzzles.length / WINDOW_SIZE) * 100
        });
      }
    });
    
    return movingAverages;
  };

  const processData = (graphqlData) => {
    if (!graphqlData.myPuzzles?.edges) {
      throw new Error('Invalid data format: puzzles not found');
    }

    // Get all timestamps sorted chronologically
    const allPuzzleTimestamps = [...new Set(
      graphqlData.myPuzzles.edges.map(edge => new Date(edge.node.created_at))
    )].sort((a, b) => a - b);

    // Initialize data structure
    const puzzlesByThemeAndTag = {
      'own_mistakes': {},
      'opponent_mistake_caught': {},
      'opponent_mistake_missed': {}
    };

    // Initialize arrays for each theme and tag
    Object.keys(puzzlesByThemeAndTag).forEach(theme => {
      ALLOWED_TAGS.forEach(tag => {
        puzzlesByThemeAndTag[theme][tag] = [];
      });
    });

    // Process each puzzle
    graphqlData.myPuzzles.edges.forEach(({ node: puzzle }) => {
      const theme = puzzle.theme;
      if (puzzlesByThemeAndTag[theme]) {
        puzzle.tags.forEach(tag => {
          if (ALLOWED_TAGS.includes(tag) && puzzlesByThemeAndTag[theme][tag]) {
            puzzlesByThemeAndTag[theme][tag].push({
              timestamp: new Date(puzzle.created_at),
              success: true // You might want to add success/failure data to the GraphQL query
            });
          }
        });
      }
    });

    // Calculate moving averages for each theme and tag
    const result = {};
    Object.entries(puzzlesByThemeAndTag).forEach(([theme, tagData]) => {
      result[theme] = {};
      Object.entries(tagData).forEach(([tag, puzzles]) => {
        result[theme][tag] = calculateMovingAverage(puzzles, allPuzzleTimestamps);
      });
    });

    return result;
  };

  const getThemeColor = (theme, alpha = 1) => {
    // Base colors for each theme
    const themeBaseColors = {
      'own_mistakes': {
        'mateIn1': `rgba(220, 0, 78, ${alpha})`,      // Red
        'fork': `rgba(244, 67, 54, ${alpha})`,          // Light Red
        'pin': `rgba(255, 152, 0, ${alpha})`            // Orange
      },
      'opponent_mistake_caught': {
        'mateIn1': `rgba(0, 200, 83, ${alpha})`,      // Bright Green
        'fork': `rgba(0, 200, 83, ${alpha})`,           // Bright Green
        'pin': `rgba(0, 200, 83, ${alpha})`             // Bright Green
      },
      'opponent_mistake_missed': {
        'mateIn1': `rgba(255, 87, 34, ${alpha})`,     // Deep Orange
        'fork': `rgba(255, 87, 34, ${alpha})`,          // Deep Orange
        'pin': `rgba(255, 87, 34, ${alpha})`            // Deep Orange
      }
    };

    return themeBaseColors[theme] || `rgba(0, 0, 0, ${alpha})`;
  };

  const handleTagChange = (tag) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const handleThemeChange = (theme) => {
    setSelectedThemes(prev => 
      prev.includes(theme) 
        ? prev.filter(t => t !== theme)
        : [...prev, theme]
    );
  };

  const createChartData = (theme, tagDataArray) => {
    if (!selectedThemes.includes(theme)) {
      return [];
    }

    const filteredTagData = tagDataArray.filter(tagData => 
      selectedTags.includes(tagData.tag)
    );

    return filteredTagData.map(tagData => ({
      label: `${tagData.tag} - ${theme === 'opponent_mistake_caught' ? 'Caught' : 'Missed'}`,
      data: tagData.data.map(point => ({
        x: point.timestamp * 1000,
        y: point.percentage
      })),
      borderColor: getThemeColor(theme)[tagData.tag],
      backgroundColor: getThemeColor(theme, 0.2)[tagData.tag],
      fill: false
    }));
  };

  const chartOptions = {
    responsive: true,
    scales: {
      x: {
        type: 'time',
        time: {
          minUnit: 'day',
          displayFormats: {
            millisecond: 'HH:mm:ss',
            second: 'HH:mm:ss',
            minute: 'HH:mm',
            hour: 'MMM d, HH:mm',
            day: 'MMM d',
            week: 'MMM d',
            month: 'MMM yyyy',
            quarter: 'MMM yyyy',
            year: 'yyyy'
          },
          tooltipFormat: 'MMM d, yyyy'
        },
        title: {
          display: true,
          text: 'Date'
        },
        ticks: {
          autoSkip: true,
          maxRotation: 0,
          major: {
            enabled: true
          },
          font: {
            size: 12
          }
        }
      },
      y: {
        title: {
          display: true,
          text: 'Occurrence Rate (%)',
        },
        beginAtZero: true,
        ticks: {
          callback: (value) => `${value.toFixed(2)}%`
        }
      }
    },
    plugins: {
      title: {
        display: true,
        text: `Chess Tactics Performance (${WINDOW_SIZE}-game Moving Average)`
      },
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            return `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%`;
          }
        }
      },
      zoom: {
        pan: {
          enabled: true,
          mode: 'x'
        },
        zoom: {
          wheel: {
            enabled: true
          },
          pinch: {
            enabled: true
          },
          mode: 'x',
          drag: {
            enabled: true,
            backgroundColor: 'rgba(0,0,0,0.1)'
          }
        }
      }
    }
  };

  // Add a reference to the chart
  const chartRef = React.useRef(null);

  // Add reset zoom function
  const handleResetZoom = () => {
    if (chartRef && chartRef.current) {
      chartRef.current.resetZoom();
    }
  };

  // Prepare trend chart data
  let trendChartData = null;
  if (groupedStatsData && groupedStatsData.myGroupedPuzzleStats) {
    const nodes = groupedStatsData.myGroupedPuzzleStats.nodes;
    // Only include these tags in the trend chart
    const TREND_TAGS = ['mateIn1', 'fork', 'pin', 'hangingPiece'];
    // Collect all tags, but filter to only the ones we want
    const allTags = TREND_TAGS.filter(tag => nodes.some(n => n.stats.tag_counts.some(tc => tc.tag === tag)));
    trendChartData = {
      labels: nodes.map(n => n.start_time.split('T')[0]),
      datasets: allTags.filter(tag => visibleTrendTags.includes(tag)).map((tag, i) => ({
        label: tag,
        data: nodes.map(n => {
          const found = n.stats.tag_counts.find(tc => tc.tag === tag);
          return found ? found.count : 0;
        }),
        borderColor: `hsl(${i * 60}, 70%, 50%)`,
        backgroundColor: `hsl(${i * 60}, 70%, 80%)`,
        fill: false,
        tension: 0.2
      }))
    };
  }

  const trendLegendCheckbox = (
    <Box sx={{ display: 'flex', gap: 3, mb: 2 }}>
      {ALLOWED_TAGS.map((tag, i) => (
        <FormControlLabel
          key={tag}
          control={
            <Checkbox
              checked={visibleTrendTags.includes(tag)}
              onChange={() => setVisibleTrendTags(prev =>
                prev.includes(tag)
                  ? prev.filter(t => t !== tag)
                  : [...prev, tag]
              )}
              color="primary"
            />
          }
          label={
            <span style={{ display: 'flex', alignItems: 'center' }}>
              <span
                style={{
                  display: 'inline-block',
                  width: 16,
                  height: 16,
                  backgroundColor: `hsl(${i * 60}, 70%, 50%)`,
                  borderRadius: 3,
                  marginRight: 6,
                  border: '1px solid #888'
                }}
              />
              {tag}
            </span>
          }
          sx={{ marginRight: 2 }}
        />
      ))}
    </Box>
  );

  if (statsLoading || puzzlesLoading) return <div>Loading statistics...</div>;
  if (statsError || puzzlesError) return <div>Error loading statistics: {statsError?.message || puzzlesError?.message}</div>;
  if (error) return <div>Error processing statistics: {error}</div>;

  return (
    <ThemeProvider theme={theme}>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Chess Tactics Performance
        </Typography>

        {/* Tactic Trend Chart */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Tactic Trend (by Tag, Weekly)
          </Typography>
          {trendLegendCheckbox}
          {groupedStatsLoading && <div>Loading trend...</div>}
          {groupedStatsError && <Alert severity="error">Error loading trend: {groupedStatsError.message}</Alert>}
          {trendChartData && (
            <Line
              data={trendChartData}
              options={chartOptions}
              height={80}
            />
          )}
          {!groupedStatsLoading && !groupedStatsError && !trendChartData && (
            <div>No trend data available for this period.</div>
          )}
        </Paper>

        <Paper sx={{ p: 2, mb: 3 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          

          {puzzleLengths && (
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Puzzle Length Distribution
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Total Puzzles: {puzzleLengths.total} | Average Length: {puzzleLengths.average} moves
              </Typography>
              <Bar
                data={{
                  labels: puzzleLengths.labels,
                  datasets: [
                    {
                      label: "Number of Puzzles",
                      data: puzzleLengths.data,
                      backgroundColor: "rgba(54, 162, 235, 0.5)",
                      borderColor: "rgb(54, 162, 235)",
                      borderWidth: 1,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  plugins: {
                    legend: {
                      position: "top",
                    },
                    title: {
                      display: true,
                      text: "Distribution of Puzzle Lengths",
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      title: {
                        display: true,
                        text: "Number of Puzzles",
                      },
                    },
                    x: {
                      title: {
                        display: true,
                        text: "Number of Moves",
                      },
                    },
                  },
                }}
              />
            </Box>
          )}

          {themeDistribution && (
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Tactical Theme Distribution
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Total Puzzles: {themeDistribution.total}
              </Typography>
              <Bar
                data={{
                  labels: themeDistribution.labels,
                  datasets: [
                    {
                      label: "Number of Puzzles",
                      data: themeDistribution.data,
                      backgroundColor: "rgba(75, 192, 192, 0.5)",
                      borderColor: "rgb(75, 192, 192)",
                      borderWidth: 1,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  plugins: {
                    legend: {
                      position: "top",
                    },
                    title: {
                      display: true,
                      text: "Distribution of Tactical Themes",
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      title: {
                        display: true,
                        text: "Number of Puzzles",
                      },
                    },
                    x: {
                      title: {
                        display: true,
                        text: "Tactical Theme",
                      },
                    },
                  },
                }}
              />
            </Box>
          )}
        </Paper>
      </Container>
    </ThemeProvider>
  );
}

export default TacticsVisualization; 