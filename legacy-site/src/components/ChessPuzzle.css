/* Base Button Styles */
.btn {
    padding: 8px 16px;
    font-size: clamp(14px, 3vw, 16px);
    border-radius: 4px;
    cursor: pointer;
    border: none;
    transition: background-color 0.15s ease-in-out, opacity 0.15s ease-in-out, color 0.15s ease-in-out;
    white-space: nowrap;
    line-height: 1.5;
    text-align: center;
    vertical-align: middle;
  }
  
  /* --- Dark Square Color Scheme --- */
  :root {
    --dark-square-bg: #b58863;
    --dark-square-hover: #a07655; /* Slightly darker */
    --dark-square-text: white; /* White text on the dark squares */
    --disabled-bg: #6c757d; /* Keep grey for disabled */
    --disabled-text: #ccc; /* Lighter text when disabled */
    --text-color: #333;
    --border-color: #ccc;
    --danger-color: #dc3545; /* Added danger color variable */
  }
  
  /* Primary Button Style (Dark Square Color) */
  .btn-primary {
    background-color: var(--dark-square-bg);
    color: var(--dark-square-text);
  }
  
  .btn-primary:hover {
    background-color: var(--dark-square-hover);
  }
  
  .btn-primary:disabled {
     background-color: var(--disabled-bg);
     color: var(--disabled-text);
     /* Inherits .btn:disabled opacity and cursor */
  }
  
  
  /* Secondary Button Style (Light Grey - Unchanged) */
  .btn-secondary {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ccc;
  }
  
  .btn-secondary:hover {
    background-color: #e2e6ea;
  }
  
  .btn-secondary:disabled {
      background-color: #f8f9fa;
      color: #6c757d;
      border-color: #d6d8db;
      /* Inherits .btn:disabled opacity and cursor */
  }
  
  /* General Disabled State Styles */
  .btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }
  
  /* --- Session Summary Buttons --- */
  /* Keep these separate for now, or change their classes to btn-primary/secondary */
  .session-summary-button {
    margin-top: 20px;
    padding: 10px 20px;
    font-size: 1.1em;
    cursor: pointer;
  }
  
  /* Example: Use dark square for Review button */
  .review-mistakes-button {
     background-color: var(--dark-square-bg);
     color: var(--dark-square-text);
     border: none;
  }
  .review-mistakes-button:hover {
     background-color: var(--dark-square-hover);
  }
  .review-mistakes-button:disabled { /* If it can be disabled */
     background-color: var(--disabled-bg);
     color: var(--disabled-text);
  }
  
  
  /* Example: Use secondary style for New Session, or change class to btn-primary */
  .new-session-button {
      /* Using secondary style as an example */
      background-color: #f8f9fa;
      color: #333;
      border: 1px solid #ccc;
      margin-left: 15px;
  }
  .new-session-button.no-mistakes {
     margin-left: 0;
  }
  .new-session-button:hover {
     background-color: #e2e6ea;
  }
  .new-session-button:disabled {
     background-color: #f8f9fa;
     color: #6c757d;
     border-color: #d6d8db;
  }
  
body, html {
  margin: 0;
  padding: 0;
}
  