import React, { useState, useEffect, useRef } from "react";
import { Chess } from "chess.js";
import { Chessboard } from "react-chessboard";
import moveSound from "../assets/sounds/move-sound.mp3";
import captureSound from "../assets/sounds/capture-sound.mp3";
import errorSound from "../assets/sounds/error-sound.mp3";
import confirmationSound from "../assets/sounds/confirmation-sound.mp3";
import Sf167Web from "./sf16-7.js";
import { filterPuzzles } from "../utils/puzzleUtils";
import './ChessPuzzle.css'; // Import the CSS file

const playSound = (soundFile) => {
  new Audio(soundFile).play().catch((error) => {
    console.error("Error playing sound:", error);
  });
};

// Define the Overlay component - Accepts children now
const WeaknessOverlay = ({ children, onClose }) => {
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000, // Ensure it's above other content
    }}>
      <div style={{
        position: 'relative',
        backgroundColor: 'var(--light-square-bg, white)', // Use light square variable
        padding: '30px',
        borderRadius: '8px',
        textAlign: 'center',
        maxWidth: '80%',
        boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)'
      }}>
        {/* Close button */}
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: 10,
            right: 10,
            background: 'transparent',
            border: 'none',
            fontSize: '1.5rem',
            cursor: 'pointer',
            color: '#888',
          }}
          aria-label="Close"
        >
          ×
        </button>
        <p style={{ 
          marginBottom: '0', 
          fontSize: '1.2em', 
          color: 'var(--text-color, #333)' 
        }}>
          {children}
        </p> 
      </div>
    </div>
  );
};

// --- Session Summary Component (Placeholder) ---
const SessionSummary = ({ 
  results, 
  targetedTheme, 
  totalTargeted, 
  totalMixed, 
  onStartNew, 
  onReviewMistakes,
  formatThemeName 
}) => {
  // Calculate stats from results
  const successfulPuzzles = results.filter(r => r.outcome === 'success').length;
  const failedPuzzles = results.filter(r => r.outcome === 'failure').length;
  const skippedPuzzles = results.filter(r => r.outcome === 'skipped').length;

  const targetedCorrect = results.filter(r => r.isTargeted && r.outcome === 'success').length;
  const mixedCorrect = results.filter(r => !r.isTargeted && r.outcome === 'success').length;

  const formattedTargetedTheme = formatThemeName(targetedTheme);

  const mistakesExist = failedPuzzles > 0 || skippedPuzzles > 0;

  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h2>Session Complete!</h2>
      <div style={{ margin: '20px 0', fontSize: '1.2em' }}>
        {/* Overall Stats */} 
        <span style={{ color: "#27ae60", fontWeight: "bold" }}>{successfulPuzzles} ✓</span> | 
        <span style={{ color: "#e74c3c", fontWeight: "bold" }}>{failedPuzzles} ✗</span> | 
        <span style={{ color: "#f39c12", fontWeight: "bold" }}>{skippedPuzzles} ⏭</span>
      </div>

      {/* Performance Breakdown */} 
      {totalTargeted > 0 && (
        <p>Targeted Puzzles (<strong>{formattedTargetedTheme}</strong>): {targetedCorrect} / {totalTargeted} correct</p>
      )}
      {totalMixed > 0 && (
        <p>Mixed Puzzles: {mixedCorrect} / {totalMixed} correct</p>
      )}

      {/* Review Button */} 
      {mistakesExist && (
        <button 
          onClick={onReviewMistakes} 
          className="btn session-summary-button review-mistakes-button"
        >
          Review Mistakes
        </button>
      )}

      {/* New Session Button */} 
      <button 
        onClick={onStartNew}
        className={`btn session-summary-button new-session-button ${!mistakesExist ? 'no-mistakes' : ''}`}
      >
        Start New Session
      </button>
    </div>
  );
};
// --- End Session Summary Component ---

// --- Puzzle Review Component (Structure + Auto-Play Logic) ---
const PuzzleReview = ({ puzzlesToReview, onReviewComplete, boardWidth, formatThemeName, getPieceImage }) => {
  const [currentReviewIndex, setCurrentReviewIndex] = useState(0);
  const [reviewGame, setReviewGame] = useState(null);
  const [reviewMoveIndex, setReviewMoveIndex] = useState(-1); // Start at -1 for initial position, 0 is after first move
  const [reviewSquares, setReviewSquares] = useState({});
  const [isAutoPlaying, setIsAutoPlaying] = useState(true); // State for auto-play
  const timeoutRef = useRef(null);

  const currentPuzzleData = puzzlesToReview?.[currentReviewIndex]?.puzzleData;
  const solutionMoves = currentPuzzleData?.moves || [];
  const totalMoves = solutionMoves.length;

  // Function to safely make a move and update state
  const makeReviewMove = (gameInstance, moveIndex) => {
    if (moveIndex < 0 || moveIndex >= totalMoves) return null; // Invalid index
    const moveNotation = solutionMoves[moveIndex];
    const from = moveNotation.slice(0, 2);
    const to = moveNotation.slice(2, 4);
    const promotion = moveNotation.length > 4 ? moveNotation[4] : undefined;
    const moveResult = gameInstance.move({ from, to, promotion });
    if (moveResult) {
      setReviewSquares({ 
        [from]: { backgroundColor: "rgba(255, 255, 0, 0.4)" }, 
        [to]: { backgroundColor: "rgba(255, 255, 0, 0.4)" } 
      });
      playSound(moveResult.captured ? captureSound : moveSound);
    } else {
       console.error("Invalid move during review step:", moveNotation, gameInstance.fen());
    }
    return moveResult;
  }

  // Load the current puzzle for review OR replay to specific move index
  useEffect(() => {
    if (currentPuzzleData) {
      const fen = currentPuzzleData.fen || currentPuzzleData.FEN;
      if (fen) {
        const newGame = new Chess(fen);
        // Replay moves if reviewMoveIndex > -1
        for (let i = 0; i <= reviewMoveIndex; i++) {
          if (i < totalMoves) {
             const success = makeReviewMove(newGame, i); // Make move but don't play sound/highlight yet for replay
             if (!success) {
                console.error("Failed replay at move", i);
                // Decide how to handle replay failure - maybe skip puzzle?
                gotoNextReviewPuzzle();
                return; // Exit effect early
             } 
          }
        }
        setReviewGame(newGame);
        // Set highlight for the *last* completed move if not at start
        if (reviewMoveIndex >= 0 && reviewMoveIndex < totalMoves) {
             const moveNotation = solutionMoves[reviewMoveIndex];
             const from = moveNotation.slice(0, 2);
             const to = moveNotation.slice(2, 4);
             setReviewSquares({ 
                [from]: { backgroundColor: "rgba(255, 255, 0, 0.4)" }, 
                [to]: { backgroundColor: "rgba(255, 255, 0, 0.4)" } 
             });
        } else {
             setReviewSquares({}); // Clear highlights at start/end
        }
      } else {
        gotoNextReviewPuzzle();
      }
    } else if (currentReviewIndex >= puzzlesToReview.length) {
      onReviewComplete();
    }
  }, [currentReviewIndex, puzzlesToReview, reviewMoveIndex]); // Rerun when index changes OR when move index is manually changed

  // Auto-play moves / Handle end-of-puzzle transition
  useEffect(() => {
    // Clear previous timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Condition 1: Auto-play next move if enabled and moves remain
    if (isAutoPlaying && reviewGame && reviewMoveIndex < totalMoves - 1) {
      timeoutRef.current = setTimeout(() => {
        setReviewMoveIndex(prev => prev + 1);
      }, 1500); // Delay between moves
    // Condition 2: ALWAYS schedule transition if last move index is reached/passed
    } else if (reviewGame && reviewMoveIndex >= totalMoves - 1) {
      // Finished moves for this puzzle, pause then go to next
      timeoutRef.current = setTimeout(() => {
        gotoNextReviewPuzzle();
      }, 2000); // Pause after finishing puzzle
    }

    // Cleanup timeout
    return () => clearTimeout(timeoutRef.current);

  }, [reviewGame, reviewMoveIndex, isAutoPlaying, currentReviewIndex, puzzlesToReview, totalMoves]); // Dependencies remain

  const gotoNextReviewPuzzle = () => {
    setIsAutoPlaying(true); // Default to auto-playing next puzzle
    setCurrentReviewIndex(prev => prev + 1);
    setReviewMoveIndex(-1); // Reset move index for the new puzzle
  };

  // Manual Step Handler
  const handleManualStep = (direction) => {
    setIsAutoPlaying(false); // Pause auto-play on manual interaction
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current); // Clear pending auto-step or auto-next puzzle timeout
    }

    const newMoveIndex = reviewMoveIndex + direction;

    // Check bounds (-1 is start, totalMoves-1 is the last move index)
    if (newMoveIndex >= -1 && newMoveIndex < totalMoves) {
       setReviewMoveIndex(newMoveIndex);
       // REMOVED: Timeout logic previously here is now handled by the useEffect above
       // The main useEffect will handle board update and highlighting
    } else {
       console.log("Step out of bounds");
    }
  };

  // Toggle AutoPlay Handler
  const toggleAutoPlay = () => {
    const switchingToAuto = !isAutoPlaying;
    setIsAutoPlaying(switchingToAuto);
    if (!switchingToAuto && timeoutRef.current) {
       clearTimeout(timeoutRef.current); // Clear timeout if pausing
    }
    // If switching to auto, the auto-play useEffect will pick it up
  };

  if (!reviewGame || !currentPuzzleData) {
    // Handle loading or edge case where data might be missing briefly
    return <p>Loading review...</p>; 
  }


  return (
    <div style={{
      padding: '20px',
      width: '100%',
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center'
    }}>
      <Chessboard
        position={reviewGame.fen()}
        boardWidth={boardWidth}
        arePiecesDraggable={false} // Non-interactive
        customBoardStyle={{
          borderRadius: "4px",
          boxShadow: "0 2px 10px rgba(0, 0, 0, 0.5)"
        }}
        customSquareStyles={reviewSquares}
        customPieces={getPieceImage} // Pass piece renderer
      />
      {/* Review Controls */} 
      <div style={{ marginTop: '15px', display: 'flex', justifyContent: 'center', gap: '10px', alignItems: 'center' }}>
          {/* Secondary Button Style + Disabled State */} 
          <button 
            onClick={() => handleManualStep(-1)} 
            disabled={reviewMoveIndex < 0}
            className="btn btn-secondary"
          >
            ◀ Prev
          </button>
          {/* Primary Button Style */} 
          <button 
            onClick={toggleAutoPlay}
            className="btn btn-primary"
          >
            {isAutoPlaying ? "❚❚ Pause" : "▶ Play"}
          </button>
          {/* Secondary Button Style + Disabled State */} 
          <button 
            onClick={() => handleManualStep(1)} 
            disabled={reviewMoveIndex >= totalMoves - 1}
            className="btn btn-secondary"
          >
            Next ▶
          </button>
           {/* Primary Button Style */} 
           <button 
            onClick={onReviewComplete}
            className="btn btn-primary"
           >
             Finish Review
           </button> 
      </div>
       
    </div>
  );
};
// --- End Puzzle Review Component ---

const ChessPuzzle = ({ puzzles: initialPuzzles, onPuzzleComplete, onTrainingComplete, stockfishLoading, showWeaknessOnMount = true }) => {
  // Add analyzeWeakestTactic function
  const analyzeWeakestTactic = (puzzles) => {
    if (!puzzles || puzzles.length === 0) return null;

    // Count missed tactics by theme
    const themeCounts = {};
    const tacticalThemes = ["pin", "fork", "mateIn1", "hangingPiece"];
    
    puzzles.forEach(puzzle => {
      if (puzzle.theme === "opponent_mistake_missed" || puzzle.theme === "opponent_blunder_missed") {
        // Only count tactical themes we care about
        const tacticalTag = puzzle.tags.find(tag => tacticalThemes.includes(tag));
        if (tacticalTag) {
          themeCounts[tacticalTag] = (themeCounts[tacticalTag] || 0) + 1;
        }
      }
    });

    // Find theme with highest count
    let maxCount = 0;
    let weakestTheme = null;
    Object.entries(themeCounts).forEach(([theme, count]) => {
      if (count > maxCount) {
        maxCount = count;
        weakestTheme = theme;
      }
    });

    return weakestTheme ? { theme: weakestTheme, count: maxCount } : null;
  };

  // Helper to format theme names for display (e.g., "mateIn1" -> "mate in 1", "hangingPiece" -> "hanging piece")
  const formatThemeName = (theme) => {
    // Separate word part from optional trailing number
    const match = theme.match(/^([a-zA-Z]+?)(\d*)$/);
    
    let wordPart = theme;
    let numberPart = "";

    if (match && match[1]) {
      wordPart = match[1];
      numberPart = match[2] || ""; // Ensure numberPart is a string
    }

    // Split camelCase, trim, and convert to lowercase
    const formattedWord = wordPart
      .replace(/([A-Z])/g, ' $1') // Insert space before caps
      .trim()
      .toLowerCase();

    // Combine word and number part if number exists
    return numberPart ? `${formattedWord} ${numberPart}` : formattedWord;
  };

  // --- State Variables --- 
  const [currentLoadedPuzzleIndex, setCurrentLoadedPuzzleIndex] = useState(null); 
  const [usedPuzzleIndices, setUsedPuzzleIndices] = useState(new Set());
  const [game, setGame] = useState(null);
  const [message, setMessage] = useState("");
  const [customSquares, setCustomSquares] = useState({});
  const [moveSquares, setMoveSquares] = useState({});
  const [optionSquares, setOptionSquares] = useState({});
  const [showingHint, setShowingHint] = useState(false);
  const [boardWidth, setBoardWidth] = useState(400);
  const [boardOrientation, setBoardOrientation] = useState("white");
  const boardContainerRef = React.useRef(null);
  const [moves, setMoves] = useState(0); // Total moves in current puzzle - Still needed for onDrop logic
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [totalAvailablePuzzles, setTotalAvailablePuzzles] = useState(0);
  const [streak, setStreak] = useState(0);
  const goodMovesCount = useRef(0);
  const [isLoadingPuzzle, setIsLoadingPuzzle] = useState(false);
  const isLoadingRef = useRef(false);
  const scoreBefore = useRef(null);
  const bestMoveBefore = useRef(null);
  const [showWeaknessOverlay, setShowWeaknessOverlay] = useState(false);
  const [weaknessMessage, setWeaknessMessage] = useState(null); 

  // --- Session Tracking State --- 
  const [isSessionComplete, setIsSessionComplete] = useState(false);
  const [sessionResults, setSessionResults] = useState([]); 
  const [targetedTheme, setTargetedTheme] = useState(""); 
  const [targetedPuzzleCount, setTargetedPuzzleCount] = useState(0); 
  const [mixedPuzzleCount, setMixedPuzzleCount] = useState(0); 
  
  // --- Review Mode State --- 
  const [isReviewing, setIsReviewing] = useState(false);
  const [puzzlesToReview, setPuzzlesToReview] = useState([]);
  // --- End New State --- 

  // Set puzzles from props when they change
  useEffect(() => {
    if (initialPuzzles && initialPuzzles.length > 0 && isInitialLoad) {
      const TARGET_SESSION_SIZE = 20;
      // Create a mixture of targeted and mixed puzzles
      const calculatedTargetedCount = Math.floor(TARGET_SESSION_SIZE * 0.7); // 70% targeted
      const calculatedMixedCount = TARGET_SESSION_SIZE - calculatedTargetedCount; // 30% mixed

      // Get the weakest theme based on ALL initial puzzles
      const weakness = analyzeWeakestTactic(initialPuzzles);
      const identifiedWeakestTheme = weakness?.theme || null; // Can be null if no weakness found
      setTargetedTheme(identifiedWeakestTheme || "Mixed"); // Store the theme being targeted (or 'Mixed' if none)
      
      let potentialTargetedPuzzles = [];
      let potentialOtherPuzzles = [];

      // Define the sorting function based on moves length (shortest first)
      const sortByMovesLength = (a, b) => {
          const lengthA = a.moves?.length ?? Infinity; // Treat missing moves as very long
          const lengthB = b.moves?.length ?? Infinity;
          return lengthA - lengthB;
      };

      // Filter initial puzzles into targeted/other pools
      if (identifiedWeakestTheme) {
        potentialTargetedPuzzles = initialPuzzles.filter(p => (p.theme === identifiedWeakestTheme || (p.tags && p.tags.includes(identifiedWeakestTheme))) && !usedPuzzleIndices.has(initialPuzzles.indexOf(p)));
        potentialOtherPuzzles = initialPuzzles.filter(p => !(p.theme === identifiedWeakestTheme || (p.tags && p.tags.includes(identifiedWeakestTheme))) && !usedPuzzleIndices.has(initialPuzzles.indexOf(p)));
      } else {
        // If no specific weakness, all available puzzles are 'other'
        potentialTargetedPuzzles = [];
        potentialOtherPuzzles = initialPuzzles.filter(p => !usedPuzzleIndices.has(initialPuzzles.indexOf(p)));
      }

      // Sort potential puzzles by moves length (shortest first)
      potentialTargetedPuzzles.sort(sortByMovesLength);
      potentialOtherPuzzles.sort(sortByMovesLength);

      // Take the required number from the *sorted* lists, ensuring we don't exceed available
      // This selects the *easiest* available puzzles up to the quota for each group.
      const actualTargetedPuzzles = potentialTargetedPuzzles.slice(0, calculatedTargetedCount);
      const actualMixedPuzzles = potentialOtherPuzzles.slice(0, calculatedMixedCount);
      
      // Store the actual counts for the summary
      setTargetedPuzzleCount(actualTargetedPuzzles.length);
      setMixedPuzzleCount(actualMixedPuzzles.length);

      // Combine the selected easiest puzzles and shuffle for the session
      const combinedPuzzles = [...actualTargetedPuzzles, ...actualMixedPuzzles].sort(() => Math.random() - 0.5);
      
      let shouldShowOverlay = false; // Flag to track if overlay will be shown
      // Show weakness overlay if applicable
      if (showWeaknessOnMount && weakness && actualTargetedPuzzles.length > 0) { // Show overlay only if weakest theme is represented and allowed
        const formattedTheme = formatThemeName(weakness.theme);
        const messageJsx = (
          <>
            Based on recent games, you need to improve your{' '}
            <strong>{formattedTheme}</strong> tactics (missed {weakness.count} times).
          </>
        );
        setWeaknessMessage(messageJsx);
        setShowWeaknessOverlay(true);
        shouldShowOverlay = true;
        // console.log("Initial load: Weakness found, showing overlay");
      } else {
        // console.log("Initial load: No specific weakness found or not represented, not showing overlay");
      }

        setIsInitialLoad(false);
      if (!shouldShowOverlay) {
          // console.log("Initial load: No overlay, loading first puzzle immediately.")
          loadNextPuzzle(); // Make sure this uses the combinedPuzzles, or loadPuzzle needs access
      }
       // NOTE: We need to make loadNextPuzzle aware of the 'combinedPuzzles' list
       // Easiest might be to add it to state, or pass it around.
       // For now, loadNextPuzzle still pulls from initialPuzzles, which needs fixing if combinedPuzzles is the definitive list for the session.
    }
  }, [initialPuzzles, isInitialLoad]);

  // Add totalAvailablePuzzles state and calculation
  useEffect(() => {
    if (initialPuzzles) {
      const filtered = filterPuzzles(initialPuzzles);
      setTotalAvailablePuzzles(filtered.length);
    }
  }, [initialPuzzles]);

  const loadNextPuzzle = async () => {
    console.log("loadNextPuzzle called");
    if (isLoadingRef.current) {
      console.log("Puzzle already loading, skipping...");
      return;
    }
    
    // Determine which puzzles are available *for this session* 
    // We need the list generated in the useEffect. Let's add state for it.
    // const availableSessionIndices = sessionPuzzleList.map(p => initialPuzzles.indexOf(p)).filter(idx => !usedPuzzleIndices.has(idx));

    // --- RETHINK --- Get *all* unused puzzle indices from initialPuzzles
    // This allows continuing beyond the initial 20 if more are available.
    let unusedIndicesList = initialPuzzles
      .map((_, index) => index)
      .filter(index => {
        const puzzle = initialPuzzles[index];
        // Basic filter check (can be expanded)
        return !usedPuzzleIndices.has(index) && puzzle.fen && puzzle.moves?.length > 0;
      });

    if (unusedIndicesList.length === 0) {
      console.log("No unused puzzles available - Session Complete!");
      setIsSessionComplete(true); // Set session complete flag
      // Maybe call onTrainingComplete here? Pass sessionResults?
      if (onTrainingComplete) {
        onTrainingComplete(sessionResults); // Pass results up
      }
      return;
    }
    
    // Randomly select an unused puzzle from the available list
    const randomIndex = unusedIndicesList[Math.floor(Math.random() * unusedIndicesList.length)];
    console.log("Selected puzzle index from initialPuzzles:", randomIndex);

    await loadPuzzle(randomIndex); // Load puzzle using its original index
  };

  const handlePuzzleCompletion = async (completed = true) => {
    const outcome = completed ? 'success' : 'failure';
    const puzzleData = initialPuzzles[currentLoadedPuzzleIndex];
    const isTargeted = !!targetedTheme && (puzzleData.theme === targetedTheme || puzzleData.tags?.includes(targetedTheme));
    
    // Store result
    setSessionResults(prev => [...prev, { puzzleData, outcome, isTargeted }]);

    // Update streak
    if (completed) {
      setStreak(prev => prev + 1);
      playSound(confirmationSound);
    } else {
      setStreak(0);
      playSound(errorSound);
    }

    setMessage(completed ? "Correct!" : "Incorrect."); // Give feedback
    await sleep(1500); // Shorter delay
    setMessage(""); // Clear feedback

    await loadNextPuzzle();
  };

  // Modify loadPuzzle to just take the original index
  const loadPuzzle = async (index) => {
    console.log("loadPuzzle called - index:", index, "isLoadingPuzzle:", isLoadingPuzzle);
    if (isLoadingRef.current) {
      console.log("Puzzle load already in progress, skipping");
      return;
    }

    isLoadingRef.current = true;
    setIsLoadingPuzzle(true);
    try {
      if (!initialPuzzles || !initialPuzzles[index]) {
        console.error("Invalid puzzle data or index:", { index, initialPuzzles });
        return;
      }

      const puzzle = initialPuzzles[index];
      console.log("Loading puzzle:", puzzle);
      // Mark this puzzle as used
      setUsedPuzzleIndices(prev => new Set([...prev, index]));
      // Handle both 'fen' and 'FEN' property names
      const fen = puzzle.fen || puzzle.FEN;
      if (!fen) {
        console.error("Puzzle is missing FEN position:", puzzle);
        return;
      }
      
      const newGame = new Chess(fen);
      
      // Set board orientation based on who moves first
      setBoardOrientation(fen.split(" ")[1] === "b" ? "white" : "black");

      setMoveSquares({});
      setOptionSquares({});
      setCustomSquares({});
      setShowingHint(false);
      setMoves(puzzle.moves?.length || 0);

      // Set the initial position first
      setGame(newGame);

      // Make the first move after a delay
      if (puzzle.moves && puzzle.moves.length > 0) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        
        const firstMove = puzzle.moves[0];
        const from = firstMove.slice(0, 2);
        const to = firstMove.slice(2, 4);
        const promotion = firstMove.length > 4 ? firstMove[4] : undefined;
        
        // Make the move on the game instance
        const moveResult = newGame.move({
          from,
          to,
          promotion: promotion
        });
        
        if (moveResult) {
          // Play appropriate sound
          if (moveResult.captured) {
            playSound(captureSound);
          } else {
            playSound(moveSound);
          }
          
          // Highlight the move
          highlightLastMove(from, to);

          // Get initial evaluation after the server's move
          const initialEval = await evaluatePosition(newGame);
          scoreBefore.current = initialEval.score;
          bestMoveBefore.current = initialEval.bestMove;
          console.log("Setting initial evaluation after server move:", initialEval);

          // Set the game state after all moves and evaluations are done
          setGame(newGame);
        }
      }
      goodMovesCount.current = 0;
    } finally {
      isLoadingRef.current = false;
      setIsLoadingPuzzle(false);
    }
    // Store the index being loaded
    setCurrentLoadedPuzzleIndex(index); 
  };


  // Add this function to track skipped puzzles
  const handleSkipPuzzle = async () => {
    const puzzleData = initialPuzzles[currentLoadedPuzzleIndex];
    const isTargeted = !!targetedTheme && (puzzleData.theme === targetedTheme || puzzleData.tags?.includes(targetedTheme));

    // Store result
    setSessionResults(prev => [...prev, { puzzleData, outcome: 'skipped', isTargeted }]);
    setStreak(0); // Reset streak on skip

    setMessage("Puzzle Skipped");
    await sleep(1000);
    setMessage("");
    
    await loadNextPuzzle();
  };

  // Function to calculate board width based on container and window size
  const calculateBoardWidth = () => {
    if (!boardContainerRef.current) return;

    const containerWidth = boardContainerRef.current.clientWidth;
    const windowHeight = window.innerHeight;

    // Calculate maximum possible width while maintaining aspect ratio
    const maxWidth = Math.min(
      containerWidth - 40, // Leave some padding
      windowHeight * 0.7 // Don't let board height exceed 70% of window height
    );

    // Ensure width is at least 200px and at most 800px
    const newWidth = Math.max(200, Math.min(800, maxWidth));
    setBoardWidth(newWidth);
  };

  // Set up resize observer and window resize listener
  useEffect(() => {
    const resizeObserver = new ResizeObserver(calculateBoardWidth);
    if (boardContainerRef.current) {
      resizeObserver.observe(boardContainerRef.current);
    }

    window.addEventListener("resize", calculateBoardWidth);

    // Initial calculation
    calculateBoardWidth();

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener("resize", calculateBoardWidth);
    };
  }, []);

  // Add useEffect to handle window resize
  useEffect(() => {
    const handleResize = () => {
      const width = Math.min(window.innerWidth - 40, 400);
      setBoardWidth(width);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const wasmThreadsSupported = () => {
    // WebAssembly 1.0
    const source = Uint8Array.of(0x0, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00);
    if (
      typeof WebAssembly !== "object" ||
      typeof WebAssembly.validate !== "function"
    )
      return false;
    if (!WebAssembly.validate(source)) return false;

    // SharedArrayBuffer
    // if (typeof SharedArrayBuffer !== "function") return false;

    // Atomics
    if (typeof Atomics !== "object") return false;

    // Shared memory
    const mem = new WebAssembly.Memory({
      shared: true,
      initial: 8,
      maximum: 16,
    });
    // if (!(mem.buffer instanceof SharedArrayBuffer)) return false;

    // Structured cloning
    try {
      window.postMessage(mem, "*");
    } catch (e) {
      return false;
    }

    // Growable shared memory (optional)
    try {
      mem.grow(8);
    } catch (e) {
      return false;
    }

    return true;
  };

  useEffect(() => {
    if (!wasmThreadsSupported()) {
      console.error("WebAssembly threads not supported");
      return;
    }
  }, []);

  const highlightLastMove = (sourceSquare, targetSquare) => {
    const newSquares = {};
    newSquares[sourceSquare] = { backgroundColor: "rgba(255, 255, 0, 0.4)" };
    newSquares[targetSquare] = { backgroundColor: "rgba(255, 255, 0, 0.4)" };
    setMoveSquares(newSquares);
  };

  const showHint = async () => {
    if (!game) return;
    const stockfish = await Sf167Web();
    if (!stockfish) return;

    const listener = (line) => {
      if (line.includes("bestmove")) {
        const bestMove = line.split("bestmove ")[1].split(" ")[0];
        const from = bestMove.slice(0, 2);
        const to = bestMove.slice(2, 4);

        const newSquares = {};
        newSquares[from] = {
          background: "rgba(21, 120, 27, 0.4)",
          borderRadius: "50%",
        };
        newSquares[to] = {
          background: "rgba(21, 120, 27, 0.4)",
          borderRadius: "50%",
        };
        setCustomSquares(newSquares);
        setShowingHint(true);

        setTimeout(() => {
          setCustomSquares({});
          setShowingHint(false);
        }, 2000);

        stockfish.uci("quit");
      }
    };

    stockfish.listen = listener;
    stockfish.uci("position fen " + game.fen());
    stockfish.uci("go depth 20");
  };

  // Helper function to check if a move is a promotion move
  const isPromotion = (sourceSquare, targetSquare, game) => {
    const piece = game.get(sourceSquare);

    if (!piece || piece.type !== "p") return false;

    const targetRank = targetSquare[1];
    return (
      (piece.color === "w" && targetRank === "8") ||
      (piece.color === "b" && targetRank === "1")
    );
  };

  const isValidMove = (sourceSquare, targetSquare) => {
    try {
      const moveGame = new Chess(game.fen());

      // Try to make the move
      let move;

      if (isPromotion(sourceSquare, targetSquare, moveGame)) {
        move = moveGame.move({
          from: sourceSquare,
          to: targetSquare,
          promotion: "q",
        });
      } else {
        move = moveGame.move({ from: sourceSquare, to: targetSquare });
      }

      // If move is null, it's an invalid move
      if (move === null) {
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error in isValidMove:", error);
      return false;
    }
  };

  const makeMove = (sourceSquare, targetSquare, promotion) => {
    let moveResult;
    if (isPromotion(sourceSquare, targetSquare, game)) {
      moveResult = game.move({
        from: sourceSquare,
        to: targetSquare,
        promotion: promotion,
      });
    } else {
      moveResult = game.move({ from: sourceSquare, to: targetSquare });
    }
    // Play appropriate sound based on whether it was a capture
    if (moveResult.captured) {
      playSound(captureSound);
    } else {
      playSound(moveSound);
    }
    setGame(game);
    return new Promise((resolve) => {
      resolve(moveResult);
    });
  };

  const computerReacts = async () => {
    if (!game) return;
    const stockfish = await Sf167Web();
    if (!stockfish) return;

    return new Promise((resolve) => {
      const listener = (line) => {
        if (line.includes("bestmove")) {
          const bestMove = line.split("bestmove ")[1].split(" ")[0];
          const from = bestMove.slice(0, 2);
          const to = bestMove.slice(2, 4);
          const promotion = bestMove.length > 4 ? bestMove[4] : undefined;

          makeMove(from, to, promotion);
          // Store the computer's move to highlight later
          setMoveSquares({
            [from]: { backgroundColor: "rgba(255, 255, 0, 0.4)" },
            [to]: { backgroundColor: "rgba(255, 255, 0, 0.4)" }
          });
          stockfish.uci("quit");
          resolve();
        }
      };

      stockfish.listen = listener;
      stockfish.uci("position fen " + game.fen());
      stockfish.uci("go depth 20");
    });
  };
  // Add this helper function at the top level
  const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  const evaluatePosition = async (game) => {
    const stockfish = await Sf167Web();
    return new Promise((resolve) => {
      let score = null;
      let bestMove = null;

      stockfish.listen = (line) => {
        if (line.startsWith("info")) {
          const scoreMatch = line.match(/score (cp|mate) (-?\d+)/);
          if (scoreMatch) {
            const scoreType = scoreMatch[1];
            const rawScore = parseFloat(scoreMatch[2]);
            
            if (scoreType === "cp") {
              score = rawScore / 100; // Convert centipawns to pawns
            } else if (scoreType === "mate") {
              score = rawScore > 0 ? 100 : -100;
            }
          }
        } else if (line.startsWith("bestmove")) {
          bestMove = line.split(" ")[1];
          console.log("Final evaluation:", score);
          resolve({ score, bestMove });
          stockfish.uci("quit");
        }
      };

      stockfish.uci(`position fen ${game.fen()}`);
      stockfish.uci("go depth 20");
    });
  };

  // Modify PuzzleStats component to show remaining puzzles
  const PuzzleStats = ({ streak, totalAvailable, usedCount, sessionResults }) => {
    const remainingPuzzles = Math.max(0, totalAvailable - usedCount);
    // Get live counts from sessionResults for display *during* session
    const successfulCurrent = sessionResults.filter(r => r.outcome === 'success').length;
    const failedCurrent = sessionResults.filter(r => r.outcome === 'failure').length;
    const skippedCurrent = sessionResults.filter(r => r.outcome === 'skipped').length;

    return (
      <div style={{ /*marginBottom: "20px" */ }}> {/* Reduce margin? */} 
        <p
          style={{
            fontSize: "1em",
            marginBottom: "8px",
            color: "#2c3e50"
          }}
        >
          {/* Display potentially approximate remaining count */}
          <span style={{ fontWeight: "bold" }}>
            Remaining: {remainingPuzzles} 
          </span>
          {" | "}
          <span style={{ color: "#27ae60", fontWeight: "bold" }}>
            {successfulCurrent} ✓
          </span>
          {" | "}
          <span style={{ color: "#e74c3c", fontWeight: "bold" }}>
            {failedCurrent} ✗
          </span>
          {" | "}
          <span style={{ color: "#f39c12", fontWeight: "bold" }}>
            {skippedCurrent} ⏭
          </span>
          <>
            {" | "}
            <span title={`Current Streak: ${streak}`} style={{ fontWeight: "bold", color: "#f39c12" }}>
              🔥 {streak}
          </span>
          </>
        </p>
      </div>
    );
  };

  const onDrop = async (sourceSquare, targetSquare, promotion) => {
    setMoveSquares({
    });
    if (!isValidMove(sourceSquare, targetSquare)) {
      setMessage("Invalid move");
      return;
    }
    // Make the move immediately
    let newMoveCount = await makeMove(
      sourceSquare,
      targetSquare,
      promotion.slice(1, 2).toLowerCase()
    );

    // Wait for evaluation before proceeding
    await (async () => {
      console.log("=== Starting background evaluation ===");
      
      // Get the move the player actually made (from the result of makeMove)
      // Note: 'newMoveCount' here actually holds the chess.js move object
      const playerMoveObject = newMoveCount; 
      const playerMovePlayedUCI = playerMoveObject.from + playerMoveObject.to + (playerMoveObject.promotion || '');
      console.log("Player move played (UCI reconstructed):", playerMovePlayedUCI);
      
      // Check for checkmate first using chess.js
      // Use a fresh game instance based on the state *after* the player's move
      const gameAfterMove = new Chess(game.fen());
      if (gameAfterMove.isCheckmate()) {
        console.log("Checkmate found!");
        setMessage("Great, you found the best move!");
        goodMovesCount.current += 1;
        return;
      }

      // Evaluate position after move using a copy
      console.log("Evaluating position after move...");
      console.log("Position after move FEN:", gameAfterMove.fen());
      const { score, bestMove } = await evaluatePosition(gameAfterMove);
      const scoreAfter = -score; // Adjust score for player's perspective
      console.log("After move evaluation:", scoreAfter);
      
      // --- Get Expected Puzzle Move --- 
      let expectedPuzzleMoveUCI = null;
      let playerMadeCorrectPuzzleMove = false;
      const playerMoveIndex = game.history().length - 1; // Index of the move just played
      const currentPuzzleData = initialPuzzles[currentLoadedPuzzleIndex];

      if (currentPuzzleData && currentPuzzleData.moves && playerMoveIndex < currentPuzzleData.moves.length) {
          expectedPuzzleMoveUCI = currentPuzzleData.moves[playerMoveIndex];
          playerMadeCorrectPuzzleMove = (playerMovePlayedUCI === expectedPuzzleMoveUCI);
          console.log(`Comparing Player Move: ${playerMovePlayedUCI} with Expected Puzzle Move #${playerMoveIndex + 1}: ${expectedPuzzleMoveUCI} => Match: ${playerMadeCorrectPuzzleMove}`);
      } else {
          console.warn(`Could not find expected puzzle move at index ${playerMoveIndex}`);
      }
      // --- End Get Expected Puzzle Move --- 
      
      if (scoreBefore.current !== null && scoreAfter !== null) {
        const scoreChange = Math.abs(scoreAfter - scoreBefore.current);
        console.log("Score change:", scoreChange);
        
        // Get the player's move in UCI format (already have playerMovePlayedUCI)
        console.log("Best move before (Engine Thought):", bestMoveBefore.current);
        
        // --- MODIFY DECISION LOGIC --- 
        // A move is good if either:
        // 1. It loses less than a pawn of advantage
        // 2. OR it matches Stockfish's best move for the player's position
        // 3. OR it matches the exact move from the puzzle solution
        if (scoreChange <= 0.5 || playerMovePlayedUCI === bestMoveBefore.current || playerMadeCorrectPuzzleMove) {
          console.log("Good move determined! Conditions met:", { scoreChangeOk: scoreChange <= 0.5, matchedBestBefore: playerMovePlayedUCI === bestMoveBefore.current, matchedPuzzleMove: playerMadeCorrectPuzzleMove });
          setMessage("Great, you found the best move!");
          goodMovesCount.current += 1;
        } else {
          console.log("Better move exists! Conditions NOT met:", { scoreChangeOk: scoreChange <= 0.5, matchedBestBefore: playerMovePlayedUCI === bestMoveBefore.current, matchedPuzzleMove: playerMadeCorrectPuzzleMove });
          setMessage("There is a better move");
        }
        // --- END MODIFY DECISION LOGIC --- 
      }
      console.log("=== Background evaluation complete ===");
    })();

    // --- MOVED CHECK --- 
    // Check if the puzzle length is met *after* the player's move and evaluation
    if (game.history().length >= moves) {
       console.log("Puzzle complete by reaching expected move count! History:", game.history().length, "Expected:", moves);
       // Determine success based on player making enough good moves 
       const requiredGoodMoves = Math.ceil(moves / 2); // Player needs roughly half the moves
       const puzzleSuccess = goodMovesCount.current >= requiredGoodMoves;
       console.log(`Good moves: ${goodMovesCount.current}, Required: ${requiredGoodMoves}, Success: ${puzzleSuccess}`);
       await handlePuzzleCompletion(puzzleSuccess);
       return true; // Puzzle finished, do not proceed to computer move
    }
    // --- END MOVED CHECK --- 

    // Add delay before computer's move
    await sleep(1000);
    // console.log("newMoveCount:", newMoveCount); // newMoveCount is not defined here
    console.log("current puzzle length", moves);
    // This check was here before, but it was logically incorrect and relied on newMoveCount
    // if (newMoveCount >= moves) { 
    //   console.log("Puzzle complete! Good moves:", goodMovesCount.current);
    //   await handlePuzzleCompletion(goodMovesCount.current === moves/2);
    //   return true;
    // }

    // Now let the computer react
    console.log("Computer reacting...");
    await computerReacts(); // Assuming this updates the main 'game' state

    // Check if computer checkmated player
    if (game.isCheckmate()) {
      console.log("Checkmate after computer move!");
      await handlePuzzleCompletion(false); // Computer won, player failed
      return true;
    }

    // Remove the duplicate check that was here
    // if (game.history().length >= moves) { ... }

    console.log("=== onDrop complete (puzzle not finished) ===");
    return true; // Move successful, but puzzle continues
  };

  // Modify getPieceImage to use responsive piece size
  const getPieceImage = (piece) => {
    const pieceSize = Math.floor(boardWidth / 8);
    return (
      <img
        src={`/pieces/${piece.type}${piece.color}.svg`}
        alt={`${piece.color} ${piece.type}`}
        style={{
          width: pieceSize,
          height: pieceSize,
          objectFit: 'contain'
        }}
      />
    );
  };

  // --- New Handlers for Review Mode ---
  const handleStartReview = () => {
    const mistakes = sessionResults.filter(r => r.outcome === 'failure' || r.outcome === 'skipped');
    if (mistakes.length > 0) {
      setPuzzlesToReview(mistakes);
      setIsReviewing(true); // Enter review mode
      // The PuzzleReview component will handle loading the first puzzle via its useEffect
    } else {
      console.log("No mistakes to review.");
      // Optionally show a message to the user
    }
  };

  const handleReviewComplete = () => {
    setIsReviewing(false);
    setPuzzlesToReview([]); // Clear the review list
    // User is now back at the SessionSummary screen
  };
  // --- End Review Handlers --- 

  const handleStartNewSession = () => {
    setIsSessionComplete(false);
    setIsReviewing(false); // Ensure review mode is off
    setPuzzlesToReview([]);
    setSessionResults([]);
    setTargetedTheme("");
    setTargetedPuzzleCount(0);
    setMixedPuzzleCount(0);
    setCurrentLoadedPuzzleIndex(null);
    setStreak(0);
    setUsedPuzzleIndices(new Set()); 
    // --- ADD RESETS --- 
    setGame(null);             // Clear the previous game state immediately
    setIsLoadingPuzzle(true);  // Set back to loading state
    setMessage("");             // Clear any previous messages
    setMoveSquares({});        // Clear board highlights
    setCustomSquares({});
    setOptionSquares({});
    // --- END RESETS --- 
    setIsInitialLoad(true); // Trigger the initial load useEffect for the new session
  };

  // Instead, add a handler to close the overlay manually
  const handleCloseWeaknessOverlay = () => {
    setShowWeaknessOverlay(false);
    loadNextPuzzle(); // Load the first puzzle after closing overlay
  };

  return (
    <div className="chess-puzzle-container" style={{ padding: '20px' }}>
      {/* Weakness Overlay (only shown during initial load) */} 
      {!isSessionComplete && !isReviewing && showWeaknessOverlay && (
        <WeaknessOverlay onClose={handleCloseWeaknessOverlay}>{weaknessMessage}</WeaknessOverlay>
      )}

      {/* Conditional Rendering: Summary, Review, or Puzzle */} 
      {isSessionComplete && !isReviewing ? (
        // --- Show Session Summary --- 
        <SessionSummary 
          results={sessionResults}
          targetedTheme={targetedTheme}
          totalTargeted={targetedPuzzleCount}
          totalMixed={mixedPuzzleCount}
          onStartNew={handleStartNewSession}
          onReviewMistakes={handleStartReview} // Pass review handler
          formatThemeName={formatThemeName}
        />
      ) : isReviewing ? (
        // --- Show Puzzle Review --- 
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
          <PuzzleReview 
            puzzlesToReview={puzzlesToReview} 
            onReviewComplete={handleReviewComplete}
            boardWidth={boardWidth} 
            formatThemeName={formatThemeName} 
            getPieceImage={getPieceImage} // Pass getPieceImage as prop
          />
        </div>
      ) : (
        // --- Show Interactive Puzzle View --- 
        <> 
          {/* ... (PuzzleStats component as before) ... */}
      <div className="stats-panel" style={{ 
        marginBottom: '20px',
        fontSize: 'clamp(14px, 3vw, 16px)'
      }}>
        <PuzzleStats
          streak={streak}
              totalAvailable={totalAvailablePuzzles} 
              usedCount={usedPuzzleIndices.size}
              sessionResults={sessionResults} 
        />
      </div>

          {/* ... (Chessboard container as before) ... */} 
      <div ref={boardContainerRef} style={{ 
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        marginBottom: '20px'
      }}>
            {game && (
        <Chessboard
                position={game.fen()} 
          onPieceDrop={onDrop}
          boardWidth={boardWidth}
                arePiecesDraggable={true} // Ensure draggable during play
          customBoardStyle={{
            borderRadius: "4px",
            boxShadow: "0 2px 10px rgba(0, 0, 0, 0.5)"
          }}
          customSquareStyles={{
            ...moveSquares,
            ...optionSquares,
            ...customSquares
          }}
          customPieces={getPieceImage}
          boardOrientation={boardOrientation}
        />
            )}
             {!game && isLoadingPuzzle && <p>Loading puzzle...</p>} 
      </div>

          {/* ... (Controls container as before) ... */} 
      <div className="controls" style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '10px',
        alignItems: 'center'
      }}>
        <div style={{ 
          display: 'flex',
          gap: '10px',
          flexWrap: 'wrap',
          justifyContent: 'center'
        }}>
          <button
            onClick={handleSkipPuzzle}
                className="btn btn-primary"
          >
            Skip Puzzle
          </button>
          <button
            onClick={showHint}
                disabled={showingHint} 
                className="btn btn-primary"
              >
                {showingHint ? "Showing Hint..." : "Show Hint"}
          </button>
        </div>
        {message && (
          <div style={{
            marginTop: '10px',
            fontSize: 'clamp(14px, 3vw, 16px)',
            textAlign: 'center'
          }}>
            {message}
          </div>
        )}
      </div>
        </> 
      )} 
    </div>
  );
};

export default ChessPuzzle;
