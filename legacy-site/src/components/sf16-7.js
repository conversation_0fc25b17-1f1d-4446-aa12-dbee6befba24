/* eslint-disable no-restricted-globals */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-undef */

var Sf167Web = (() => {
  var _scriptName = import.meta.url;
  
  return (
function(moduleArg = {}) {
  var moduleRtn;

function h(){k.buffer!=l.buffer&&p();return l}function aa(){k.buffer!=l.buffer&&p();return ba}function ca(){k.buffer!=l.buffer&&p();return da}function q(){k.buffer!=l.buffer&&p();return ea}function t(){k.buffer!=l.buffer&&p();return fa}function ja(){k.buffer!=l.buffer&&p();return ka}var u=moduleArg,la,ma,na=new Promise((a,b)=>{la=a;ma=b}),oa="object"==typeof window,v="undefined"!=typeof WorkerGlobalScope,w=v&&self.name?.startsWith("em-pthread");u.listen||(u.listen=a=>console.log(a));
u.onError||(u.onError=a=>console.error(a));u.getRecommendedNnue=(a=0)=>pa(qa(a));u.setNnueBuffer=function(a,b=0){if(!a)throw Error("buf is null");if(0>=a.byteLength)throw Error(`${a.byteLength} bytes?`);const c=ra(a.byteLength);if(!c)throw Error(`could not allocate ${a.byteLength} bytes`);u.HEAPU8.set(a,c);sa(c,a.byteLength,b)};u.uci=function(a){const b=ta(a)+1,c=ra(b);if(!c)throw Error(`Could not allocate ${b} bytes`);x(a,c,b);ua(c)};u.print=a=>u.listen?.(a);u.printErr=a=>u.onError?.(a);
var va=Object.assign({},u),wa=[],y="",xa,ya;
if(oa||v)v?y=self.location.href:"undefined"!=typeof document&&document.currentScript&&(y=document.currentScript.src),_scriptName&&(y=_scriptName),y.startsWith("blob:")?y="":y=y.substr(0,y.replace(/[?#].*/,"").lastIndexOf("/")+1),v&&(ya=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.responseType="arraybuffer";b.send(null);return new Uint8Array(b.response)}),xa=async a=>{a=await fetch(a,{credentials:"same-origin"});if(a.ok)return a.arrayBuffer();throw Error(a.status+" : "+a.url);};
var za=u.print||console.log.bind(console),A=u.printErr||console.error.bind(console);Object.assign(u,va);va=null;var k,Aa,Ba=!1,B,l,ba,da,ea,fa,ka;function p(){var a=k.buffer;l=new Int8Array(a);da=new Int16Array(a);u.HEAPU8=ba=new Uint8Array(a);new Uint16Array(a);ea=new Int32Array(a);fa=new Uint32Array(a);new Float32Array(a);ka=new Float64Array(a)}
if(w){var Ca,Da=!1;function a(...c){console.error(c.join(" "))}u.printErr||(A=a);self.alert=function(...c){postMessage({ha:"alert",text:c.join(" "),ib:Ea()})};self.onunhandledrejection=c=>{throw c.reason||c;};function b(c){try{var d=c.data,e=d.ha;if("load"===e){let f=[];self.onmessage=g=>f.push(g);self.startWorker=()=>{postMessage({ha:"loaded"});for(let g of f)b(g);self.onmessage=b};for(const g of d.Ya)if(!u[g]||u[g].proxy)u[g]=(...m)=>{postMessage({ha:"callHandler",Xa:g,Va:m})},"print"==g&&(za=u[g]),
"printErr"==g&&(A=u[g]);k=d.lb;p();Ca(d.mb)}else if("run"===e){Fa(d.ka);Ga(d.ka,0,0,1,0,0);Ha();Ia(d.ka);Da||=!0;try{Ja(d.gb,d.Ca)}catch(f){if("unwind"!=f)throw f;}}else"setimmediate"!==d.target&&("checkMailbox"===e?Da&&Ka():e&&(A(`worker: received unknown command ${e}`),A(d)))}catch(f){throw La(),f;}}self.onmessage=b}w||(u.wasmMemory?k=u.wasmMemory:k=new WebAssembly.Memory({initial:1024,maximum:32768,shared:!0}),p());var Ma=[],Na=[],Oa=[],Pa=[],Qa=[],Ra=!1;
function Sa(){w?startWorker(u):(u.noFSInit||Ta||(Ta=!0,Ua("/dev/tty","/dev/stdin"),Ua("/dev/tty","/dev/stdout"),Ua("/dev/tty1","/dev/stderr"),Wa("/dev/stdin",0),Wa("/dev/stdout",1),Wa("/dev/stderr",1)),Xa=!1,C(Na))}var D=0,E=null;function Ya(){D--;if(0==D&&E){var a=E;E=null;a()}}function F(a){a="Aborted("+a+")";A(a);Ba=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");ma(a);throw a;}var Za=a=>a.startsWith("data:application/octet-stream;base64,"),$a;
async function ab(a){try{var b=await xa(a);return new Uint8Array(b)}catch{}if(ya)a=ya(a);else throw"both async and sync fetching of the wasm failed";return a}async function bb(a,b){try{var c=await ab(a);return await WebAssembly.instantiate(c,b)}catch(d){A(`failed to asynchronously prepare wasm: ${d}`),F(d)}}
async function cb(a){var b=$a;if("function"==typeof WebAssembly.instantiateStreaming&&!Za(b)&&"function"==typeof fetch)try{var c=fetch(b,{credentials:"same-origin"});return await WebAssembly.instantiateStreaming(c,a)}catch(d){A(`wasm streaming compile failed: ${d}`),A("falling back to ArrayBuffer instantiation")}return bb(b,a)}
function db(){eb={C:fb,f:gb,x:hb,y:ib,m:jb,k:kb,B:lb,i:mb,q:nb,g:ob,j:Ia,h:pb,r:qb,s:rb,o:sb,d:tb,l:ub,b:vb,A:wb,v:xb,t:yb,u:zb,c:Ab,e:Bb,w:Cb,n:Db,z:Eb,a:k,p:Fb};return{a:eb}}var G,H;class Gb{name="ExitStatus";constructor(a){this.message=`Program terminated with exit(${a})`;this.status=a}}
var Hb=a=>{a.terminate();a.onmessage=()=>{}},Kb=a=>{0==I.length&&(Ib(),Jb(I[0]));var b=I.pop();if(!b)return 6;J.push(b);K[a.ka]=b;b.ka=a.ka;b.postMessage({ha:"run",gb:a.fb,Ca:a.Ca,ka:a.ka},a.Ta);return 0},M=0,xb=()=>0<M,N=(a,b,...c)=>{for(var d=c.length,e=Lb(),f=Mb(8*d),g=f>>3,m=0;m<c.length;m++){var r=c[m];ja()[g+m]=r}a=Nb(a,0,d,f,b);Ob(e);return a};function Fb(a){if(w)return N(0,1,a);B=a;0<M||(Pb(),Ba=!0);throw new Gb(a);}var Qb=a=>{if(!(a instanceof Gb||"unwind"==a))throw a;};
function Rb(a){if(w)return N(1,0,a);--M;Ab(a)}var Ab=a=>{B=a;if(w)throw Rb(a),"unwind";if(!(0<M||w)){Sb();C(Pa);Ta=!1;Tb(0);for(var b=0;b<P.length;b++){var c=P[b];c&&Ub(c)}Pb();Ra=!0}Fb(a)},I=[],J=[],Vb=[],K={};function Wb(){for(var a=navigator.hardwareConcurrency;a--;)Ib();Ma.unshift(()=>{D++;Xb(()=>Ya())})}var Pb=()=>{for(var a of J)Hb(a);for(a of I)Hb(a);I=[];J=[];K={}},Zb=a=>{var b=a.ka;delete K[b];I.push(a);J.splice(J.indexOf(a),1);a.ka=0;Yb(b)};function Ha(){Vb.forEach(a=>a())}
var Jb=a=>new Promise(b=>{a.onmessage=f=>{f=f.data;var g=f.ha;if(f.Ba&&f.Ba!=Ea()){var m=K[f.Ba];m?m.postMessage(f,f.Ta):A(`Internal error! Worker sent a message "${g}" to target pthread ${f.Ba}, but that thread no longer exists!`)}else if("checkMailbox"===g)Ka();else if("spawnThread"===g)Kb(f);else if("cleanupThread"===g)Zb(K[f.hb]);else if("loaded"===g)a.loaded=!0,b(a);else if("alert"===g)alert(`Thread ${f.ib}: ${f.text}`);else if("setimmediate"===f.target)a.postMessage(f);else if("callHandler"===
g)u[f.Xa](...f.Va);else g&&A(`worker sent an unknown command ${g}`)};a.onerror=f=>{A(`${"worker sent an error!"} ${f.filename}:${f.lineno}: ${f.message}`);throw f;};var c=[],d=["print","printErr"],e;for(e of d)u.propertyIsEnumerable(e)&&c.push(e);a.postMessage({ha:"load",Ya:c,lb:k,mb:Aa})});function Xb(a){w?a():Promise.all(I.map(Jb)).then(a)}function Ib(){var a=new Worker(new URL("sf16-7.js",import.meta.url),{type:"module",name:"em-pthread"});I.push(a)}
var C=a=>{for(;0<a.length;)a.shift()(u)},Fa=a=>{p();var b=t()[a+52>>2];a=t()[a+56>>2];$b(b,b-a);Ob(b)},ac=[],bc,Ja=(a,b)=>{M=0;var c=ac[a];c||(a>=ac.length&&(ac.length=a+1),ac[a]=c=bc.get(a));a=c(b);0<M?B=a:cc(a)};function dc(a,b,c,d){return w?N(2,1,a,b,c,d):fb(a,b,c,d)}
var fb=(a,b,c,d)=>{if("undefined"==typeof SharedArrayBuffer)return 6;var e=[];if(w&&0===e.length)return dc(a,b,c,d);a={fb:c,ka:a,Ca:d,Ta:e};return w?(a.ha="spawnThread",postMessage(a,e),0):Kb(a)},S=()=>{var a=q()[+ec>>2];ec+=4;return a},fc=(a,b)=>{for(var c=0,d=a.length-1;0<=d;d--){var e=a[d];"."===e?a.splice(d,1):".."===e?(a.splice(d,1),c++):c&&(a.splice(d,1),c--)}if(b)for(;c;c--)a.unshift("..");return a},gc=a=>{var b="/"===a.charAt(0),c="/"===a.substr(-1);(a=fc(a.split("/").filter(d=>!!d),!b).join("/"))||
b||(a=".");a&&c&&(a+="/");return(b?"/":"")+a},hc=a=>{var b=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(a).slice(1);a=b[0];b=b[1];if(!a&&!b)return".";b&&=b.substr(0,b.length-1);return a+b},ic=a=>{if("/"===a)return"/";a=gc(a);a=a.replace(/\/$/,"");var b=a.lastIndexOf("/");return-1===b?a:a.substr(b+1)},jc=()=>{if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return a=>(a.set(crypto.getRandomValues(new Uint8Array(a.byteLength))),a);F("initRandomDevice")},
kc=a=>(kc=jc())(a),lc=(...a)=>{for(var b="",c=!1,d=a.length-1;-1<=d&&!c;d--){c=0<=d?a[d]:"/";if("string"!=typeof c)throw new TypeError("Arguments to path.resolve must be strings");if(!c)return"";b=c+"/"+b;c="/"===c.charAt(0)}b=fc(b.split("/").filter(e=>!!e),!c).join("/");return(c?"/":"")+b||"."},mc="undefined"!=typeof TextDecoder?new TextDecoder:void 0,nc=(a,b=0,c=NaN)=>{var d=b+c;for(c=b;a[c]&&!(c>=d);)++c;if(16<c-b&&a.buffer&&mc)return mc.decode(a.buffer instanceof ArrayBuffer?a.subarray(b,c):a.slice(b,
c));for(d="";b<c;){var e=a[b++];if(e&128){var f=a[b++]&63;if(192==(e&224))d+=String.fromCharCode((e&31)<<6|f);else{var g=a[b++]&63;e=224==(e&240)?(e&15)<<12|f<<6|g:(e&7)<<18|f<<12|g<<6|a[b++]&63;65536>e?d+=String.fromCharCode(e):(e-=65536,d+=String.fromCharCode(55296|e>>10,56320|e&1023))}}else d+=String.fromCharCode(e)}return d},oc=[],ta=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);127>=d?b++:2047>=d?b+=2:55296<=d&&57343>=d?(b+=4,++c):b+=3}return b},pc=(a,b,c,d)=>{if(!(0<d))return 0;
var e=c;d=c+d-1;for(var f=0;f<a.length;++f){var g=a.charCodeAt(f);if(55296<=g&&57343>=g){var m=a.charCodeAt(++f);g=65536+((g&1023)<<10)|m&1023}if(127>=g){if(c>=d)break;b[c++]=g}else{if(2047>=g){if(c+1>=d)break;b[c++]=192|g>>6}else{if(65535>=g){if(c+2>=d)break;b[c++]=224|g>>12}else{if(c+3>=d)break;b[c++]=240|g>>18;b[c++]=128|g>>12&63}b[c++]=128|g>>6&63}b[c++]=128|g&63}}b[c]=0;return c-e},qc=[];function sc(a,b){qc[a]={input:[],ea:[],na:b};tc(a,uc)}
var uc={open(a){var b=qc[a.node.za];if(!b)throw new T(43);a.ca=b;a.seekable=!1},close(a){a.ca.na.wa(a.ca)},wa(a){a.ca.na.wa(a.ca)},read(a,b,c,d){if(!a.ca||!a.ca.na.Ma)throw new T(60);for(var e=0,f=0;f<d;f++){try{var g=a.ca.na.Ma(a.ca)}catch(m){throw new T(29);}if(void 0===g&&0===e)throw new T(6);if(null===g||void 0===g)break;e++;b[c+f]=g}e&&(a.node.ra=Date.now());return e},write(a,b,c,d){if(!a.ca||!a.ca.na.Fa)throw new T(60);try{for(var e=0;e<d;e++)a.ca.na.Fa(a.ca,b[c+e])}catch(f){throw new T(29);
}d&&(a.node.ga=a.node.fa=Date.now());return e}},vc={Ma(){a:{if(!oc.length){var a=null;"undefined"!=typeof window&&"function"==typeof window.prompt&&(a=window.prompt("Input: "),null!==a&&(a+="\n"));if(!a){var b=null;break a}b=Array(ta(a)+1);a=pc(a,b,0,b.length);b.length=a;oc=b}b=oc.shift()}return b},Fa(a,b){null===b||10===b?(za(nc(a.ea)),a.ea=[]):0!=b&&a.ea.push(b)},wa(a){a.ea&&0<a.ea.length&&(za(nc(a.ea)),a.ea=[])},$a(){return{rb:25856,tb:5,qb:191,sb:35387,pb:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,
23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ab(){return 0},bb(){return[24,80]}},wc={Fa(a,b){null===b||10===b?(A(nc(a.ea)),a.ea=[]):0!=b&&a.ea.push(b)},wa(a){a.ea&&0<a.ea.length&&(A(nc(a.ea)),a.ea=[])}};function xc(a,b){var c=a.ba?a.ba.length:0;c>=b||(b=Math.max(b,c*(1048576>c?2:1.125)>>>0),0!=c&&(b=Math.max(b,256)),c=a.ba,a.ba=new Uint8Array(b),0<a.da&&a.ba.set(c.subarray(0,a.da),0))}
var U={ja:null,ma(){return U.createNode(null,"/",16895,0)},createNode(a,b,c,d){if(24576===(c&61440)||4096===(c&61440))throw new T(63);U.ja||(U.ja={dir:{node:{qa:U.aa.qa,la:U.aa.la,ta:U.aa.ta,xa:U.aa.xa,Ra:U.aa.Ra,Ua:U.aa.Ua,Sa:U.aa.Sa,Ga:U.aa.Ga,Aa:U.aa.Aa},stream:{ia:U.$.ia}},file:{node:{qa:U.aa.qa,la:U.aa.la},stream:{ia:U.$.ia,read:U.$.read,write:U.$.write,Ia:U.$.Ia,Oa:U.$.Oa,Qa:U.$.Qa}},link:{node:{qa:U.aa.qa,la:U.aa.la,ua:U.aa.ua},stream:{}},Ja:{node:{qa:U.aa.qa,la:U.aa.la},stream:yc}});c=zc(a,
b,c,d);16384===(c.mode&61440)?(c.aa=U.ja.dir.node,c.$=U.ja.dir.stream,c.ba={}):32768===(c.mode&61440)?(c.aa=U.ja.file.node,c.$=U.ja.file.stream,c.da=0,c.ba=null):40960===(c.mode&61440)?(c.aa=U.ja.link.node,c.$=U.ja.link.stream):8192===(c.mode&61440)&&(c.aa=U.ja.Ja.node,c.$=U.ja.Ja.stream);c.ra=c.ga=c.fa=Date.now();a&&(a.ba[b]=c,a.ra=a.ga=a.fa=c.ra);return c},wb(a){return a.ba?a.ba.subarray?a.ba.subarray(0,a.da):new Uint8Array(a.ba):new Uint8Array(0)},aa:{qa(a){var b={};b.ub=8192===(a.mode&61440)?
a.id:1;b.yb=a.id;b.mode=a.mode;b.zb=1;b.uid=0;b.xb=0;b.za=a.za;16384===(a.mode&61440)?b.size=4096:32768===(a.mode&61440)?b.size=a.da:40960===(a.mode&61440)?b.size=a.link.length:b.size=0;b.ra=new Date(a.ra);b.ga=new Date(a.ga);b.fa=new Date(a.fa);b.Wa=4096;b.ob=Math.ceil(b.size/b.Wa);return b},la(a,b){for(var c of["mode","atime","mtime","ctime"])b[c]&&(a[c]=b[c]);void 0!==b.size&&(b=b.size,a.da!=b&&(0==b?(a.ba=null,a.da=0):(c=a.ba,a.ba=new Uint8Array(b),c&&a.ba.set(c.subarray(0,Math.min(b,a.da))),
a.da=b)))},ta(){throw U.Ka;},xa(a,b,c,d){return U.createNode(a,b,c,d)},Ra(a,b,c){try{var d=Ac(b,c)}catch(f){}if(d){if(16384===(a.mode&61440))for(var e in d.ba)throw new T(55);e=Bc(d.parent.id,d.name);if(V[e]===d)V[e]=d.sa;else for(e=V[e];e;){if(e.sa===d){e.sa=d.sa;break}e=e.sa}}delete a.parent.ba[a.name];b.ba[c]=a;a.name=c;b.fa=b.ga=a.parent.fa=a.parent.ga=Date.now()},Ua(a,b){delete a.ba[b];a.fa=a.ga=Date.now()},Sa(a,b){var c=Ac(a,b),d;for(d in c.ba)throw new T(55);delete a.ba[b];a.fa=a.ga=Date.now()},
Ga(a){return[".","..",...Object.keys(a.ba)]},Aa(a,b,c){a=U.createNode(a,b,41471,0);a.link=c;return a},ua(a){if(40960!==(a.mode&61440))throw new T(28);return a.link}},$:{read(a,b,c,d,e){var f=a.node.ba;if(e>=a.node.da)return 0;a=Math.min(a.node.da-e,d);if(8<a&&f.subarray)b.set(f.subarray(e,e+a),c);else for(d=0;d<a;d++)b[c+d]=f[e+d];return a},write(a,b,c,d,e,f){b.buffer===h().buffer&&(f=!1);if(!d)return 0;a=a.node;a.ga=a.fa=Date.now();if(b.subarray&&(!a.ba||a.ba.subarray)){if(f)return a.ba=b.subarray(c,
c+d),a.da=d;if(0===a.da&&0===e)return a.ba=b.slice(c,c+d),a.da=d;if(e+d<=a.da)return a.ba.set(b.subarray(c,c+d),e),d}xc(a,e+d);if(a.ba.subarray&&b.subarray)a.ba.set(b.subarray(c,c+d),e);else for(f=0;f<d;f++)a.ba[e+f]=b[c+f];a.da=Math.max(a.da,e+d);return d},ia(a,b,c){1===c?b+=a.position:2===c&&32768===(a.node.mode&61440)&&(b+=a.node.da);if(0>b)throw new T(28);return b},Ia(a,b,c){xc(a.node,b+c);a.node.da=Math.max(a.node.da,b+c)},Oa(a,b,c,d,e){if(32768!==(a.node.mode&61440))throw new T(43);a=a.node.ba;
if(e&2||!a||a.buffer!==h().buffer){d=!0;F();e=void 0;if(!e)throw new T(48);if(a){if(0<c||c+b<a.length)a.subarray?a=a.subarray(c,c+b):a=Array.prototype.slice.call(a,c,c+b);h().set(a,e)}}else d=!1,e=a.byteOffset;return{Bb:e,nb:d}},Qa(a,b,c,d){U.$.write(a,b,0,d,c,!1);return 0}}},Cc=a=>{var b=0;a&&(b|=365);return b},Dc=null,Ec={},P=[],Fc=1,V=null,Ta=!1,Xa=!0,T=class{name="ErrnoError";constructor(a){this.oa=a}},Gc=class{va={};node=null;get flags(){return this.va.flags}set flags(a){this.va.flags=a}get position(){return this.va.position}set position(a){this.va.position=
a}},Hc=class{aa={};$={};ya=null;constructor(a,b,c,d){a||=this;this.parent=a;this.ma=a.ma;this.id=Fc++;this.name=b;this.mode=c;this.za=d;this.ra=this.ga=this.fa=Date.now()}get read(){return 365===(this.mode&365)}set read(a){a?this.mode|=365:this.mode&=-366}get write(){return 146===(this.mode&146)}set write(a){a?this.mode|=146:this.mode&=-147}};
function Ic(a,b={}){if(!a)return{path:"",node:null};b.Da??(b.Da=!0);"/"===a.charAt(0)||(a="//"+a);var c=0;a:for(;40>c;c++){a=a.split("/").filter(m=>!!m&&"."!==m);for(var d=Dc,e="/",f=0;f<a.length;f++){var g=f===a.length-1;if(g&&b.parent)break;if(".."===a[f])e=hc(e),d=d.parent;else{e=gc(e+"/"+a[f]);try{d=Ac(d,a[f])}catch(m){if(44===m?.oa&&g&&b.eb)return{path:e};throw m;}!d.ya||g&&!b.Da||(d=d.ya.root);if(40960===(d.mode&61440)&&(!g||b.La)){if(!d.aa.ua)throw new T(52);d=d.aa.ua(d);"/"===d.charAt(0)||
(d=hc(e)+"/"+d);a=d+"/"+a.slice(f+1).join("/");continue a}}}return{path:e,node:d}}throw new T(32);}function Bc(a,b){for(var c=0,d=0;d<b.length;d++)c=(c<<5)-c+b.charCodeAt(d)|0;return(a+c>>>0)%V.length}function Ac(a,b){var c=16384===(a.mode&61440)?(c=Jc(a,"x"))?c:a.aa.ta?0:2:54;if(c)throw new T(c);for(c=V[Bc(a.id,b)];c;c=c.sa){var d=c.name;if(c.parent.id===a.id&&d===b)return c}return a.aa.ta(a,b)}function zc(a,b,c,d){a=new Hc(a,b,c,d);b=Bc(a.parent.id,a.name);a.sa=V[b];return V[b]=a}
function Kc(a){var b=["r","w","rw"][a&3];a&512&&(b+="w");return b}function Jc(a,b){if(Xa)return 0;if(!b.includes("r")||a.mode&292){if(b.includes("w")&&!(a.mode&146)||b.includes("x")&&!(a.mode&73))return 2}else return 2;return 0}function Lc(a,b){if(16384!==(a.mode&61440))return 54;try{return Ac(a,b),20}catch(c){}return Jc(a,"wx")}function W(a){a=P[a];if(!a)throw new T(8);return a}
function Mc(a,b=-1){a=Object.assign(new Gc,a);if(-1==b)a:{for(b=0;4096>=b;b++)if(!P[b])break a;throw new T(33);}a.pa=b;return P[b]=a}function Nc(a,b=-1){a=Mc(a,b);a.$?.vb?.(a);return a}var yc={open(a){a.$=Ec[a.node.za].$;a.$.open?.(a)},ia(){throw new T(70);}};function tc(a,b){Ec[a]={$:b}}
function Oc(a,b){var c="/"===b;if(c&&Dc)throw new T(10);if(!c&&b){var d=Ic(b,{Da:!1});b=d.path;d=d.node;if(d.ya)throw new T(10);if(16384!==(d.mode&61440))throw new T(54);}b={type:a,Ab:{},Pa:b,cb:[]};a=a.ma(b);a.ma=b;b.root=a;c?Dc=a:d&&(d.ya=b,d.ma&&d.ma.cb.push(b))}function Pc(a,b,c){var d=Ic(a,{parent:!0}).node;a=ic(a);if(!a||"."===a||".."===a)throw new T(28);var e=Lc(d,a);if(e)throw new T(e);if(!d.aa.xa)throw new T(63);return d.aa.xa(d,a,b,c)}function X(a){return Pc(a,16895,0)}
function Qc(a,b,c){"undefined"==typeof c&&(c=b,b=438);Pc(a,b|8192,c)}function Ua(a,b){if(!lc(a))throw new T(44);var c=Ic(b,{parent:!0}).node;if(!c)throw new T(44);b=ic(b);var d=Lc(c,b);if(d)throw new T(d);if(!c.aa.Aa)throw new T(63);c.aa.Aa(c,b,a)}
function Wa(a,b,c=438){if(""===a)throw new T(44);if("string"==typeof b){var d={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[b];if("undefined"==typeof d)throw Error(`Unknown file open mode: ${b}`);b=d}c=b&64?c&4095|32768:0;"object"==typeof a?d=a:(a=Ic(a,{La:!(b&131072),eb:!0}),d=a.node,a=a.path);var e=!1;if(b&64)if(d){if(b&128)throw new T(20);}else d=Pc(a,c,0),e=!0;if(!d)throw new T(44);8192===(d.mode&61440)&&(b&=-513);if(b&65536&&16384!==(d.mode&61440))throw new T(54);if(!e&&(c=d?40960===(d.mode&61440)?
32:16384===(d.mode&61440)&&("r"!==Kc(b)||b&512)?31:Jc(d,Kc(b)):44))throw new T(c);if(b&512&&!e){c=d;c="string"==typeof c?Ic(c,{La:!0}).node:c;if(!c.aa.la)throw new T(63);if(16384===(c.mode&61440))throw new T(31);if(32768!==(c.mode&61440))throw new T(28);if(a=Jc(c,"w"))throw new T(a);c.aa.la(c,{size:0,timestamp:Date.now()})}a:for(c=d;;){if(c===c.parent){c=c.ma.Pa;var f=f?"/"!==c[c.length-1]?`${c}/${f}`:c+f:c;break a}f=f?`${c.name}/${f}`:c.name;c=c.parent}b=Mc({node:d,path:f,flags:b&-131713,seekable:!0,
position:0,$:d.$,jb:[],error:!1});b.$.open&&b.$.open(b);return b}function Ub(a){if(null===a.pa)throw new T(8);a.Ea&&(a.Ea=null);try{a.$.close&&a.$.close(a)}catch(b){throw b;}finally{P[a.pa]=null}a.pa=null}function Rc(a,b,c){if(null===a.pa)throw new T(8);if(!a.seekable||!a.$.ia)throw new T(70);if(0!=c&&1!=c&&2!=c)throw new T(28);a.position=a.$.ia(a,b,c);a.jb=[]}
function Sc(a,b){a=gc("/dev/"+a);var c=Cc(!!b);Sc.Na??(Sc.Na=64);var d=Sc.Na++<<8|0;tc(d,{open(e){e.seekable=!1},close(){(void 0)?.buffer?.length&&(void 0)(10)},read(e,f,g,m){for(var r=0,n=0;n<m;n++){try{var z=b()}catch(O){throw new T(29);}if(void 0===z&&0===r)throw new T(6);if(null===z||void 0===z)break;r++;f[g+n]=z}r&&(e.node.ra=Date.now());return r},write(e,f,g,m){for(var r=0;r<m;r++)try{(void 0)(f[g+r])}catch(n){throw new T(29);}m&&(e.node.ga=e.node.fa=Date.now());return r}});Qc(a,c,d)}
var Y={},pa=(a,b)=>a?nc(aa(),a,b):"",ec=void 0;function gb(a,b,c){if(w)return N(3,1,a,b,c);ec=c;try{var d=W(a);switch(b){case 0:var e=S();if(0>e)break;for(;P[e];)e++;return Nc(d,e).pa;case 1:case 2:return 0;case 3:return d.flags;case 4:return e=S(),d.flags|=e,0;case 12:return e=S(),ca()[e+0>>1]=2,0;case 13:case 14:return 0}return-28}catch(f){if("undefined"==typeof Y||"ErrnoError"!==f.name)throw f;return-f.oa}}
function hb(a,b,c){if(w)return N(4,1,a,b,c);ec=c;try{var d=W(a);switch(b){case 21509:return d.ca?0:-59;case 21505:if(!d.ca)return-59;if(d.ca.na.$a){a=[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];var e=S();q()[e>>2]=25856;q()[e+4>>2]=5;q()[e+8>>2]=191;q()[e+12>>2]=35387;for(var f=0;32>f;f++)h()[e+f+17]=a[f]||0}return 0;case 21510:case 21511:case 21512:return d.ca?0:-59;case 21506:case 21507:case 21508:if(!d.ca)return-59;if(d.ca.na.ab)for(e=S(),q(),q(),q(),q(),a=[],f=
0;32>f;f++)a.push(h()[e+f+17]);return 0;case 21519:if(!d.ca)return-59;e=S();return q()[e>>2]=0;case 21520:return d.ca?-28:-59;case 21531:e=S();if(!d.$.Za)throw new T(59);return d.$.Za(d,b,e);case 21523:if(!d.ca)return-59;d.ca.na.bb&&(f=[24,80],e=S(),ca()[e>>1]=f[0],ca()[e+2>>1]=f[1]);return 0;case 21524:return d.ca?0:-59;case 21515:return d.ca?0:-59;default:return-28}}catch(g){if("undefined"==typeof Y||"ErrnoError"!==g.name)throw g;return-g.oa}}
function ib(a,b,c,d){if(w)return N(5,1,a,b,c,d);ec=d;try{b=pa(b);var e=b;if("/"===e.charAt(0))b=e;else{var f=-100===a?"/":W(a).path;if(0==e.length)throw new T(44);b=f+"/"+e}var g=d?S():0;return Wa(b,c,g).pa}catch(m){if("undefined"==typeof Y||"ErrnoError"!==m.name)throw m;return-m.oa}}
var jb=()=>F(""),kb=a=>{Ga(a,!v,1,!oa,2097152,!1);Ha()},Tc=a=>{if(!Ra&&!Ba)try{if(a(),!(Ra||0<M))try{w?cc(B):Ab(B)}catch(b){Qb(b)}}catch(b){Qb(b)}},Ia=a=>{"function"===typeof Atomics.kb&&(Atomics.kb(q(),a>>2,a).value.then(Ka),a+=128,Atomics.store(q(),a>>2,1))},Ka=()=>{var a=Ea();a&&(Ia(a),Tc(Uc))},lb=(a,b)=>{a==b?setTimeout(Ka):w?postMessage({Ba:a,ha:"checkMailbox"}):(a=K[a])&&a.postMessage({ha:"checkMailbox"})},Vc=[],mb=(a,b,c,d,e)=>{Vc.length=d;b=e>>3;for(c=0;c<d;c++)Vc[c]=ja()[b+c];return(0,Wc[a])(...Vc)},
nb=()=>{M=0},ob=a=>{w?postMessage({ha:"cleanupThread",hb:a}):Zb(K[a])},pb=()=>{},Xc={},vb=()=>performance.timeOrigin+performance.now();function qb(a,b){if(w)return N(6,1,a,b);Xc[a]&&(clearTimeout(Xc[a].id),delete Xc[a]);if(!b)return 0;var c=setTimeout(()=>{delete Xc[a];Tc(()=>Yc(a,performance.timeOrigin+performance.now()))},b);Xc[a]={id:c,Cb:b};return 0}
var x=(a,b,c)=>pc(a,aa(),b,c),rb=(a,b,c,d)=>{var e=(new Date).getFullYear(),f=(new Date(e,0,1)).getTimezoneOffset();e=(new Date(e,6,1)).getTimezoneOffset();var g=Math.max(f,e);t()[a>>2]=60*g;q()[b>>2]=Number(f!=e);b=m=>{var r=Math.abs(m);return`UTC${0<=m?"-":"+"}${String(Math.floor(r/60)).padStart(2,"0")}${String(r%60).padStart(2,"0")}`};a=b(f);b=b(e);e<f?(x(a,c,17),x(b,d,17)):(x(a,d,17),x(b,c,17))},Zc=1;
function sb(a,b,c,d){if(!(0<=a&&3>=a))return 28;if(0===a)a=Date.now();else if(Zc)a=performance.timeOrigin+performance.now();else return 52;a=Math.round(1E6*a);H=[a>>>0,(G=a,1<=+Math.abs(G)?0<G?+Math.floor(G/4294967296)>>>0:~~+Math.ceil((G-+(~~G>>>0))/4294967296)>>>0:0)];q()[d>>2]=H[0];q()[d+4>>2]=H[1];return 0}
var $c=()=>{$c.Ha||($c.Ha={});$c.Ha["Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread"]||($c.Ha["Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread"]=1,A("Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread"))},tb=()=>{v||($c(),F("Blocking on the main thread is not allowed by default. See https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread"))},
ub=()=>{M+=1;throw"unwind";},wb=a=>{var b=aa().length;a>>>=0;if(a<=b||2147483648<a)return!1;for(var c=1;4>=c;c*=2){var d=b*(1+.2/c);d=Math.min(d,a+100663296);a:{d=(Math.min(2147483648,65536*Math.ceil(Math.max(a,d)/65536))-k.buffer.byteLength+65535)/65536|0;try{k.grow(d);p();var e=1;break a}catch(f){}e=void 0}if(e)return!0}return!1},ad={},cd=()=>{if(!bd){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||
"C").replace("-","_")+".UTF-8",_:"./this.program"},b;for(b in ad)void 0===ad[b]?delete a[b]:a[b]=ad[b];var c=[];for(b in a)c.push(`${b}=${a[b]}`);bd=c}return bd},bd;function yb(a,b){if(w)return N(7,1,a,b);var c=0;cd().forEach((d,e)=>{var f=b+c;e=t()[a+4*e>>2]=f;for(f=0;f<d.length;++f)h()[e++]=d.charCodeAt(f);h()[e]=0;c+=d.length+1});return 0}function zb(a,b){if(w)return N(8,1,a,b);var c=cd();t()[a>>2]=c.length;var d=0;c.forEach(e=>d+=e.length+1);t()[b>>2]=d;return 0}
function Bb(a){if(w)return N(9,1,a);try{var b=W(a);Ub(b);return 0}catch(c){if("undefined"==typeof Y||"ErrnoError"!==c.name)throw c;return c.oa}}
function Cb(a,b,c,d){if(w)return N(10,1,a,b,c,d);try{a:{var e=W(a);a=b;for(var f,g=b=0;g<c;g++){var m=t()[a>>2],r=t()[a+4>>2];a+=8;var n=e,z=h(),O=f;if(0>r||0>O)throw new T(28);if(null===n.pa)throw new T(8);if(1===(n.flags&2097155))throw new T(8);if(16384===(n.node.mode&61440))throw new T(31);if(!n.$.read)throw new T(28);var ha="undefined"!=typeof O;if(!ha)O=n.position;else if(!n.seekable)throw new T(70);var Q=n.$.read(n,z,m,r,O);ha||(n.position+=Q);var R=Q;if(0>R){var ia=-1;break a}b+=R;if(R<r)break;
"undefined"!=typeof f&&(f+=R)}ia=b}t()[d>>2]=ia;return 0}catch(L){if("undefined"==typeof Y||"ErrnoError"!==L.name)throw L;return L.oa}}
function Db(a,b,c,d,e){if(w)return N(11,1,a,b,c,d,e);b=c+2097152>>>0<4194305-!!b?(b>>>0)+4294967296*c:NaN;try{if(isNaN(b))return 61;var f=W(a);Rc(f,b,d);H=[f.position>>>0,(G=f.position,1<=+Math.abs(G)?0<G?+Math.floor(G/4294967296)>>>0:~~+Math.ceil((G-+(~~G>>>0))/4294967296)>>>0:0)];q()[e>>2]=H[0];q()[e+4>>2]=H[1];f.Ea&&0===b&&0===d&&(f.Ea=null);return 0}catch(g){if("undefined"==typeof Y||"ErrnoError"!==g.name)throw g;return g.oa}}
function Eb(a,b,c,d){if(w)return N(12,1,a,b,c,d);try{a:{var e=W(a);a=b;for(var f,g=b=0;g<c;g++){var m=t()[a>>2],r=t()[a+4>>2];a+=8;var n=e,z=h(),O=m,ha=r,Q=f;if(0>ha||0>Q)throw new T(28);if(null===n.pa)throw new T(8);if(0===(n.flags&2097155))throw new T(8);if(16384===(n.node.mode&61440))throw new T(31);if(!n.$.write)throw new T(28);n.seekable&&n.flags&1024&&Rc(n,0,2);var R="undefined"!=typeof Q;if(!R)Q=n.position;else if(!n.seekable)throw new T(70);var ia=n.$.write(n,z,O,ha,Q,void 0);R||(n.position+=
ia);var L=ia;if(0>L){var rc=-1;break a}b+=L;if(L<r)break;"undefined"!=typeof f&&(f+=L)}rc=b}t()[d>>2]=rc;return 0}catch(Va){if("undefined"==typeof Y||"ErrnoError"!==Va.name)throw Va;return Va.oa}}w||Wb();V=Array(4096);Oc(U,"/");X("/tmp");X("/home");X("/home/<USER>");
(function(){X("/dev");tc(259,{read:()=>0,write:(d,e,f,g)=>g,ia:()=>0});Qc("/dev/null",259);sc(1280,vc);sc(1536,wc);Qc("/dev/tty",1280);Qc("/dev/tty1",1536);var a=new Uint8Array(1024),b=0,c=()=>{0===b&&(b=kc(a).byteLength);return a[--b]};Sc("random",c);Sc("urandom",c);X("/dev/shm");X("/dev/shm/tmp")})();
(function(){X("/proc");var a=X("/proc/self");X("/proc/self/fd");Oc({ma(){var b=zc(a,"fd",16895,73);b.$={ia:U.$.ia};b.aa={ta(c,d){c=+d;var e=W(c);c={parent:null,ma:{Pa:"fake"},aa:{ua:()=>e.path},id:c+1};return c.parent=c},Ga(){return Array.from(P.entries()).filter(([,c])=>c).map(([c])=>c.toString())}};return b}},"/proc/self/fd")})();U.Ka=new T(44);U.Ka.stack="<generic error, no stack>";var Wc=[Fb,Rb,dc,gb,hb,ib,qb,yb,zb,Bb,Cb,Db,Eb],eb,Z;
(async function(){function a(d,e){Z=d.exports;Vb.push(Z.J);bc=Z.M;Na.unshift(Z.D);Aa=e;Ya();return Z}D++;var b=db();if(u.instantiateWasm)try{return u.instantiateWasm(b,a)}catch(d){A(`Module.instantiateWasm callback failed with error: ${d}`),ma(d)}if(w)return new Promise(d=>{Ca=e=>{var f=new WebAssembly.Instance(e,db());a(f,e);d()}});$a??=u.locateFile?Za("sf16-7.wasm")?"sf16-7.wasm":u.locateFile?u.locateFile("sf16-7.wasm",y):y+"sf16-7.wasm":(new URL("sf16-7.wasm",import.meta.url)).href;
try{var c=await cb(b);a(c.instance,c.module);return c}catch(d){ma(d)}})();u._main=(a,b)=>(u._main=Z.E)(a,b);u.__Z10js_getlinev=a=>(u.__Z10js_getlinev=Z.F)(a);
var ua=u._uci=a=>(ua=u._uci=Z.G)(a),sa=u._setNnueBuffer=(a,b,c)=>(sa=u._setNnueBuffer=Z.H)(a,b,c),qa=u._getRecommendedNnue=a=>(qa=u._getRecommendedNnue=Z.I)(a),Ea=()=>(Ea=Z.K)(),dd=u.__emscripten_proxy_main=(a,b)=>(dd=u.__emscripten_proxy_main=Z.L)(a,b),Sb=()=>(Sb=Z.N)(),Ga=(a,b,c,d,e,f)=>(Ga=Z.O)(a,b,c,d,e,f),La=()=>(La=Z.P)(),Tb=a=>(Tb=Z.Q)(a),ra=u._malloc=a=>(ra=u._malloc=Z.R)(a),Nb=(a,b,c,d,e)=>(Nb=Z.S)(a,b,c,d,e),Yb=a=>(Yb=Z.T)(a),cc=a=>(cc=Z.U)(a),Yc=(a,b)=>(Yc=Z.V)(a,b),Uc=()=>(Uc=Z.W)(),$b=
(a,b)=>($b=Z.X)(a,b),Ob=a=>(Ob=Z.Y)(a),Mb=a=>(Mb=Z.Z)(a),Lb=()=>(Lb=Z._)();u.UTF8ToString=pa;u.stringToUTF8=x;var ed;E=function fd(){ed||gd();ed||(E=fd)};function hd(a=[]){var b=dd;M+=1;a.unshift("./this.program");var c=a.length,d=Mb(4*(c+1)),e=d;a.forEach(g=>{var m=t(),r=e>>2,n=ta(g)+1,z=Mb(n);x(g,z,n);m[r]=z;e+=4});t()[e>>2]=0;try{var f=b(c,d);Ab(f,!0)}catch(g){Qb(g)}}function gd(){0<D||(w?(la(u),Sa()):(C(Ma),0<D||ed||(ed=!0,u.calledRun=!0,Ba||(Sa(),w||C(Oa),la(u),jd&&hd(wa),w||C(Qa)))))}
var jd=!0;gd();moduleRtn=na;


  return moduleRtn;
}
);
})();
export default Sf167Web;
var isPthread = globalThis.self?.name?.startsWith('em-pthread');
// When running as a pthread, construct a new instance on startup
isPthread && Sf167Web();
