// GraphQL query strings (no longer using gql from Apollo Client)

export const GET_PUZZLE_STATS = `
  query GetPuzzleStats {
    myPuzzleStats {
      tag_counts {
        tag
        count
      }
      theme_counts {
        theme
        count
      }
      total_count
    }
  }
`;

export const GET_PUZZLES = `
  query GetPuzzles($filter: PuzzleFilter, $pagination: OffsetPaginationInput) {
    myPuzzles(filter: $filter, pagination: $pagination) {
      edges {
        node {
          id
          theme
          fen
          moves
          prev_cp
          cp
          tags
          user_color
          puzzle_color
        }
        cursor
      }
      page_info {
        has_next_page
        has_previous_page
      }
      total_count
    }
  }
`;

export const GET_DETAILED_PUZZLE_STATS = `
  query GetDetailedPuzzleStats($filter: PuzzleStatsFilterInput) {
    myPuzzleStats(filter: $filter) {
      tag_counts {
        tag
        count
      }
      theme_counts {
        theme
        count
      }
      user_color_counts {
        color
        count
      }
      game_move_buckets {
        name
        min_move
        max_move
        count
      }
      move_length_counts {
        length
        count
      }
      total_count
    }
    myPuzzles(
      filter: $filter
      pagination: {
        offset: 0
        limit: 1000
      }
    ) {
      edges {
        node {
          id
          theme
          tags
          moves
          game_move
          user_color
          created_at
        }
      }
      total_count
    }
  }
`;

export const GET_GAMES = `
  query GetGames($filter: GameFilter, $pagination: OffsetPaginationInput, $sort: SortInput) {
    myGames(filter: $filter, pagination: $pagination, sort: $sort) {
      edges {
        node {
          id
          platform
          game_time
          time_control
          rated
          user_color
          winner
          result
          pgn
          white_player
          black_player
        }
        cursor
      }
      page_info {
        has_next_page
        has_previous_page
      }
      total_count
    }
  }
`;

export const GET_GROUPED_PUZZLE_STATS = `
  query GetGroupedPuzzleStats(
    $filter: PuzzleFilter
    $pagination: OffsetPaginationInput
    $group_unit: TimeGrouping!
    $group_length: Int!
  ) {
    myGroupedPuzzleStats(
      filter: $filter
      pagination: $pagination
      group_unit: $group_unit
      group_length: $group_length
    ) {
      nodes {
        start_time
        end_time
        stats {
          tag_counts { tag count }
          theme_counts { theme count }
          user_color_counts { color count }
          total_count
        }
      }
      total_count
    }
  }
`;