import { useState, useEffect, useCallback, useRef } from 'react';
import { graphqlRequest } from '../services/apiService';

/**
 * Custom hook to replace Apollo Client's useQuery
 * @param {string} query - GraphQL query string
 * @param {Object} options - Query options
 * @returns {Object} Query result with data, loading, error, and refetch
 */
export const useQuery = (query, options = {}) => {
  const { variables = {}, skip = false } = options;
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(!skip);
  const [error, setError] = useState(null);
  const [hasExecuted, setHasExecuted] = useState(false);
  const abortControllerRef = useRef(null);
  const retryTimeoutRef = useRef(null);

  const executeQuery = useCallback(async (isRetry = false) => {
    if (skip) return;

    // If this is an automatic retry due to error, don't execute
    if (isRetry && error) {
      console.log('Skipping automatic retry due to previous error');
      return;
    }

    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Clear any pending retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    abortControllerRef.current = new AbortController();
    setLoading(true);
    if (!isRetry) {
      setError(null);
    }

    try {
      // Convert GraphQL AST to string if needed
      let queryString = query;
      if (typeof query === 'object' && query.loc && query.loc.source) {
        queryString = query.loc.source.body;
      } else if (typeof query === 'object' && query.kind === 'Document') {
        throw new Error('GraphQL query must be a string. Please use the string version of your query instead of the parsed gql object.');
      }

      const result = await graphqlRequest(queryString, variables);
      setData(result);
      setError(null);
      setHasExecuted(true);
    } catch (err) {
      if (err.name !== 'AbortError') {
        console.error('GraphQL query error:', err);
        setError(err);
        setData(null);
        setHasExecuted(true);

        // Don't automatically retry on errors - let user manually retry
        console.log('GraphQL query failed, not retrying automatically');
      }
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [query, variables, skip, error]);

  // Execute query only once when dependencies change, not on every error
  useEffect(() => {
    // Only execute if we haven't executed yet or if skip/query/variables actually changed
    if (!hasExecuted || skip !== undefined) {
      setHasExecuted(false);
      executeQuery();
    }

    // Cleanup function to abort request on unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [query, JSON.stringify(variables), skip]); // Use JSON.stringify to avoid infinite loops

  // Refetch function for manual retries
  const refetch = useCallback(() => {
    setHasExecuted(false);
    setError(null);
    return executeQuery();
  }, [executeQuery]);

  return {
    data,
    loading,
    error,
    refetch
  };
};

/**
 * Custom hook for GraphQL mutations
 * @param {string} mutation - GraphQL mutation string
 * @returns {Array} [mutate function, { data, loading, error }]
 */
export const useMutation = (mutation) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const mutate = useCallback(async (variables = {}) => {
    setLoading(true);
    setError(null);

    try {
      const result = await graphqlRequest(mutation, variables);
      setData(result);
      setError(null);
      return result;
    } catch (err) {
      console.error('GraphQL mutation error:', err);
      setError(err);
      setData(null);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [mutation]);

  return [
    mutate,
    {
      data,
      loading,
      error
    }
  ];
};

/**
 * Hook for lazy queries (queries that are triggered manually)
 * @param {string} query - GraphQL query string
 * @returns {Array} [execute function, { data, loading, error }]
 */
export const useLazyQuery = (query) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const execute = useCallback(async (variables = {}) => {
    setLoading(true);
    setError(null);

    try {
      const result = await graphqlRequest(query, variables);
      setData(result);
      setError(null);
      return result;
    } catch (err) {
      console.error('GraphQL lazy query error:', err);
      setError(err);
      setData(null);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [query]);

  return [
    execute,
    {
      data,
      loading,
      error
    }
  ];
};
