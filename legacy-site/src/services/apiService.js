import {
  getAuthToken,
  getSessionToken,
  updateAuthToken,
  clearTokens,
  needsTokenRefresh
} from '../utils/tokenUtils';
import { API_CONFIG } from '../config/api';

// Track ongoing refresh to prevent multiple simultaneous refresh attempts
let refreshPromise = null;

/**
 * Refresh the auth token using the session token
 * @returns {Promise<boolean>} True if refresh successful, false otherwise
 */
export const refreshAuthToken = async () => {
  // If refresh is already in progress, wait for it
  if (refreshPromise) {
    return refreshPromise;
  }

  const sessionToken = getSessionToken();
  if (!sessionToken) {
    console.warn('No session token available for refresh');
    return false;
  }

  refreshPromise = (async () => {
    try {
      console.log('Attempting token refresh with session token:', sessionToken.substring(0, 10) + '...');

      const response = await fetch(`${API_CONFIG.BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          ...API_CONFIG.DEFAULT_HEADERS,
        },
        body: JSON.stringify({ session_token: sessionToken }),
      });

      if (!response.ok) {
        console.error('Token refresh failed:', response.status);
        return false;
      }

      const data = await response.json();
      if (data.token) {
        // Update auth token with new token (assume 60 minutes expiry)
        updateAuthToken(data.token, 60);
        console.log('Token refreshed successfully');
        return true;
      } else {
        console.error('No token in refresh response');
        return false;
      }
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    } finally {
      refreshPromise = null;
    }
  })();

  return refreshPromise;
};

/**
 * Handle 401 errors with token refresh and retry logic
 * @param {Function} requestFn - Function that makes the request
 * @returns {Promise<any>} Request result or throws AUTHENTICATION_FAILED
 */
export const handleAuthenticatedRequest = async (requestFn) => {
  try {
    // First attempt
    const result = await requestFn();

    // Only retry on 401 errors, not other 4xx errors
    if (result && result.status === 401 && getSessionToken()) {
      console.log('Received 401, attempting token refresh...');

      const refreshSuccess = await refreshAuthToken();
      if (refreshSuccess) {
        console.log('Token refresh successful, retrying request...');
        // Retry the request
        return await requestFn();
      } else {
        // Refresh failed, clear tokens and throw error
        console.log('Token refresh failed, clearing tokens');
        clearTokens();
        throw new Error('AUTHENTICATION_FAILED');
      }
    }

    // For other 4xx errors (400, 403, 404, 429, etc.), don't retry and log the error
    if (result && result.status >= 400 && result.status < 500 && result.status !== 401) {
      console.warn(`Client error ${result.status}, not retrying. This will be shown as an error to the user.`);
    }

    // For 5xx errors, also don't retry
    if (result && result.status >= 500) {
      console.warn(`Server error ${result.status}, not retrying. This will be shown as an error to the user.`);
    }

    return result;
  } catch (error) {
    // For network errors or other exceptions, don't retry unless it's specifically a 401
    console.warn('Request failed with error:', error.message, '- not retrying automatically');

    // Re-throw the error without retry
    throw error;
  }
};

/**
 * Make an authenticated API request with automatic token refresh
 * @param {string} url - API endpoint (relative to base URL)
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} Fetch response
 */
export const apiRequest = async (url, options = {}) => {
  const { skipAuth = false, ...fetchOptions } = options;

  const makeRequest = async () => {
    const fullUrl = url.startsWith('http') ? url : `${API_CONFIG.BASE_URL}${url}`;

    // Prepare headers
    const headers = {
      ...API_CONFIG.DEFAULT_HEADERS,
      ...fetchOptions.headers,
    };

    // Add auth header if not skipping auth
    if (!skipAuth) {
      const authToken = getAuthToken();
      if (authToken) {
        headers.Authorization = `Bearer ${authToken}`;
      }
    }

    // Make the request
    const requestOptions = {
      ...fetchOptions,
      headers,
    };

    return await fetch(fullUrl, requestOptions);
  };

  // Use shared retry logic if auth is enabled
  if (!skipAuth) {
    return await handleAuthenticatedRequest(makeRequest);
  } else {
    return await makeRequest();
  }
};

/**
 * Make a GET request
 * @param {string} url - API endpoint
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Response>} Fetch response
 */
export const apiGet = (url, options = {}) => {
  return apiRequest(url, { method: 'GET', ...options });
};

/**
 * Make a POST request
 * @param {string} url - API endpoint
 * @param {Object} data - Request body data
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Response>} Fetch response
 */
export const apiPost = (url, data = null, options = {}) => {
  const requestOptions = { method: 'POST', ...options };
  if (data) {
    requestOptions.body = JSON.stringify(data);
  }
  return apiRequest(url, requestOptions);
};

/**
 * Make a PUT request
 * @param {string} url - API endpoint
 * @param {Object} data - Request body data
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Response>} Fetch response
 */
export const apiPut = (url, data = null, options = {}) => {
  const requestOptions = { method: 'PUT', ...options };
  if (data) {
    requestOptions.body = JSON.stringify(data);
  }
  return apiRequest(url, requestOptions);
};

/**
 * Make a DELETE request
 * @param {string} url - API endpoint
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Response>} Fetch response
 */
export const apiDelete = (url, options = {}) => {
  return apiRequest(url, { method: 'DELETE', ...options });
};

/**
 * Check if token needs refresh and refresh if necessary
 * @returns {Promise<boolean>} True if token is valid (after refresh if needed)
 */
export const ensureValidToken = async () => {
  if (!getAuthToken() && !getSessionToken()) {
    return false;
  }

  if (needsTokenRefresh()) {
    return await refreshAuthToken();
  }

  return true;
};

/**
 * Make a GraphQL request (alternative to Apollo Client for non-React contexts)
 * @param {string|Object} query - GraphQL query string or parsed query object
 * @param {Object} variables - Query variables
 * @returns {Promise<Object>} GraphQL response data
 */
export const graphqlRequest = async (query, variables = {}) => {
  // Convert GraphQL AST to string if needed
  let queryString = query;
  if (typeof query === 'object' && query.loc && query.loc.source) {
    queryString = query.loc.source.body;
  } else if (typeof query === 'object' && query.kind === 'Document') {
    // If it's a parsed GraphQL document, we need to convert it back to string
    // For now, throw an error to help debug
    throw new Error('GraphQL query must be a string, not a parsed object. Use the string version of your query.');
  }

  const response = await apiPost('/graphql/query', {
    query: queryString,
    variables
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`GraphQL request failed (${response.status}): ${errorText}`);
  }

  const result = await response.json();

  if (result.errors) {
    throw new Error(`GraphQL errors: ${result.errors.map(e => e.message).join(', ')}`);
  }

  return result.data;
};
