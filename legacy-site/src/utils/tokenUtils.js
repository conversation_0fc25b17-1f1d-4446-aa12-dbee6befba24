import Cookies from 'js-cookie';

// Token cookie names
export const AUTH_TOKEN_COOKIE = 'auth_token';
export const SESSION_TOKEN_COOKIE = 'session_token';
export const TOKEN_EXPIRY_COOKIE = 'token_expiry';

// Token refresh buffer (5 minutes in milliseconds)
const TOKEN_REFRESH_BUFFER = 5 * 60 * 1000;

/**
 * Store authentication tokens in cookies
 * @param {string} authToken - JWT token
 * @param {string} sessionToken - Long-term session token
 * @param {number} expiryMinutes - Token expiry in minutes (optional, defaults to 60)
 */
export const storeTokens = (authToken, sessionToken, expiryMinutes = 60) => {
  const expiryTime = new Date(Date.now() + expiryMinutes * 60 * 1000);
  
  // Store auth token with shorter expiry for security
  Cookies.set(AUTH_TOKEN_COOKIE, authToken, { 
    expires: 7, // 7 days max, but we'll refresh before expiry
    secure: true, 
    sameSite: 'strict' 
  });
  
  // Store session token with longer expiry (30 days)
  if (sessionToken) {
    Cookies.set(SESSION_TOKEN_COOKIE, sessionToken, { 
      expires: 30, 
      secure: true, 
      sameSite: 'strict' 
    });
  }
  
  // Store token expiry time for refresh logic
  Cookies.set(TOKEN_EXPIRY_COOKIE, expiryTime.toISOString(), { 
    expires: 7, 
    secure: true, 
    sameSite: 'strict' 
  });
};

/**
 * Get the current auth token from cookies
 * @returns {string|null} Auth token or null if not found
 */
export const getAuthToken = () => {
  return Cookies.get(AUTH_TOKEN_COOKIE) || null;
};

/**
 * Get the current session token from cookies
 * @returns {string|null} Session token or null if not found
 */
export const getSessionToken = () => {
  return Cookies.get(SESSION_TOKEN_COOKIE) || null;
};

/**
 * Check if the current auth token needs refresh
 * @returns {boolean} True if token needs refresh
 */
export const needsTokenRefresh = () => {
  const expiryStr = Cookies.get(TOKEN_EXPIRY_COOKIE);
  if (!expiryStr) return true;
  
  try {
    const expiryTime = new Date(expiryStr);
    const now = new Date();
    
    // Check if token expires within the buffer time
    return (expiryTime.getTime() - now.getTime()) <= TOKEN_REFRESH_BUFFER;
  } catch (error) {
    console.error('Error parsing token expiry:', error);
    return true;
  }
};

/**
 * Check if user has valid authentication (either auth token or session token)
 * @returns {boolean} True if user has valid authentication
 */
export const hasValidAuth = () => {
  return !!(getAuthToken() || getSessionToken());
};

/**
 * Clear all authentication tokens
 */
export const clearTokens = () => {
  Cookies.remove(AUTH_TOKEN_COOKIE);
  Cookies.remove(SESSION_TOKEN_COOKIE);
  Cookies.remove(TOKEN_EXPIRY_COOKIE);
};

/**
 * Update only the auth token (used during refresh)
 * @param {string} authToken - New JWT token
 * @param {number} expiryMinutes - Token expiry in minutes
 */
export const updateAuthToken = (authToken, expiryMinutes = 60) => {
  const expiryTime = new Date(Date.now() + expiryMinutes * 60 * 1000);
  
  Cookies.set(AUTH_TOKEN_COOKIE, authToken, { 
    expires: 7, 
    secure: true, 
    sameSite: 'strict' 
  });
  
  Cookies.set(TOKEN_EXPIRY_COOKIE, expiryTime.toISOString(), { 
    expires: 7, 
    secure: true, 
    sameSite: 'strict' 
  });
};
