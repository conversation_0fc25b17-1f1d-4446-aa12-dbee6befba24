/**
 * Authentication API functions
 */

import { apiPost, apiGet, apiDelete, ApiError } from '../auth/api-client'
import { API_CONFIG } from '../config'
import { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  RegisterResponse,
  User,
  SessionToken,
  CreateChessProfileRequest,
  CreateChessProfileResponse
} from '../auth/types'

/**
 * Login with email/password or session token
 */
export async function login(request: LoginRequest): Promise<LoginResponse> {
  try {
    const response = await apiPost(API_CONFIG.ENDPOINTS.LOGIN, request, { skipAuth: true })
    
    if (!response.ok) {
      const errorText = await response.text()
      throw new ApiError(
        response.status === 401 ? 'Invalid credentials' : 'Login failed',
        response.status
      )
    }
    
    return await response.json()
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error during login')
  }
}

/**
 * Register a new user with invitation code
 */
export async function register(request: RegisterRequest): Promise<RegisterResponse> {
  try {
    const response = await apiPost(API_CONFIG.ENDPOINTS.REGISTER, request, { skipAuth: true })
    
    if (!response.ok) {
      const errorText = await response.text()
      let errorMessage = 'Registration failed'
      
      if (response.status === 400) {
        errorMessage = 'Invalid registration data'
      } else if (response.status === 409) {
        errorMessage = 'Email already exists'
      } else if (response.status === 422) {
        errorMessage = 'Invalid invitation code'
      }
      
      throw new ApiError(errorMessage, response.status)
    }
    
    return await response.json()
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error during registration')
  }
}

/**
 * Get current user information
 */
export async function getCurrentUser(): Promise<User> {
  try {
    const response = await apiGet(API_CONFIG.ENDPOINTS.USER_ME)
    
    if (!response.ok) {
      throw new ApiError('Failed to get user information', response.status)
    }
    
    return await response.json()
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error getting user information')
  }
}

/**
 * Get user's session tokens
 */
export async function getSessionTokens(): Promise<SessionToken[]> {
  try {
    const response = await apiGet(API_CONFIG.ENDPOINTS.SESSION_TOKENS)
    
    if (!response.ok) {
      throw new ApiError('Failed to get session tokens', response.status)
    }
    
    return await response.json()
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error getting session tokens')
  }
}

/**
 * Revoke a session token
 */
export async function revokeSessionToken(tokenId: string): Promise<void> {
  try {
    const response = await apiDelete(`${API_CONFIG.ENDPOINTS.SESSION_TOKENS}/${tokenId}`)
    
    if (!response.ok) {
      throw new ApiError('Failed to revoke session token', response.status)
    }
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error revoking session token')
  }
}

/**
 * Get user's chess profiles
 */
export async function getChessProfiles(): Promise<CreateChessProfileResponse[]> {
  try {
    const response = await apiGet(API_CONFIG.ENDPOINTS.CHESS_PROFILES)
    
    if (!response.ok) {
      throw new ApiError('Failed to get chess profiles', response.status)
    }
    
    return await response.json()
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error getting chess profiles')
  }
}

/**
 * Create a new chess profile
 */
export async function createChessProfile(request: CreateChessProfileRequest): Promise<CreateChessProfileResponse> {
  try {
    const response = await apiPost(API_CONFIG.ENDPOINTS.CHESS_PROFILES, request)
    
    if (!response.ok) {
      let errorMessage = 'Failed to create chess profile'
      
      if (response.status === 400) {
        errorMessage = 'Invalid chess profile data'
      } else if (response.status === 409) {
        errorMessage = 'Chess profile already exists'
      }
      
      throw new ApiError(errorMessage, response.status)
    }
    
    return await response.json()
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error creating chess profile')
  }
}

/**
 * Delete a chess profile
 */
export async function deleteChessProfile(profileId: string): Promise<void> {
  try {
    const response = await apiDelete(`${API_CONFIG.ENDPOINTS.CHESS_PROFILES}/${profileId}`)
    
    if (!response.ok) {
      throw new ApiError('Failed to delete chess profile', response.status)
    }
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error deleting chess profile')
  }
}
