/**
 * GraphQL client
 * Adapted from legacy-site/src/services/apiService.js and hooks/useGraphQL.js
 */

import { apiPost, ApiError } from '../auth/api-client'
import { GRAPHQL_ENDPOINT } from '../config'

export interface GraphQLRequest {
  query: string
  variables?: Record<string, any>
}

export interface GraphQLResponse<T = any> {
  data?: T
  errors?: Array<{
    message: string
    locations?: Array<{ line: number; column: number }>
    path?: Array<string | number>
  }>
}

/**
 * Execute a GraphQL request
 */
export async function graphqlRequest<T = any>(
  query: string,
  variables: Record<string, any> = {}
): Promise<T> {
  try {
    const response = await apiPost(GRAPHQL_ENDPOINT, {
      query,
      variables,
    })

    if (!response.ok) {
      throw new ApiError(`GraphQL request failed: ${response.status}`, response.status)
    }

    const result: GraphQLResponse<T> = await response.json()

    if (result.errors && result.errors.length > 0) {
      const errorMessage = result.errors.map(error => error.message).join(', ')
      throw new ApiError(`GraphQL errors: ${errorMessage}`)
    }

    if (!result.data) {
      throw new ApiError('No data returned from GraphQL query')
    }

    return result.data
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error during GraphQL request')
  }
}

// Common GraphQL queries from the legacy site
export const QUERIES = {
  GET_PUZZLE_STATS: `
    query GetPuzzleStats {
      myPuzzleStats {
        tag_counts {
          tag
          count
        }
        theme_counts {
          theme
          count
        }
        total_count
      }
    }
  `,

  GET_PUZZLES: `
    query GetPuzzles($filter: PuzzleFilter, $pagination: OffsetPaginationInput) {
      myPuzzles(filter: $filter, pagination: $pagination) {
        edges {
          node {
            id
            theme
            fen
            moves
            prev_cp
            cp
            tags
            user_color
            puzzle_color
          }
          cursor
        }
        page_info {
          has_next_page
          has_previous_page
        }
        total_count
      }
    }
  `,

  GET_GAMES: `
    query GetGames($filter: GameFilter, $pagination: OffsetPaginationInput, $sort: SortInput) {
      myGames(filter: $filter, pagination: $pagination, sort: $sort) {
        edges {
          node {
            id
            platform
            game_time
            time_control
            rated
            user_color
            winner
            result
            pgn
            white_player
            black_player
          }
          cursor
        }
        page_info {
          has_next_page
          has_previous_page
        }
        total_count
      }
    }
  `,

  OPPONENT_MISTAKES_MISSED_BY_PERIOD: `
    query OpponentMistakesMissedByPeriod($startDate: String!, $endDate: String!) {
      myPuzzles(
        filter: {
          theme: OPPONENT_MISTAKE_MISSED,
          game_start_time: $startDate,
          game_end_time: $endDate
        },
        pagination: { limit: 1000 }
      ) {
        edges {
          node {
            id
            game {
              game_time
            }
          }
        }
        total_count
      }
      
      allOpponentMistakes: myPuzzles(
        filter: {
          theme_in: [OPPONENT_MISTAKE_CAUGHT, OPPONENT_MISTAKE_MISSED],
          game_start_time: $startDate,
          game_end_time: $endDate
        },
        pagination: { limit: 1000 }
      ) {
        total_count
      }
    }
  `,

  MY_MISTAKES_PER_GAME: `
    query MyMistakesPerGame($startDate: String!) {
      myMistakes: myPuzzles(
        filter: {
          user_color: WHITE,
          puzzle_color: BLACK,
          game_start_time: $startDate
        },
        pagination: { limit: 1000 }
      ) {
        edges {
          node {
            id
            game {
              id
              game_time
            }
          }
        }
      }
      
      myGames(
        filter: {
          start_time: $startDate
        },
        pagination: { limit: 1000 }
      ) {
        total_count
        edges {
          node {
            id
            game_time
          }
        }
      }
    }
  `,
} as const
