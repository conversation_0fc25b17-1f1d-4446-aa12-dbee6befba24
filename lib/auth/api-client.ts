/**
 * Authenticated API client
 * Adapted from legacy-site/src/services/apiService.js
 */

import { API_CONFIG, debugLog } from '../config'
import {
  getAuthToken,
  getSessionToken,
  updateAuthToken,
  clearTokens
} from './tokens'


// Track ongoing refresh to prevent multiple simultaneous refresh attempts
let refreshPromise: Promise<boolean> | null = null

/**
 * Refresh the auth token using the session token
 */
export async function refreshAuthToken(): Promise<boolean> {
  // If refresh is already in progress, wait for it
  if (refreshPromise) {
    return refreshPromise
  }

  const sessionToken = getSessionToken()
  if (!sessionToken) {
    debugLog('No session token available for refresh')
    return false
  }

  refreshPromise = (async () => {
    try {
      debugLog('Attempting token refresh with session token:', sessionToken.substring(0, 10) + '...')

      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.LOGIN}`, {
        method: 'POST',
        headers: {
          ...API_CONFIG.DEFAULT_HEADERS,
        },
        body: JSON.stringify({ session_token: sessionToken }),
      })

      if (!response.ok) {
        debugLog('Token refresh failed:', response.status)
        return false
      }

      const data = await response.json()
      if (data.token) {
        // Update auth token with new token (assume 60 minutes expiry)
        updateAuthToken(data.token, 60)
        debugLog('Token refreshed successfully')
        return true
      } else {
        debugLog('No token in refresh response')
        return false
      }
    } catch (error) {
      debugLog('Token refresh error:', error)
      return false
    } finally {
      refreshPromise = null
    }
  })()

  return refreshPromise
}

/**
 * Handle authenticated requests with automatic retry on 401
 */
async function handleAuthenticatedRequest(
  requestFn: () => Promise<Response>
): Promise<Response> {
  const result = await requestFn()

  // Only retry on 401 errors, not other 4xx errors
  if (result && result.status === 401 && getSessionToken()) {
    debugLog('Received 401, attempting token refresh...')

    const refreshSuccess = await refreshAuthToken()
    if (refreshSuccess) {
      debugLog('Token refresh successful, retrying request...')
      // Retry the request
      return await requestFn()
    } else {
      // Refresh failed, clear tokens and throw error
      debugLog('Token refresh failed, clearing tokens')
      clearTokens()
      throw new ApiError('Authentication failed', 401, 'AUTHENTICATION_FAILED')
    }
  }

  return result
}

/**
 * Make an authenticated API request
 */
export async function apiRequest(
  url: string,
  options: RequestInit = {},
  skipAuth: boolean = false
): Promise<Response> {
  const makeRequest = async () => {
    const fullUrl = url.startsWith('http') ? url : `${API_CONFIG.BASE_URL}${url}`

    // Prepare headers
    const headers: Record<string, string> = {
      ...API_CONFIG.DEFAULT_HEADERS,
    }

    // Add headers from options if provided
    if (options.headers) {
      if (options.headers instanceof Headers) {
        // Handle Headers object
        options.headers.forEach((value, key) => {
          headers[key] = value
        })
      } else if (Array.isArray(options.headers)) {
        // Handle array of [key, value] pairs
        options.headers.forEach(([key, value]) => {
          headers[key] = value
        })
      } else {
        // Handle plain object
        Object.assign(headers, options.headers)
      }
    }

    // Add auth header if not skipping auth
    if (!skipAuth) {
      const authToken = getAuthToken()
      if (authToken) {
        headers.Authorization = `Bearer ${authToken}`
      }
    }

    // Make the request
    const requestOptions = {
      ...options,
      headers,
    }

    // Use AbortSignal from parameter or options (parameter takes precedence)
    if (options.signal) {
      requestOptions.signal = options.signal
    }

    return await fetch(fullUrl, requestOptions)
  }

  // Use shared retry logic if auth is enabled
  if (!skipAuth) {
    return await handleAuthenticatedRequest(makeRequest)
  } else {
    return await makeRequest()
  }
}

/**
 * Convenience method for GET requests
 */
export async function apiGet(
  url: string,
  skipAuth: boolean = false,
  abortSignal?: AbortSignal
): Promise<Response> {
  return apiRequest(url, { method: 'GET', signal: abortSignal }, skipAuth)
}

/**
 * Convenience method for POST requests
 */
export async function apiPost(
  url: string,
  data?: any,
  options: { skipAuth?: boolean; abortSignal?: AbortSignal } = {}
): Promise<Response> {
  const { skipAuth = false, abortSignal } = options

  return apiRequest(url, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
    signal: abortSignal,
  }, skipAuth)
}

/**
 * Convenience method for PUT requests
 */
export async function apiPut(
  url: string,
  data?: any,
  skipAuth: boolean = false,
  abortSignal?: AbortSignal
): Promise<Response> {
  return apiRequest(url, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
    signal: abortSignal,
  }, skipAuth)
}

/**
 * Convenience method for DELETE requests
 */
export async function apiDelete(
  url: string,
  skipAuth: boolean = false,
  abortSignal?: AbortSignal
): Promise<Response> {
  return apiRequest(url, { method: 'DELETE', signal: abortSignal }, skipAuth)
}

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message)
    this.name = 'ApiError'
  }
}
