/**
 * Token management utilities
 * Adapted from legacy-site/src/utils/tokenUtils.js
 */

import Cookies from 'js-cookie'
import { TOKEN_CONFIG } from '../config'
import { TokenData } from './types'

/**
 * Store authentication tokens in cookies
 */
export function storeTokens(
  authToken: string,
  sessionToken?: string,
  expiryMinutes: number = TOKEN_CONFIG.DEFAULT_EXPIRY_MINUTES
): void {
  const expiryTime = new Date(Date.now() + expiryMinutes * 60 * 1000)
  
  // Store auth token
  Cookies.set(TOKEN_CONFIG.AUTH_TOKEN_COOKIE, authToken, TOKEN_CONFIG.COOKIE_OPTIONS)
  
  // Store session token if provided
  if (sessionToken) {
    Cookies.set(TOKEN_CONFIG.SESSION_TOKEN_COOKIE, sessionToken, TOKEN_CONFIG.SESSION_COOKIE_OPTIONS)
  }
  
  // Store token expiry time for refresh logic
  Cookies.set(TOKEN_CONFIG.TOKEN_EXPIRY_COOKIE, expiryTime.toISOString(), TOKEN_CONFIG.COOKIE_OPTIONS)
}

/**
 * Get the current auth token from cookies
 */
export function getAuthToken(): string | null {
  return Cookies.get(TOKEN_CONFIG.AUTH_TOKEN_COOKIE) || null
}

/**
 * Get the current session token from cookies
 */
export function getSessionToken(): string | null {
  return Cookies.get(TOKEN_CONFIG.SESSION_TOKEN_COOKIE) || null
}

/**
 * Get token expiry time from cookies
 */
export function getTokenExpiry(): Date | null {
  const expiryStr = Cookies.get(TOKEN_CONFIG.TOKEN_EXPIRY_COOKIE)
  if (!expiryStr) return null
  
  try {
    return new Date(expiryStr)
  } catch (error) {
    console.error('Error parsing token expiry:', error)
    return null
  }
}

/**
 * Check if the current auth token needs refresh
 */
export function needsTokenRefresh(): boolean {
  const expiryTime = getTokenExpiry()
  if (!expiryTime) return true
  
  const now = new Date()
  // Check if token expires within the buffer time
  return (expiryTime.getTime() - now.getTime()) <= TOKEN_CONFIG.REFRESH_BUFFER
}

/**
 * Check if user has valid authentication (either auth token or session token)
 */
export function hasValidAuth(): boolean {
  return !!(getAuthToken() || getSessionToken())
}

/**
 * Clear all authentication tokens
 */
export function clearTokens(): void {
  Cookies.remove(TOKEN_CONFIG.AUTH_TOKEN_COOKIE)
  Cookies.remove(TOKEN_CONFIG.SESSION_TOKEN_COOKIE)
  Cookies.remove(TOKEN_CONFIG.TOKEN_EXPIRY_COOKIE)
}

/**
 * Update only the auth token (used during refresh)
 */
export function updateAuthToken(
  authToken: string,
  expiryMinutes: number = TOKEN_CONFIG.DEFAULT_EXPIRY_MINUTES
): void {
  const expiryTime = new Date(Date.now() + expiryMinutes * 60 * 1000)
  
  Cookies.set(TOKEN_CONFIG.AUTH_TOKEN_COOKIE, authToken, TOKEN_CONFIG.COOKIE_OPTIONS)
  Cookies.set(TOKEN_CONFIG.TOKEN_EXPIRY_COOKIE, expiryTime.toISOString(), TOKEN_CONFIG.COOKIE_OPTIONS)
}

/**
 * Get all token data at once
 */
export function getTokenData(): TokenData {
  return {
    authToken: getAuthToken(),
    sessionToken: getSessionToken(),
    expiryTime: getTokenExpiry(),
  }
}
