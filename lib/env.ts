/**
 * Environment variable configuration
 * Centralizes all environment variable access with type safety
 */

/**
 * Get default API URL based on environment
 */
function getDefaultApiUrl(): string {
  const nodeEnv = process.env.NODE_ENV || 'development'
  if (nodeEnv === 'production') {
    return 'https://chessticize-server-9ddca5bcf137.herokuapp.com'
  }
  return 'http://localhost:8080'
}

export const env = {
  // API Configuration
  API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || getDefaultApiUrl(),

  // Environment
  NODE_ENV: process.env.NODE_ENV || 'development',

  // Feature flags
  ENABLE_DEBUG_LOGS: process.env.NEXT_PUBLIC_ENABLE_DEBUG_LOGS === 'true',
} as const

export const isDevelopment = env.NODE_ENV === 'development'
export const isProduction = env.NODE_ENV === 'production'
export const isTest = env.NODE_ENV === 'test'

// Validation function to ensure required env vars are present
export function validateEnv() {
  const required = ['NEXT_PUBLIC_API_BASE_URL'] as const
  const missing = required.filter(key => !process.env[key])
  
  if (missing.length > 0) {
    console.warn(`Missing environment variables: ${missing.join(', ')}`)
    console.warn('Using default values for development')
  }
}

// Call validation in non-test environments
if (!isTest) {
  validateEnv()
}
