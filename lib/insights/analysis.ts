/**
 * User data analysis utilities for generating insights
 */

import { User } from "@/lib/auth/types"

export interface UserInsights {
  overview: {
    currentStreak: number
    totalPuzzlesSolved: number
    totalPuzzlesAttempted: number
    overallSuccessRate: number
    daysActive: number
    averageDailyPuzzles: number
  }
  trends: {
    recentPerformance: 'improving' | 'declining' | 'stable'
    streakTrend: number // percentage change
    activityTrend: number // percentage change
  }
  strengths: string[]
  weaknesses: string[]
  recommendations: string[]
}

/**
 * Calculate current streak from daily stats
 */
export function calculateCurrentStreak(dailyStats: Array<{
  date: string
  puzzle_success: number
  puzzle_total: number
  streak: number
}>): number {
  if (!dailyStats || dailyStats.length === 0) return 0
  
  // Sort by date descending to get most recent first
  const sortedStats = [...dailyStats].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  )
  
  return sortedStats[0]?.streak || 0
}

/**
 * Calculate total puzzles solved and attempted
 */
export function calculatePuzzleTotals(dailyStats: Array<{
  date: string
  puzzle_success: number
  puzzle_total: number
  streak: number
}>): { solved: number; attempted: number; successRate: number } {
  if (!dailyStats || dailyStats.length === 0) {
    return { solved: 0, attempted: 0, successRate: 0 }
  }
  
  const totals = dailyStats.reduce(
    (acc, day) => ({
      solved: acc.solved + (day.puzzle_success || 0),
      attempted: acc.attempted + (day.puzzle_total || 0)
    }),
    { solved: 0, attempted: 0 }
  )
  
  const successRate = totals.attempted > 0 ? (totals.solved / totals.attempted) * 100 : 0
  
  return {
    ...totals,
    successRate: Math.round(successRate * 10) / 10 // Round to 1 decimal
  }
}

/**
 * Analyze performance trends over time
 */
export function analyzePerformanceTrends(dailyStats: Array<{
  date: string
  puzzle_success: number
  puzzle_total: number
  streak: number
}>): {
  recentPerformance: 'improving' | 'declining' | 'stable'
  streakTrend: number
  activityTrend: number
} {
  if (!dailyStats || dailyStats.length < 7) {
    return { recentPerformance: 'stable', streakTrend: 0, activityTrend: 0 }
  }
  
  // Sort by date
  const sortedStats = [...dailyStats].sort((a, b) => 
    new Date(a.date).getTime() - new Date(b.date).getTime()
  )
  
  // Compare recent week vs previous week
  const recentWeek = sortedStats.slice(-7)
  const previousWeek = sortedStats.slice(-14, -7)
  
  if (previousWeek.length === 0) {
    return { recentPerformance: 'stable', streakTrend: 0, activityTrend: 0 }
  }
  
  // Calculate success rates
  const recentSuccessRate = calculateSuccessRate(recentWeek)
  const previousSuccessRate = calculateSuccessRate(previousWeek)
  
  // Calculate activity levels
  const recentActivity = recentWeek.reduce((sum, day) => sum + day.puzzle_total, 0)
  const previousActivity = previousWeek.reduce((sum, day) => sum + day.puzzle_total, 0)
  
  // Determine trends
  const performanceDiff = recentSuccessRate - previousSuccessRate
  const activityChange = previousActivity > 0 ? 
    ((recentActivity - previousActivity) / previousActivity) * 100 : 0
  
  let recentPerformance: 'improving' | 'declining' | 'stable' = 'stable'
  if (performanceDiff > 5) recentPerformance = 'improving'
  else if (performanceDiff < -5) recentPerformance = 'declining'
  
  // Calculate streak trend (simplified)
  const recentAvgStreak = recentWeek.reduce((sum, day) => sum + day.streak, 0) / recentWeek.length
  const previousAvgStreak = previousWeek.reduce((sum, day) => sum + day.streak, 0) / previousWeek.length
  const streakTrend = previousAvgStreak > 0 ? 
    ((recentAvgStreak - previousAvgStreak) / previousAvgStreak) * 100 : 0
  
  return {
    recentPerformance,
    streakTrend: Math.round(streakTrend),
    activityTrend: Math.round(activityChange)
  }
}

/**
 * Helper function to calculate success rate for a period
 */
function calculateSuccessRate(stats: Array<{
  puzzle_success: number
  puzzle_total: number
}>): number {
  const totals = stats.reduce(
    (acc, day) => ({
      solved: acc.solved + day.puzzle_success,
      attempted: acc.attempted + day.puzzle_total
    }),
    { solved: 0, attempted: 0 }
  )
  
  return totals.attempted > 0 ? (totals.solved / totals.attempted) * 100 : 0
}

/**
 * Generate insights based on user data
 */
export function generateUserInsights(user: User): UserInsights {
  const dailyStats = user.daily_stats || []
  const sprintStats = user.sprint_daily_stats || []
  
  // Calculate overview metrics
  const currentStreak = calculateCurrentStreak(dailyStats)
  const puzzleTotals = calculatePuzzleTotals(dailyStats)
  const trends = analyzePerformanceTrends(dailyStats)
  
  // Calculate days active
  const daysActive = dailyStats.length
  const averageDailyPuzzles = daysActive > 0 ? puzzleTotals.attempted / daysActive : 0
  
  // Generate strengths and weaknesses based on data
  const strengths: string[] = []
  const weaknesses: string[] = []
  const recommendations: string[] = []
  
  // Analyze strengths
  if (currentStreak >= 7) {
    strengths.push(`Excellent consistency with ${currentStreak}-day streak`)
  }
  if (puzzleTotals.successRate >= 75) {
    strengths.push(`High puzzle accuracy at ${puzzleTotals.successRate}%`)
  }
  if (trends.recentPerformance === 'improving') {
    strengths.push('Recent performance is improving')
  }
  if (averageDailyPuzzles >= 10) {
    strengths.push('High daily puzzle volume')
  }
  
  // Analyze weaknesses
  if (currentStreak < 3) {
    weaknesses.push('Inconsistent daily practice')
  }
  if (puzzleTotals.successRate < 60) {
    weaknesses.push(`Puzzle accuracy could improve (${puzzleTotals.successRate}%)`)
  }
  if (trends.recentPerformance === 'declining') {
    weaknesses.push('Recent performance decline')
  }
  if (averageDailyPuzzles < 5) {
    weaknesses.push('Low daily puzzle volume')
  }
  
  // Generate recommendations
  if (currentStreak < 7) {
    recommendations.push('Try to solve at least 5 puzzles daily to build consistency')
  }
  if (puzzleTotals.successRate < 70) {
    recommendations.push('Focus on accuracy over speed - take time to calculate')
  }
  if (trends.activityTrend < 0) {
    recommendations.push('Increase daily practice time to maintain progress')
  }
  if (sprintStats.length === 0) {
    recommendations.push('Try puzzle sprints to improve pattern recognition speed')
  }
  
  return {
    overview: {
      currentStreak,
      totalPuzzlesSolved: puzzleTotals.solved,
      totalPuzzlesAttempted: puzzleTotals.attempted,
      overallSuccessRate: puzzleTotals.successRate,
      daysActive,
      averageDailyPuzzles: Math.round(averageDailyPuzzles * 10) / 10
    },
    trends,
    strengths,
    weaknesses,
    recommendations
  }
}
