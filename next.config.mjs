/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Add headers for cross-origin isolation (required for SharedArrayBuffer/Stockfish)
  async headers() {
    return [
      {
        // Apply to all routes
        source: '/(.*)',
        headers: [
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'require-corp',
          },
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin',
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
      // PWA-related headers removed - no service worker or manifest
    ]
  },
  // Webpack configuration for Stockfish and WASM support
  webpack: (config, { isServer }) => {
    // Handle WASM files
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
    }

    // Handle worker files and reduce circular dependency warnings
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'webassembly/async',
    })

    // Copy WASM files to public directory for proper access
    config.module.rules.push({
      test: /sf16-7\.wasm$/,
      type: 'asset/resource',
      generator: {
        filename: 'static/wasm/[name][ext]',
      },
    })

    // Handle Stockfish JS files
    config.module.rules.push({
      test: /sf16-7\.js$/,
      type: 'asset/resource',
      generator: {
        filename: 'static/js/[name][ext]',
      },
    })

    // Suppress specific warnings for Stockfish
    config.ignoreWarnings = [
      ...(config.ignoreWarnings || []),
      {
        module: /sf16-7\.js/,
        message: /Circular dependency/,
      },
      {
        module: /em-pthread/,
        message: /Circular dependency/,
      },
      {
        module: /sf16-7\.js/,
        message: /Can't resolve 'a'/,
      },
    ]

    // Resolve fallbacks for Node.js modules
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      crypto: false,
    }

    return config
  },
}

export default nextConfig