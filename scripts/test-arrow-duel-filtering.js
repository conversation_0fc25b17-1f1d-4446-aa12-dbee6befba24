/**
 * Quick test script to debug arrow duel filtering
 * Run with: node scripts/test-arrow-duel-filtering.js
 */

const testPuzzles = [
  {
    puzzle_id: 'test-1',
    fen: 'rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2',
    solution_moves: ['Qh5'], // Clear blunder
    rating: 1200,
    themes: ['blunder'],
    sequence_in_sprint: 1
  },
  {
    puzzle_id: 'test-2', 
    fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
    solution_moves: ['e2e3'], // Slightly inferior to e4
    rating: 800,
    themes: ['opening'],
    sequence_in_sprint: 1
  },
  {
    puzzle_id: 'test-3',
    fen: 'r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 4 4',
    solution_moves: ['Bxf7+'], // Tactical shot
    rating: 1400,
    themes: ['tactics'],
    sequence_in_sprint: 1
  }
]

async function testFiltering() {
  console.log('🔍 Testing Arrow Duel filtering with sample puzzles...')
  
  try {
    // Import the filter
    const { ArrowDuelFilter } = await import('../lib/arrow-duel-filter.ts')
    const filter = new ArrowDuelFilter()
    
    console.log(`📋 Testing ${testPuzzles.length} sample puzzles`)
    
    for (const puzzle of testPuzzles) {
      console.log(`\n🧩 Testing puzzle: ${puzzle.puzzle_id}`)
      console.log(`   FEN: ${puzzle.fen}`)
      console.log(`   Solution: ${puzzle.solution_moves[0]}`)
      
      try {
        const result = await filter.filterPuzzle(puzzle)
        
        if (result) {
          console.log('   ✅ ACCEPTED')
          console.log(`   Best move: ${result.bestMove}`)
          console.log(`   Blunder: ${result.blunderMove}`)
          console.log(`   Eval diff: ${result.evaluationDiff}`)
          console.log(`   Depth: ${result.analysisDepth}`)
        } else {
          console.log('   ❌ REJECTED')
        }
      } catch (error) {
        console.log(`   💥 ERROR: ${error.message}`)
      }
    }
    
  } catch (error) {
    console.error('Failed to import or test:', error)
  }
}

// Run the test
testFiltering()