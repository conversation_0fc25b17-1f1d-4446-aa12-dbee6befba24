#!/bin/bash

# Arrow Duel Pre-deployment Test Script
# Run this before manual testing to catch compilation and basic errors

echo "🏹 Running Arrow Duel Pre-deployment Tests..."
echo "================================================"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to run tests and check results
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "\n${YELLOW}Running $test_name...${NC}"
    
    if eval "$test_command"; then
        echo -e "${GREEN}✅ $test_name passed${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name failed${NC}"
        return 1
    fi
}

# Initialize counters
total_tests=0
passed_tests=0

# Test 1: TypeScript compilation
total_tests=$((total_tests + 1))
if run_test "TypeScript Compilation Check" "pnpm build --dry-run 2>/dev/null || tsc --noEmit"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 2: Smoke tests
total_tests=$((total_tests + 1))
if run_test "Smoke Tests" "pnpm test __tests__/smoke/ArrowDuelSmoke.test.tsx"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 3: Integration tests
total_tests=$((total_tests + 1))
if run_test "Integration Tests" "pnpm test __tests__/components/puzzle-sprint/ArrowDuelIntegration.test.tsx"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 4: Linting
total_tests=$((total_tests + 1))
if run_test "ESLint Check" "pnpm lint --max-warnings 0"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 5: Import validation (quick check that modules can be imported)
total_tests=$((total_tests + 1))
if run_test "Import Validation" "node -e '
require(\"./components/puzzle-sprint/ChessBoard\");
require(\"./components/puzzle-sprint/ArrowDuelMoveSelector\");
require(\"./lib/arrow-duel-filter\");
require(\"./hooks/useStockfish\");
console.log(\"All imports successful\");
'"; then
    passed_tests=$((passed_tests + 1))
fi

# Summary
echo -e "\n================================================"
echo -e "🏹 Arrow Duel Test Summary"
echo -e "================================================"
echo -e "Tests passed: ${GREEN}$passed_tests${NC}/$total_tests"

if [ $passed_tests -eq $total_tests ]; then
    echo -e "${GREEN}🎉 All tests passed! Arrow Duel is ready for manual testing.${NC}"
    exit 0
else
    failed_tests=$((total_tests - passed_tests))
    echo -e "${RED}💥 $failed_tests test(s) failed. Please fix issues before manual testing.${NC}"
    exit 1
fi