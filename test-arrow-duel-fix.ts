import { Arrow<PERSON>uelFilter } from './lib/arrow-duel-filter'
import type { SprintPuzzle } from './lib/arrow-duel-filter'

async function testArrowDuelFix() {
  console.log('🧪 Testing Arrow Duel Fix...\n')
  
  const filter = new ArrowDuelFilter()
  
  // Test puzzle where white to move
  const testPuzzle1: SprintPuzzle = {
    puzzle_id: 'test1',
    fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1', // Starting position
    solution_moves: ['d2d4'], // A normal move, not a blunder
    rating: 1500,
    themes: ['test'],
    sequence_in_sprint: 1
  }
  
  // Test puzzle where black to move  
  const testPuzzle2: SprintPuzzle = {
    puzzle_id: 'test2',
    fen: 'rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq - 0 1', // After 1.e4
    solution_moves: ['e7e5'], // A normal response
    rating: 1500,
    themes: ['test'],
    sequence_in_sprint: 2
  }
  
  console.log('Testing puzzle 1 (white to move)...')
  const result1 = await filter.filterPuzzle(testPuzzle1)
  console.log('Result 1:', result1 ? '✅ Accepted' : '❌ Rejected')
  if (result1) {
    console.log('  Best move:', result1.bestMove)
    console.log('  Blunder move:', result1.blunderMove)  
    console.log('  Eval diff:', result1.evaluationDiff)
    console.log('  Best move eval:', result1.bestMoveEval)
  }
  
  console.log('\nTesting puzzle 2 (black to move)...')
  const result2 = await filter.filterPuzzle(testPuzzle2)
  console.log('Result 2:', result2 ? '✅ Accepted' : '❌ Rejected')
  if (result2) {
    console.log('  Best move:', result2.bestMove)
    console.log('  Blunder move:', result2.blunderMove)
    console.log('  Eval diff:', result2.evaluationDiff)
    console.log('  Best move eval:', result2.bestMoveEval)
  }
  
  console.log('\n📊 Summary:')
  console.log('- New eval threshold: 2.0 pawns (200 centipawns)')
  console.log('- Max acceptable eval: 0.6 pawns (60 centipawns)')
  console.log('- Best move must be favorable for the player making the move')
  
  process.exit(0)
}

testArrowDuelFix().catch(console.error)