import { <PERSON><PERSON>uelFilter } from './lib/arrow-duel-filter'
import type { SprintPuzzle } from './lib/arrow-duel-filter'

async function testStockfishPerspective() {
  console.log('🔬 Testing Stockfish Evaluation Perspective...\n')
  
  const filter = new ArrowDuelFilter()
  
  // Test 1: Starting position (white to move)
  const startingPosition: SprintPuzzle = {
    puzzle_id: 'perspective-test-1',
    fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
    solution_moves: ['e2e3'], // A passive move
    rating: 1500,
    themes: ['test'],
    sequence_in_sprint: 1
  }
  
  // Test 2: Position where white is clearly better (white to move)
  const whiteWinning: SprintPuzzle = {
    puzzle_id: 'perspective-test-2',
    fen: 'rnbqkb1r/pppp1ppp/5n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 0 4', // Italian opening
    solution_moves: ['f3g5'], // Random move
    rating: 1500,
    themes: ['test'],
    sequence_in_sprint: 2
  }
  
  // Test 3: Position where black is clearly better (black to move)
  const blackWinning: SprintPuzzle = {
    puzzle_id: 'perspective-test-3',
    fen: 'r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R b KQkq - 0 4', // Black has extra knight
    solution_moves: ['f8c5'], // Developing move
    rating: 1500,
    themes: ['test'],
    sequence_in_sprint: 3
  }
  
  // Test 4: After 1.e4 (black to move)
  const afterE4: SprintPuzzle = {
    puzzle_id: 'perspective-test-4',
    fen: 'rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq - 0 1',
    solution_moves: ['e7e5'], // Normal response
    rating: 1500,
    themes: ['test'],
    sequence_in_sprint: 4
  }
  
  console.log('=== Test 1: Starting Position (White to move) ===')
  await filter.filterPuzzle(startingPosition)
  
  console.log('\n=== Test 2: White Winning Position (White to move) ===')
  await filter.filterPuzzle(whiteWinning)
  
  console.log('\n=== Test 3: Black Winning Position (Black to move) ===')
  await filter.filterPuzzle(blackWinning)
  
  console.log('\n=== Test 4: After 1.e4 (Black to move) ===')
  await filter.filterPuzzle(afterE4)
  
  console.log('\n📌 Key findings:')
  console.log('- Check the "interpretation" fields to see if positive always means white is better')
  console.log('- Check if evaluations change based on whose turn it is')
  console.log('- Verify our acceptance logic is correct for both colors')
  
  // Small delay to ensure all logs are printed
  await new Promise(resolve => setTimeout(resolve, 100))
  process.exit(0)
}

testStockfishPerspective().catch(console.error)