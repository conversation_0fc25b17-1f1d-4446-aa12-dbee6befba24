import pytest
import os
import json
from unittest.mock import patch, MagicMock

from chessticize_worker.api.admin_client import AdminClient
from chessticize_worker.models import Task

# Set up environment variables for testing
os.environ["CHESSTICIZE_ADMIN_TOKEN"] = "test-token"

@pytest.fixture
def admin_client():
    """Create an AdminClient instance for testing."""
    return AdminClient(worker_id="test-worker-id")

def test_admin_client_initialization():
    """Test that the AdminClient initializes correctly."""
    client = AdminClient(worker_id="test-worker-id")
    assert client.worker_id == "test-worker-id"
    assert client.admin_token == "test-token"
    assert client.headers["Authorization"] == "Bearer test-token"

def test_claim_next_task_success(admin_client, mocker):
    """Test successful claiming of a task."""
    # Mock the response from the API
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "id": "task-123",
        "user_id": "user-456",
        "task_type": "FetchChessGames",
        "task_data": {"chess_profile_id": "profile-789"},
        "status": "in_progress",
        "worker_id": "test-worker-id",
        "attempts": 0,
        "scheduled_at": "2023-01-01T00:00:00Z",
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z",
        "picked_up_at": "2023-01-01T00:00:00Z",
        "error": None
    }
    mock_response.headers = {"Content-Type": "application/json"}

    # Mock the requests.request method
    mock_request = mocker.patch("requests.request", return_value=mock_response)

    # Call the method
    task = admin_client.claim_next_task()

    # Verify the result
    assert task is not None
    assert task.id == "task-123"
    assert task.status == "in_progress"
    assert task.worker_id == "test-worker-id"

    # Verify the API call
    mock_request.assert_called_once()
    args, kwargs = mock_request.call_args
    assert args[0] == "POST"
    assert "claim" in args[1]  # Endpoint should contain "claim"
    assert json.loads(kwargs["data"]) == {"worker_id": "test-worker-id"}

def test_claim_next_task_no_tasks(admin_client, mocker):
    """Test when no tasks are available to claim."""
    # Mock the requests.request method to raise an exception
    import requests
    mock_request = mocker.patch("requests.request")
    mock_request.side_effect = requests.exceptions.RequestException("Not Found")

    # Call the method
    task = admin_client.claim_next_task()

    # Verify the result
    assert task is None