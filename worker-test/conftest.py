import os
import uuid
import subprocess
import pytest
import dotenv
from pathlib import Path

from chessticize_worker import config
from chessticize_worker.api.admin_client import AdminClient
from chessticize_worker.models import ChessPlatform
from tests.user_client import UserClient

# --- Test Configuration Constants ---
API_BASE_URL = "http://localhost:8080"
TEST_USERNAME_CHESSCOM = "zsmickycat"
TEST_PASSWORD = "password123"

# Determine project and server directories
TEST_FILE_DIR = Path(__file__).parent
PROJECT_ROOT = TEST_FILE_DIR.parent
SERVER_DIR = PROJECT_ROOT / "../chessticize-server"
SERVER_ENV_FILE = SERVER_DIR / ".env"
SERVER_EXECUTABLE = SERVER_DIR / "build/chessticize"


def get_admin_token(user_id: str = "test-admin", email: str = "<EMAIL>", expiry: str = "24h") -> str:
    """
    Generates an admin token by calling the chessticize-server's admin-token command.
    """
    if not SERVER_ENV_FILE.is_file():
        raise FileNotFoundError(f"Server .env file not found at: {SERVER_ENV_FILE}")
    if not SERVER_EXECUTABLE.is_file():
        raise FileNotFoundError(f"Server executable not found at: {SERVER_EXECUTABLE}")

    # Load server env vars
    server_env_vars = dotenv.dotenv_values(SERVER_ENV_FILE)
    if not server_env_vars:
        print(f"Warning: No variables loaded from {SERVER_ENV_FILE}")

    db_url = server_env_vars.get("DATABASE_URL")
    if not db_url:
        raise ValueError(f"DATABASE_URL not found in {SERVER_ENV_FILE}")

    command = [
        str(SERVER_EXECUTABLE),
        "admin-token",
        f"-user-id={user_id}",
        f"-email={email}",
        f"-expiry={expiry}",
    ]
    env_vars = os.environ.copy()
    env_vars.update(server_env_vars)
    env_vars["DATABASE_URL"] = db_url

    result = subprocess.run(
        command,
        capture_output=True,
        text=True,
        check=True,
        env=env_vars,
        cwd=SERVER_DIR,
    )
    token = result.stdout.strip()
    if not token:
        raise RuntimeError("Admin-token command did not return a token.")
    return token


@pytest.fixture(scope="session")
def admin_client() -> AdminClient:
    """Provides an AdminClient configured with a generated admin token."""
    # Override base URL
    original_base_url = config.API_BASE_URL
    config.API_BASE_URL = API_BASE_URL

    original_token = os.environ.get(config.ADMIN_API_TOKEN_ENV_VAR)
    try:
        token = get_admin_token()
        os.environ[config.ADMIN_API_TOKEN_ENV_VAR] = token
        client = AdminClient(worker_id=f"test-worker-{uuid.uuid4()}")
        yield client
    except Exception as e:
        pytest.fail(f"Failed to set up admin client with token: {e}")
    finally:
        # Restore config and env
        config.API_BASE_URL = original_base_url
        if original_token:
            os.environ[config.ADMIN_API_TOKEN_ENV_VAR] = original_token
        elif config.ADMIN_API_TOKEN_ENV_VAR in os.environ:
            del os.environ[config.ADMIN_API_TOKEN_ENV_VAR]


@pytest.fixture(scope="function")
def test_user_profile(admin_client) -> tuple[str, str, str]:
    """
    Creates a test user, logs in, and creates a chess.com profile.
    Yields a triple: (user_id, profile_id, user_token).
    """
    user_client = UserClient(base_url=API_BASE_URL)
    # Create a unique test user email
    TEST_USER_EMAIL = f"test_fetch_user_{uuid.uuid4()}@example.com"
    created_user = admin_client.create_user(TEST_USER_EMAIL, TEST_PASSWORD)
    if not created_user or not created_user.id:
        pytest.fail(f"Failed to create test user {TEST_USER_EMAIL} via API.")
    user_id = created_user.id
    # User login to get token
    try:
        user_token = user_client.login(TEST_USER_EMAIL, TEST_PASSWORD)
    except Exception as e:
        pytest.fail(f"Login failed for user {TEST_USER_EMAIL}: {e}")
    # Create chess.com profile via user API
    profile_json = user_client.create_chess_profile(
        user_token,
        platform=ChessPlatform.chess_dot_com.value,
        username=TEST_USERNAME_CHESSCOM,
    )
    profile_id = profile_json.get("id")
    if not profile_id:
        pytest.fail(f"User API did not return profile id after profile creation: {profile_json}")
    yield user_id, profile_id, user_token

# Ensure the chess engine is shut down after the test session
from chessticize_worker.engine.manager import shutdown_engine

def pytest_sessionfinish(session, exitstatus):
    """Shut down the chess engine after all tests complete."""
    shutdown_engine()
