import pytest
import requests
import json
from pathlib import Path

# Paths to the emulator data files
CHESSCOM_ARCHIVES_PATH = Path(__file__).parent / "test_games/chess.com/zsmickycat_archives.json"
CHESSCOM_202503_PATH = Path(__file__).parent / "test_games/chess.com/zsmickycat_202503.json"
CHESSCOM_202504_PATH = Path(__file__).parent / "test_games/chess.com/zsmickycat_202504.json"
LICHESS_PATH = Path(__file__).parent / "test_games/lichess/zsmickycat.json"

class DummyResponse:
    def __init__(self, json_data=None, iter_lines_data=None, status_code=200):
        self._json_data = json_data or {}
        self._iter_lines_data = iter_lines_data or []
        self.status_code = status_code

    def raise_for_status(self):
        if self.status_code >= 400:
            raise requests.HTTPError(f"Status code: {self.status_code}")

    def json(self):
        return self._json_data

    def iter_lines(self):
        return self._iter_lines_data

@pytest.fixture(autouse=True)
def chess_api_emulator(monkeypatch):
    """
    Pytest fixture that monkeypatches requests.get to emulate Lichess and Chess.com API responses
    for the user 'zsmickycat', using static JSON files. This allows tests to run deterministically
    without hitting the real APIs.
    """
    # Preload all emulator data
    with open(CHESSCOM_ARCHIVES_PATH, "r") as f:
        chesscom_archives = json.load(f)
    with open(CHESSCOM_202503_PATH, "r") as f:
        chesscom_202503 = json.load(f)
    with open(CHESSCOM_202504_PATH, "r") as f:
        chesscom_202504 = json.load(f)
    with open(LICHESS_PATH, "r") as f:
        lichess_lines = f.readlines()

    def fake_get(url, headers=None, params=None, stream=False):
        # Chess.com archives endpoint
        if url == "https://api.chess.com/pub/player/zsmickycat/games/archives":
            return DummyResponse(json_data=chesscom_archives)
        # Chess.com monthly archives
        if url == "https://api.chess.com/pub/player/zsmickycat/games/2025/03":
            return DummyResponse(json_data=chesscom_202503)
        if url == "https://api.chess.com/pub/player/zsmickycat/games/2025/04":
            return DummyResponse(json_data=chesscom_202504)
        # Lichess user games endpoint (ndjson streaming)
        if url.startswith("https://lichess.org/api/games/user/zsmickycat"):
            # Each line in the file is a JSON object (ndjson)
            lines = [line.encode("utf-8") for line in lichess_lines if line.strip()]
            # Respect the 'max' query parameter if present
            max_games = None
            if params and 'max' in params:
                try:
                    max_games = int(params['max'])
                except (ValueError, TypeError):
                    pass  # Ignore invalid max values, return all
            if max_games is not None:
                lines = lines[:max_games]
            return DummyResponse(iter_lines_data=lines)
        raise ValueError(f"Emulator: Unexpected URL: {url}")

    monkeypatch.setattr(requests, "get", fake_get) 