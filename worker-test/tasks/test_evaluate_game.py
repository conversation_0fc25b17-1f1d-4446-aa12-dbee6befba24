# Placeholder for evaluate_game task tests
import pytest
import time
import uuid
import os
import logging
from pathlib import Path
from datetime import datetime, timezone, timedelta

# Third-party utilities
from unittest.mock import MagicMock, ANY, call # Keep if direct mocking is needed later

# Typing support
from typing import Optional, List, Dict, Any as TypingAny

# Worker imports
from chessticize_worker.api.admin_client import Admin<PERSON>lient
from chessticize_worker.tasks.fetch_games import handle_fetch_chess_games
from chessticize_worker.tasks.evaluate_game import handle_evaluate_chess_game
from chessticize_worker.models import (
    Task,
    TaskStatus,
    TaskType,
    ChessPlatform,
    FetchChessGamesData,
    EvaluateChessGameData,
    Game
)
from chessticize_worker import config

# Import the chess_api_emulator fixture
from tests.emulator.test_emulator import chess_api_emulator

# Get a logger instance
logger = logging.getLogger(__name__)

# --- Test Configuration ---
# Override EXPECTED_GAMES_TO_FETCH for these tests
EVAL_EXPECTED_GAMES_TO_FETCH = 1


@pytest.mark.integration
def test_evaluate_fresh_game_integration(admin_client: AdminClient, test_user_profile, monkeypatch, chess_api_emulator):
    """
    Tests the handle_evaluate_chess_game task handler for a new game.
    1. Uses test_user_profile to create a user and profile, which creates a FetchChessGames task.
    2. Runs handle_fetch_chess_games to download 1 game (mocked).
       This should create an EvaluateChessGame task.
    3. Runs handle_evaluate_chess_game for this new task.
    4. Verifies:
        - Game PGN is updated with evaluations.
        - A GenerateChessPuzzles task is created.
        - The EvaluateChessGame task is completed.
    """
    user_id, profile_id, user_token = test_user_profile
    assert user_id and profile_id, "Failed to get user and profile IDs"

    # --- 1. Setup: Fetch the initial FetchChessGames task ---
    user_tasks = admin_client.list_user_tasks(user_id)
    assert user_tasks is not None, "Failed to list tasks for user"
    fetch_games_task = next((t for t in user_tasks if t.task_type == TaskType.FetchChessGames and t.task_data.get("chess_profile_id") == profile_id), None)
    assert fetch_games_task is not None, "Failed to find initial FetchChessGames task for the profile"
    initial_fetch_task_id = fetch_games_task.id
    logger.info(f"Initial FetchChessGames task found: {initial_fetch_task_id}")

    # --- 2. Run handle_fetch_chess_games to get a game and an EvaluateChessGame task ---
    monkeypatch.setenv('MAX_GAMES_PER_FETCH', str(EVAL_EXPECTED_GAMES_TO_FETCH))
    import importlib
    importlib.reload(config) # Reload config to pick up the monkeypatched env var

    logger.info(f"\\n--- Running handle_fetch_chess_games for task {initial_fetch_task_id} to generate game data ---")
    task_to_fetch_games = admin_client.get_task(initial_fetch_task_id)
    assert task_to_fetch_games is not None, f"Failed to fetch task {initial_fetch_task_id} before processing"
    
    handle_fetch_chess_games(task_to_fetch_games, admin_client)
    logger.info("handle_fetch_chess_games finished. Verifying results...")

    # Verify fetch_games_task is completed
    completed_fetch_task = admin_client.get_task(initial_fetch_task_id)
    assert completed_fetch_task.status == TaskStatus.completed, f"FetchChessGames task {initial_fetch_task_id} did not complete."

    # Verify one game was created
    games = admin_client.list_user_games(user_id)
    assert games is not None and len(games) == EVAL_EXPECTED_GAMES_TO_FETCH, \
        f"Expected {EVAL_EXPECTED_GAMES_TO_FETCH} game(s) to be created, found {len(games) if games else 0}"
    game_id = games[0].id
    original_pgn = games[0].pgn
    assert original_pgn, "Fetched game has no PGN"
    assert "[%eval" not in original_pgn, "Original PGN should not have evaluations for this test."

    # Find the EvaluateChessGame task created by handle_fetch_chess_games
    all_tasks_after_fetch = admin_client.list_user_tasks(user_id)
    evaluate_task = next((
        t for t in all_tasks_after_fetch 
        if t.task_type == TaskType.EvaluateChessGame and t.task_data.get("game_id") == game_id
    ), None)
    assert evaluate_task is not None, f"Failed to find EvaluateChessGame task for game {game_id}"
    evaluate_task_id = evaluate_task.id
    logger.info(f"EvaluateChessGame task found: {evaluate_task_id} for game {game_id}")

    # --- 3. Run handle_evaluate_chess_game ---
    logger.info(f"\\n--- Running handle_evaluate_chess_game for task {evaluate_task_id} ---")
    # Fetch the full task object again to pass to the handler
    task_to_evaluate = admin_client.get_task(evaluate_task_id)
    assert task_to_evaluate is not None, f"Failed to fetch task {evaluate_task_id} for evaluation"
    
    # Store current number of tasks to check for new task creation
    tasks_before_eval = admin_client.list_user_tasks(user_id)
    
    handle_evaluate_chess_game(task_to_evaluate, admin_client)
    logger.info("handle_evaluate_chess_game finished. Verifying results...")

    # --- 4. Verifications ---
    # 4.1 Verify Game PGN is updated with evaluations
    evaluated_game = admin_client.get_game_by_id(game_id)
    assert evaluated_game is not None, f"Failed to fetch game {game_id} after evaluation"
    assert evaluated_game.pgn is not None, f"Game {game_id} has no PGN after evaluation"
    assert "[%eval" in evaluated_game.pgn, f"PGN for game {game_id} does not contain ' [%eval' after evaluation. PGN: {evaluated_game.pgn}"
    assert evaluated_game.pgn != original_pgn, "PGN should have changed after evaluation."
    logger.info(f"Game {game_id} PGN successfully updated with evaluations.")

    # 4.2 Verify a GenerateChessPuzzles task is created
    tasks_after_eval = admin_client.list_user_tasks(user_id)
    assert len(tasks_after_eval) > len(tasks_before_eval), "Number of tasks should have increased."
    
    puzzle_task = next((
        t for t in tasks_after_eval 
        if t.task_type == TaskType.GenerateChessPuzzles and \
           t.task_data.get("game_id") == game_id and \
           t.created_at > evaluate_task.created_at # Ensure it's a new task
    ), None)
    assert puzzle_task is not None, f"Failed to find new GenerateChessPuzzles task for game {game_id}"
    logger.info(f"GenerateChessPuzzles task {puzzle_task.id} found.")

    # 4.3 Verify The EvaluateChessGame task is completed
    completed_evaluate_task = admin_client.get_task(evaluate_task_id)
    assert completed_evaluate_task is not None, f"Failed to fetch task {evaluate_task_id} after evaluation"
    assert completed_evaluate_task.status == TaskStatus.completed, \
        f"EvaluateChessGame task {evaluate_task_id} should be completed, but is {completed_evaluate_task.status}. Error: {completed_evaluate_task.error}"
    logger.info(f"EvaluateChessGame task {evaluate_task_id} status verified: {completed_evaluate_task.status}")

    logger.info("\\n--- test_evaluate_fresh_game_integration finished successfully ---")


@pytest.mark.integration
def test_evaluate_already_evaluated_game(admin_client: AdminClient, test_user_profile, monkeypatch, chess_api_emulator):
    """
    Tests that handle_evaluate_chess_game does not re-evaluate an already evaluated game PGN
    but still creates the puzzle task.
    1. Setup a game and an EvaluateChessGame task (similar to the fresh game test).
    2. Manually update the game's PGN on the server to include dummy evaluation marks.
    3. Runs handle_evaluate_chess_game.
    4. Verifies:
        - Game PGN remains unchanged.
        - A GenerateChessPuzzles task is created.
        - The EvaluateChessGame task is completed.
    """
    user_id, profile_id, user_token = test_user_profile
    assert user_id and profile_id, "Failed to get user and profile IDs"

    # --- 1. Setup: Fetch the initial FetchChessGames task & Run it ---
    user_tasks_initial = admin_client.list_user_tasks(user_id)
    fetch_games_task_initial = next((t for t in user_tasks_initial if t.task_type == TaskType.FetchChessGames and t.task_data.get("chess_profile_id") == profile_id), None)
    assert fetch_games_task_initial is not None, "Failed to find initial FetchChessGames task"
    initial_fetch_task_id = fetch_games_task_initial.id

    monkeypatch.setenv('MAX_GAMES_PER_FETCH', str(EVAL_EXPECTED_GAMES_TO_FETCH))
    import importlib
    importlib.reload(config)

    logger.info(f"\\n--- Running handle_fetch_chess_games for task {initial_fetch_task_id} (pre-eval test setup) ---")
    task_to_fetch_games_for_pre_eval = admin_client.get_task(initial_fetch_task_id)
    handle_fetch_chess_games(task_to_fetch_games_for_pre_eval, admin_client)

    games_after_fetch = admin_client.list_user_games(user_id)
    assert games_after_fetch is not None and len(games_after_fetch) == EVAL_EXPECTED_GAMES_TO_FETCH
    game_to_modify = games_after_fetch[0]
    game_id = game_to_modify.id
    assert game_to_modify.pgn, "Fetched game has no PGN"
    logger.info(f"Game {game_id} fetched for pre-evaluation test.")

    # --- 2. Manually update game PGN to include evaluations ---
    evaluated_pgn_content = game_to_modify.pgn + " { [%eval #10] Test evaluation comment. }"
    logger.info(f"Manually updating PGN for game {game_id} to simulate pre-evaluation.")
    
    # Use a unique idempotency key for this manual update
    manual_update_key = f"manual-pgn-update-{game_id}-{uuid.uuid4()}"
    updated_game = admin_client.update_game(game_id, {"pgn": evaluated_pgn_content}, idempotency_key=manual_update_key)
    assert updated_game is not None and updated_game.pgn == evaluated_pgn_content, \
        f"Failed to manually update PGN for game {game_id}"
    logger.info(f"Game {game_id} PGN updated with dummy eval data.")
    
    # Find the EvaluateChessGame task for this game
    all_tasks_after_fetch_for_pre_eval = admin_client.list_user_tasks(user_id)
    evaluate_task_for_pre_eval = next((
        t for t in all_tasks_after_fetch_for_pre_eval
        if t.task_type == TaskType.EvaluateChessGame and t.task_data.get("game_id") == game_id
    ), None)
    assert evaluate_task_for_pre_eval is not None, f"Failed to find EvaluateChessGame task for game {game_id} (pre-eval test)"
    evaluate_task_id_pre_eval = evaluate_task_for_pre_eval.id
    logger.info(f"EvaluateChessGame task found: {evaluate_task_id_pre_eval} for pre-evaluated game {game_id}")

    # --- 3. Run handle_evaluate_chess_game ---
    logger.info(f"\\n--- Running handle_evaluate_chess_game for task {evaluate_task_id_pre_eval} (pre-evaluated game) ---")
    task_to_evaluate_pre_eval = admin_client.get_task(evaluate_task_id_pre_eval)
    
    tasks_before_second_eval = admin_client.list_user_tasks(user_id)
    
    handle_evaluate_chess_game(task_to_evaluate_pre_eval, admin_client)
    logger.info("handle_evaluate_chess_game finished for pre-evaluated game. Verifying results...")

    # --- 4. Verifications ---
    # 4.1 Verify Game PGN is UNCHANGED
    game_after_second_eval = admin_client.get_game_by_id(game_id)
    assert game_after_second_eval is not None, f"Failed to fetch game {game_id} after second evaluation"
    assert game_after_second_eval.pgn == evaluated_pgn_content, \
        f"PGN for game {game_id} should NOT have changed. Expected:\\n{evaluated_pgn_content}\\nGot:\\n{game_after_second_eval.pgn}"
    logger.info(f"Game {game_id} PGN correctly remained unchanged.")

    # 4.2 Verify a NEW GenerateChessPuzzles task is created
    # (even if the game PGN wasn't changed, the puzzle task should still be scheduled)
    tasks_after_second_eval = admin_client.list_user_tasks(user_id)
    assert len(tasks_after_second_eval) > len(tasks_before_second_eval), "Number of tasks should have increased (puzzle task)."
    
    new_puzzle_task = next((
        t for t in tasks_after_second_eval
        if t.task_type == TaskType.GenerateChessPuzzles and \
           t.task_data.get("game_id") == game_id and \
           t.created_at > evaluate_task_for_pre_eval.created_at # Make sure it's newer than the eval task itself
    ), None)
    assert new_puzzle_task is not None, f"Failed to find new GenerateChessPuzzles task for game {game_id} (pre-evaluated scenario)"
    logger.info(f"New GenerateChessPuzzles task {new_puzzle_task.id} found for pre-evaluated game.")

    # 4.3 Verify The EvaluateChessGame task is completed
    completed_evaluate_task_pre_eval = admin_client.get_task(evaluate_task_id_pre_eval)
    assert completed_evaluate_task_pre_eval is not None
    assert completed_evaluate_task_pre_eval.status == TaskStatus.completed, \
        f"EvaluateChessGame task {evaluate_task_id_pre_eval} should be completed, but is {completed_evaluate_task_pre_eval.status}. Error: {completed_evaluate_task_pre_eval.error}"
    logger.info(f"EvaluateChessGame task {evaluate_task_id_pre_eval} (pre-evaluated) status verified: {completed_evaluate_task_pre_eval.status}")
    
    logger.info("\\n--- test_evaluate_already_evaluated_game finished successfully ---")
