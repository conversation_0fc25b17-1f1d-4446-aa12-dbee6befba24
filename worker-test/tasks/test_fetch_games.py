# Placeholder for fetch_games task tests
import pytest
import time
import uuid
import os
import subprocess
import logging
from pathlib import Path
from datetime import datetime, timezone, timedelta
# Third-party utilities
import dotenv  # Added for .env loading
from unittest.mock import MagicMock, ANY, call

# Typing support
from typing import Optional, List, Dict, Any as TypingAny

# Worker imports
from chessticize_worker.api.admin_client import AdminClient
from chessticize_worker.tasks.fetch_games import handle_fetch_chess_games, _generate_idempotency_key
from chessticize_worker.models import (
    Task,
    TaskStatus,
    TaskType,
    ChessPlatform,
    FetchChessGamesData,
    Game,
    ChessProfile,
    Color,
    Winner,
    GameResult,
    EvaluateChessGameData,
    GenerateChessPuzzlesData,
    PlayerInfo
)
from chessticize_worker import config
# Import the class needed for spec
# from chessticize_worker.chess_logic.download.base_downloader import BaseDownloader
from chessticize_worker.chess_logic.download.chesscom_downloader import ChessComDownloader

# Test helper client for user APIs
from tests.user_client import UserClient

# Import the chess_api_emulator fixture
from tests.emulator.test_emulator import chess_api_emulator  # Enables deterministic API emulation for Chess.com and Lichess in integration tests

# Get a logger instance
logger = logging.getLogger(__name__)

# --- Test Configuration ---
# Assumes the chessticize-server is running and accessible at this URL
# You might need to set the ADMIN_API_TOKEN_ENV_VAR environment variable
# (e.g., export CHESSTICIZE_ADMIN_TOKEN='your_token')
API_BASE_URL = "http://localhost:8080" # Default, adjust if needed
EXPECTED_GAMES_TO_FETCH = 3 # Control how many games to fetch

# --- Additional Test Configuration for Lichess ---
TEST_USERNAME_LICHESS = "zsmickycat"  # A real lichess user with games
TEST_USER_EMAIL_LICHESS = f"test_fetch_user_lichess_{uuid.uuid4()}@example.com"
TEST_PASSWORD = "password123"

# --- Fixtures ---

@pytest.fixture(scope="function")
def test_lichess_user_profile(admin_client):
    """
    Creates a test user and a lichess profile for that user via API calls.
    Yields the user_id and profile_id.
    Attempts to clean up the user and profile afterwards (best effort).
    """
    user_id: Optional[str] = None
    profile_id: Optional[str] = None
    created_user = None

    user_client = UserClient(base_url=API_BASE_URL)

    print(f"\n--- Setting up test user ({TEST_USER_EMAIL_LICHESS}) and lichess profile ({TEST_USERNAME_LICHESS}) via API ---")
    # 1. Create User
    print(f"Creating user: {TEST_USER_EMAIL_LICHESS}")
    created_user = admin_client.create_user(TEST_USER_EMAIL_LICHESS, TEST_PASSWORD)
    if not created_user or not created_user.id:
        pytest.fail(f"Failed to create test user {TEST_USER_EMAIL_LICHESS} via API.")
    user_id = created_user.id
    print(f"User created with ID: {user_id}")

    # 2. Login as the newly created user to obtain a user token
    print(f"Logging in as newly created user to obtain token for lichess-profile creation...")
    try:
        user_token = user_client.login(TEST_USER_EMAIL_LICHESS, TEST_PASSWORD)
    except Exception as e:
        pytest.fail(f"Login failed for user {TEST_USER_EMAIL_LICHESS}: {e}")

    # 3. Create Chess Profile via *user* API (POST /users/me/chess-profiles)
    print(
        f"Creating lichess profile via user API for {TEST_USERNAME_LICHESS} (user {user_id})"
    )
    try:
        profile_json = user_client.create_chess_profile(
            user_token,
            platform=ChessPlatform.lichess_org.value,
            username=TEST_USERNAME_LICHESS,
        )
    except Exception as e:
        pytest.fail(
            f"Failed to create lichess profile for {TEST_USERNAME_LICHESS}: {e}"
        )

    profile_id = profile_json.get("id")
    if not profile_id:
        pytest.fail(
            "User API did not return profile id after lichess profile creation: "
            f"{profile_json}"
        )
    print(f"Lichess profile created with ID: {profile_id}")

    yield user_id, profile_id

# --- Integration Test ---

@pytest.mark.integration  # Mark as integration test
def test_fetch_games_integration_and_idempotency(admin_client, test_user_profile, monkeypatch):
    """
    Tests the handle_fetch_chess_games task handler integration.
    1. Get the initial FetchChessGames task created with the test user profile.
    2. Mocks config to fetch a specific number of games.
    3. Runs the handler.
    4. Verifies games are downloaded and subsequent tasks are created.
    5. Runs the handler again with the same task.
    6. Verifies idempotency (no duplicate games/tasks).
    """
    user_id, profile_id, user_token = test_user_profile
    assert user_id and profile_id, "Failed to get user and profile IDs"

    # --- Setup Task ---
    # Initial task is created by the user API when the profile is created
    # We just need to fetch it via the admin client
    user_tasks = admin_client.list_user_tasks(user_id)
    assert user_tasks is not None, "Failed to list tasks for user"
    initial_task = next((t for t in user_tasks if t.task_type == TaskType.FetchChessGames), None)
    assert initial_task is not None, "Failed to find initial FetchChessGames task"
    initial_task_id = initial_task.id
    print(f"Initial task created with ID: {initial_task_id}")

    # --- Override MAX_GAMES_PER_FETCH via environment variable ---
    monkeypatch.setenv('MAX_GAMES_PER_FETCH', str(EXPECTED_GAMES_TO_FETCH))
    # Reload the config module so it re-reads the env var
    import importlib
    importlib.reload(config)

    # --- First Execution ---
    print(f"\n--- Running handle_fetch_chess_games for task {initial_task_id} (First Run) ---")
    # Fetch the task object again to pass to the handler
    task_to_process = admin_client.get_task(initial_task_id)
    assert task_to_process is not None, f"Failed to fetch task {initial_task_id} before processing"
    handle_fetch_chess_games(task_to_process, admin_client)
    print("Handler finished (First Run). Verifying results...")

    # --- Verification (First Run) ---
    # 1. Verify Initial Task Status
    processed_task = admin_client.get_task(initial_task_id)
    assert processed_task is not None, f"Failed to fetch task {initial_task_id} after processing"
    assert processed_task.status == TaskStatus.completed, f"Task {initial_task_id} should be completed, but is {processed_task.status}"
    print(f"Task {initial_task_id} status verified: {processed_task.status}")

    # 2. Verify Games Created
    games = admin_client.list_user_games(user_id)
    assert games is not None, f"Failed to list games for user {user_id}"
    assert len(games) == EXPECTED_GAMES_TO_FETCH, f"Expected {EXPECTED_GAMES_TO_FETCH} games, found {len(games)}"

    # 3. Verify Subsequent Tasks Created
    all_tasks = admin_client.list_user_tasks(user_id)
    assert all_tasks is not None, f"Failed to list tasks for user {user_id}"

    # Filter tasks created *after* the initial task
    subsequent_tasks = [t for t in all_tasks if t.id != initial_task_id and t.created_at > initial_task.created_at]
    
    # Expected subsequent tasks:
    # - EXPECTED_GAMES_TO_FETCH tasks (either EvaluateChessGame or GenerateChessPuzzles)
    # - 1 FetchChessGames task scheduled for the future
    total_expected_subsequent = EXPECTED_GAMES_TO_FETCH + 1
    assert len(subsequent_tasks) == total_expected_subsequent, f"Expected {total_expected_subsequent} subsequent tasks, found {len(subsequent_tasks)}"

    eval_tasks = [t for t in subsequent_tasks if t.task_type == TaskType.EvaluateChessGame]
    next_fetch_tasks = [t for t in subsequent_tasks if t.task_type == TaskType.FetchChessGames]

    assert len(eval_tasks) == EXPECTED_GAMES_TO_FETCH, f"Expected {EXPECTED_GAMES_TO_FETCH} eval/puzzle tasks, found {len(eval_tasks)}"
    assert len(next_fetch_tasks) == 1, f"Expected 1 next fetch task, found {len(next_fetch_tasks)}"
    assert next_fetch_tasks[0].status == TaskStatus.pending
    assert next_fetch_tasks[0].scheduled_at > datetime.now(timezone.utc) - timedelta(minutes=1) # Should be scheduled in the future
    print(f"Verified {len(eval_tasks)} eval/puzzle tasks and {len(next_fetch_tasks)} next fetch task.")

    # --- Idempotency Test ---
    print(f"\n--- Running handle_fetch_chess_games for task {initial_task_id} (Second Run - Idempotency Check) ---")
    # Fetch the *original* task object again (its status is now completed)
    task_for_idempotency = admin_client.get_task(initial_task_id)
    assert task_for_idempotency is not None, f"Failed to fetch task {initial_task_id} for idempotency check"
    handle_fetch_chess_games(task_for_idempotency, admin_client)
    print("Handler finished (Second Run). Verifying idempotency...")

    # --- Verification (Idempotency) ---
    # 1. Verify Game Count Unchanged
    games_after_idem = admin_client.list_user_games(user_id)
    assert games_after_idem is not None, f"Failed to list games for user {user_id} (idempotency check)"
    assert len(games_after_idem) == EXPECTED_GAMES_TO_FETCH, f"Idempotency check failed: Expected {EXPECTED_GAMES_TO_FETCH} games, found {len(games_after_idem)}"

    # 2. Verify Task Count Unchanged
    tasks_after_idem = admin_client.list_user_tasks(user_id)
    assert tasks_after_idem is not None, f"Failed to list tasks for user {user_id} (idempotency check)"
    assert len(tasks_after_idem) == total_expected_subsequent + 1, f"Idempotency check failed: Expected {total_expected_subsequent + 1} total tasks, found {len(tasks_after_idem)}"
    print("Idempotency Task count verification skipped.")
    print("\nIntegration test finished.")

@pytest.mark.integration  # Mark as integration test
def test_fetch_games_integration_and_idempotency_lichess(admin_client, test_lichess_user_profile, monkeypatch):
    """
    Tests the handle_fetch_chess_games task handler integration for Lichess.
    1. Get the initial FetchChessGames task created with the test user profile.
    2. Mocks config to fetch a specific number of games.
    3. Runs the handler.
    4. Verifies games are downloaded and subsequent tasks are created.
    5. Runs the handler again with the same task.
    6. Verifies idempotency (no duplicate games/tasks).
    """
    user_id, profile_id = test_lichess_user_profile
    assert user_id and profile_id, "Failed to get user and profile IDs"

    # --- Setup Task ---
    user_tasks = admin_client.list_user_tasks(user_id)
    assert user_tasks is not None, "Failed to list tasks for user"
    initial_task = next((t for t in user_tasks if t.task_type == TaskType.FetchChessGames), None)
    assert initial_task is not None, "Failed to find initial FetchChessGames task"
    initial_task_id = initial_task.id
    print(f"Initial task created with ID: {initial_task_id}")

    # --- Override MAX_GAMES_PER_FETCH via environment variable ---
    monkeypatch.setenv('MAX_GAMES_PER_FETCH', str(EXPECTED_GAMES_TO_FETCH))
    import importlib
    importlib.reload(config)

    # --- First Execution ---
    print(f"\n--- Running handle_fetch_chess_games for task {initial_task_id} (First Run, Lichess) ---")
    task_to_process = admin_client.get_task(initial_task_id)
    assert task_to_process is not None, f"Failed to fetch task {initial_task_id} before processing"
    handle_fetch_chess_games(task_to_process, admin_client)
    print("Handler finished (First Run, Lichess). Verifying results...")

    # --- Verification (First Run) ---
    processed_task = admin_client.get_task(initial_task_id)
    assert processed_task is not None, f"Failed to fetch task {initial_task_id} after processing"
    assert processed_task.status == TaskStatus.completed, f"Task {initial_task_id} should be completed, but is {processed_task.status}"
    print(f"Task {initial_task_id} status verified: {processed_task.status}")

    games = admin_client.list_user_games(user_id)
    assert games is not None, f"Failed to list games for user {user_id}"
    assert len(games) == EXPECTED_GAMES_TO_FETCH, f"Expected {EXPECTED_GAMES_TO_FETCH} games, found {len(games)}"

    all_tasks = admin_client.list_user_tasks(user_id)
    assert all_tasks is not None, f"Failed to list tasks for user {user_id}"
    subsequent_tasks = [t for t in all_tasks if t.id != initial_task_id and t.created_at > initial_task.created_at]
    total_expected_subsequent = EXPECTED_GAMES_TO_FETCH + 1
    assert len(subsequent_tasks) == total_expected_subsequent, f"Expected {total_expected_subsequent} subsequent tasks, found {len(subsequent_tasks)}"
    eval_tasks = [t for t in subsequent_tasks if t.task_type == TaskType.EvaluateChessGame]
    next_fetch_tasks = [t for t in subsequent_tasks if t.task_type == TaskType.FetchChessGames]
    assert len(eval_tasks) == EXPECTED_GAMES_TO_FETCH, f"Expected {EXPECTED_GAMES_TO_FETCH} eval/puzzle tasks, found {len(eval_tasks)}"
    assert len(next_fetch_tasks) == 1, f"Expected 1 next fetch task, found {len(next_fetch_tasks)}"
    assert next_fetch_tasks[0].status == TaskStatus.pending
    assert next_fetch_tasks[0].scheduled_at > datetime.now(timezone.utc) - timedelta(minutes=1)
    print(f"Verified {len(eval_tasks)} eval/puzzle tasks and {len(next_fetch_tasks)} next fetch task (Lichess).")

    # --- Idempotency Test ---
    print(f"\n--- Running handle_fetch_chess_games for task {initial_task_id} (Second Run - Idempotency Check, Lichess) ---")
    task_for_idempotency = admin_client.get_task(initial_task_id)
    assert task_for_idempotency is not None, f"Failed to fetch task {initial_task_id} for idempotency check"
    handle_fetch_chess_games(task_for_idempotency, admin_client)
    print("Handler finished (Second Run, Lichess). Verifying idempotency...")

    games_after_idem = admin_client.list_user_games(user_id)
    assert games_after_idem is not None, f"Failed to list games for user {user_id} (idempotency check)"
    assert len(games_after_idem) == EXPECTED_GAMES_TO_FETCH, f"Idempotency check failed: Expected {EXPECTED_GAMES_TO_FETCH} games, found {len(games_after_idem)}"
    tasks_after_idem = admin_client.list_user_tasks(user_id)
    assert tasks_after_idem is not None, f"Failed to list tasks for user {user_id} (idempotency check)"
    assert len(tasks_after_idem) == total_expected_subsequent + 1, f"Idempotency check failed: Expected {total_expected_subsequent + 1} total tasks, found {len(tasks_after_idem)}"
    print("Idempotency Task count verification skipped (Lichess).\nIntegration test finished (Lichess).")

# --- Helper Functions for State Normalization ---

def normalize_task_for_comparison(task_data: dict) -> dict:
    """Removes volatile fields from a task dictionary for comparison."""
    normalized = task_data.copy()
    normalized.pop("id", None)
    normalized.pop("user_id", None)
    normalized.pop("created_at", None)
    normalized.pop("updated_at", None)
    normalized.pop("scheduled_at", None) # Very volatile
    normalized.pop("picked_up_at", None)
    normalized.pop("completed_at", None)
    normalized.pop("worker_id", None)
    normalized.pop("attempts", None) # Can vary depending on test setup
    normalized.pop("error", None)
    # Convert enums to values
    if isinstance(normalized.get("task_type"), TaskType):
        normalized["task_type"] = normalized["task_type"].value
    if isinstance(normalized.get("status"), TaskStatus):
        normalized["status"] = normalized["status"].value
    # Normalize task_data dictionary
    if "task_data" in normalized and isinstance(normalized["task_data"], dict):
        # Remove game_id as it's hard to predict
        normalized["task_data"].pop("game_id", None)
        # Remove user_id if present in task_data (found in actual tasks)
        normalized["task_data"].pop("user_id", None)
    return normalized

def get_sort_key_for_task(task_dict: dict) -> tuple:
    """Provides a stable sort key for task dictionaries."""
    task_type = task_dict.get("task_type", "")
    # Use profile ID for Fetch tasks, placeholder for others if game_id removed
    profile_id = task_dict.get("task_data", {}).get("chess_profile_id", "")
    # Use a placeholder derived from the task dict itself to distinguish eval/puzzle tasks
    # This assumes the order of tasks created matches the order of games processed
    # A better approach might be needed if game processing becomes concurrent
    task_data_str = str(sorted(task_dict.get("task_data", {}).items()))
    return (task_type, profile_id, task_data_str)
