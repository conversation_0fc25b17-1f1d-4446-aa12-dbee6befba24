# Placeholder for generate_puzzles task tests
import pytest
import os
import logging
import importlib
from datetime import datetime, timezone

from chessticize_worker.api.admin_client import AdminClient
from chessticize_worker.tasks.fetch_games import handle_fetch_chess_games
from chessticize_worker.tasks.evaluate_game import handle_evaluate_chess_game
from chessticize_worker.tasks.generate_puzzles import handle_generate_chess_puzzles
from chessticize_worker.models import TaskType, TaskStatus
from chessticize_worker import config

# Import emulator fixture
from tests.emulator.test_emulator import chess_api_emulator
from tests.user_client import UserClient  # Added UserClient

logger = logging.getLogger(__name__)

@pytest.mark.integration
def test_generate_puzzles_integration(admin_client: AdminClient, test_user_profile, monkeypatch, chess_api_emulator):
    """
    Full integration test for puzzle generation:
    1. Fetch one game.
    2. Evaluate the game.
    3. Generate puzzles for the game.
    4. Verify puzzles are stored and task is completed.
    """
    user_id, profile_id, user_token = test_user_profile
    assert user_id and profile_id, "Failed to get user and profile IDs"

    # 1. Run FetchChessGames task to download 1 game
    user_tasks = admin_client.list_user_tasks(user_id)
    fetch_task = next(
        t for t in user_tasks
        if t.task_type == TaskType.FetchChessGames and t.task_data.get("chess_profile_id") == profile_id
    )
    assert fetch_task is not None, "FetchChessGames task not found"

    # Restrict to 1 game
    monkeypatch.setenv('MAX_GAMES_PER_FETCH', '1')
    importlib.reload(config)

    task_to_fetch = admin_client.get_task(fetch_task.id)
    handle_fetch_chess_games(task_to_fetch, admin_client)

    # Verify fetch completed
    completed_fetch = admin_client.get_task(fetch_task.id)
    assert completed_fetch.status == TaskStatus.completed, "FetchChessGames task did not complete"

    # Fetch created game
    games = admin_client.list_user_games(user_id)
    assert games and len(games) == 1, f"Expected 1 game, got {len(games) if games else 0}"
    game_id = games[0].id

    # 2. Run EvaluateChessGame task
    eval_task = next(
        t for t in admin_client.list_user_tasks(user_id)
        if t.task_type == TaskType.EvaluateChessGame and t.task_data.get("game_id") == game_id
    )
    assert eval_task is not None, "EvaluateChessGame task not found"
    handle_evaluate_chess_game(admin_client.get_task(eval_task.id), admin_client)

    # Verify evaluation completed
    completed_eval = admin_client.get_task(eval_task.id)
    assert completed_eval.status == TaskStatus.completed, "EvaluateChessGame task did not complete"

    # 3. Run GenerateChessPuzzles task
    puzzle_task = next(
        t for t in admin_client.list_user_tasks(user_id)
        if t.task_type == TaskType.GenerateChessPuzzles and t.task_data.get("game_id") == game_id
    )
    assert puzzle_task is not None, "GenerateChessPuzzles task not found"
    handle_generate_chess_puzzles(admin_client.get_task(puzzle_task.id), admin_client)

    # Verify puzzle generation completed
    completed_puzzle = admin_client.get_task(puzzle_task.id)
    assert completed_puzzle.status == TaskStatus.completed, "GenerateChessPuzzles task did not complete"

    # 4. Verify puzzles via public user API
    user_client = UserClient(base_url=config.API_BASE_URL)
    puzzles = user_client.list_puzzles(user_token)
    assert puzzles and len(puzzles["puzzles"]) > 0, f"Expected puzzles for game {game_id}, got none"
    # Check each puzzle is for this game and has tags
    for p in puzzles["puzzles"]:
        assert p.get("game_id") == game_id, f"Puzzle game_id mismatch: {p.get('game_id')}"
        assert "tags" in p, "Puzzle should have tags field"
        assert isinstance(p["tags"], list), "Puzzle tags should be a list"
        assert len(p["tags"]) > 0, f"Puzzle should have at least one tag, got: {p['tags']}"
    logger.info(f"Generated and stored {len(puzzles['puzzles'])} puzzles for game {game_id} with tags")
