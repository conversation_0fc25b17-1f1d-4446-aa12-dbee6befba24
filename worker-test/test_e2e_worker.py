# tests/test_e2e_worker.py
import os
import subprocess
import time
import uuid
import pytest
import logging
import dotenv

# Assuming tests run from the project root, adjust path if necessary
# This allows importing the client for test setup/verification
import threading
import sys
from chessticize_worker.api.admin_client import AdminClient
from chessticize_worker.config import API_BASE_URL as DEFAULT_API_BASE_URL, ADMIN_API_TOKEN_ENV_VAR
from chessticize_worker.models import ChessPlatform
from tests.conftest import get_admin_token
from tests.user_client import UserClient

# Import the test emulator
from tests.emulator.test_emulator import chess_api_emulator

# --- Test Setup ---

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# E2E Mark - Skip unless RUN_E2E=true
e2e_skip_condition = not os.getenv("RUN_E2E") == "true"
skip_reason = "Skipping E2E test (set RUN_E2E=true to run)"
e2e_test = pytest.mark.skipif(e2e_skip_condition, reason=skip_reason)

# Direct test mark - Skip unless RUN_DIRECT=true
direct_skip_condition = not os.getenv("RUN_DIRECT") == "true"
direct_skip_reason = "Skipping direct test (set RUN_DIRECT=true to run)"
direct_test = pytest.mark.skipif(direct_skip_condition, reason=direct_skip_reason)

# Test configuration
TEST_USERNAME = "zsmickycat"  # Use the same username for both Lichess and Chess.com
TEST_PASSWORD = "password123"
FETCH_SIZE = 5  # Number of games to fetch
MAX_WAIT_TIME = 300  # Maximum time to wait for games and puzzles (in seconds)
POLL_INTERVAL = 5  # Time between status checks (in seconds)

# Docker image and container names
DOCKER_IMAGE_NAME = "chessticize-worker"
DOCKER_CONTAINER_NAME = "chessticize-worker-e2e-test"

@pytest.fixture(scope="module")
def docker_image():
    """Builds the Docker image for testing and returns its name."""
    if e2e_skip_condition:
        pytest.skip(skip_reason)

    logger.info(f"Building Docker image: {DOCKER_IMAGE_NAME}")
    build_cmd = ["make", "docker-build"]

    try:
        subprocess.run(build_cmd, check=True)
        logger.info(f"Docker image built successfully: {DOCKER_IMAGE_NAME}")
        return DOCKER_IMAGE_NAME
    except subprocess.CalledProcessError as e:
        pytest.fail(f"Failed to build Docker image: {e}")

@pytest.fixture(scope="module")
def api_client_for_test():
    """Provides an AdminClient instance for the test to interact with the API."""
    if e2e_skip_condition and direct_skip_condition:
        pytest.skip("Skipping test (set either RUN_E2E=true or RUN_DIRECT=true to run)")

    # We'll use the default base URL

    # Save original token if it exists
    original_token = os.environ.get(ADMIN_API_TOKEN_ENV_VAR)

    try:
        # Generate a new admin token
        token = get_admin_token()
        logger.info(f"Generated admin token in api_client_for_test fixture: {token}")
        os.environ[ADMIN_API_TOKEN_ENV_VAR] = token

        # Create the client with the token
        client = AdminClient(worker_id="e2e-test-client")
        yield client
    except Exception as e:
        logger.error(f"Failed to initialize AdminClient for test: {e}", exc_info=True)
        pytest.fail(f"Failed to initialize AdminClient for test: {e}")
    finally:
        # Restore original environment
        if original_token:
            os.environ[ADMIN_API_TOKEN_ENV_VAR] = original_token
        elif ADMIN_API_TOKEN_ENV_VAR in os.environ and not original_token:
            # Only delete if we added it and there wasn't one before
            del os.environ[ADMIN_API_TOKEN_ENV_VAR]

@pytest.fixture(scope="function")
def test_user_with_profiles(api_client_for_test):
    """
    Creates a test user with both Lichess and Chess.com profiles.
    Returns a tuple of (user_id, lichess_profile_id, chesscom_profile_id, user_token).
    """
    if e2e_skip_condition and direct_skip_condition:
        pytest.skip("Skipping test (set either RUN_E2E=true or RUN_DIRECT=true to run)")

    admin_client = api_client_for_test
    user_client = UserClient(base_url=DEFAULT_API_BASE_URL)

    # Create a unique test user email
    test_user_email = f"test_e2e_user_{uuid.uuid4()}@example.com"

    # 1. Create User
    logger.info(f"Creating test user: {test_user_email}")
    created_user = admin_client.create_user(test_user_email, TEST_PASSWORD)
    if not created_user or not created_user.id:
        pytest.fail(f"Failed to create test user {test_user_email} via API.")
    user_id = created_user.id
    logger.info(f"User created with ID: {user_id}")

    # 2. Login as the newly created user to obtain a user token
    logger.info(f"Logging in as newly created user to obtain token...")
    try:
        user_token = user_client.login(test_user_email, TEST_PASSWORD)
    except Exception as e:
        pytest.fail(f"Login failed for user {test_user_email}: {e}")

    # 3. Create Chess.com profile
    logger.info(f"Creating Chess.com profile for {TEST_USERNAME}")
    try:
        chesscom_profile_json = user_client.create_chess_profile(
            user_token,
            platform=ChessPlatform.chess_dot_com.value,
            username=TEST_USERNAME,
        )
    except Exception as e:
        pytest.fail(f"Failed to create Chess.com profile for {TEST_USERNAME}: {e}")

    chesscom_profile_id = chesscom_profile_json.get("id")
    if not chesscom_profile_id:
        pytest.fail(f"User API did not return profile id after Chess.com profile creation: {chesscom_profile_json}")
    logger.info(f"Chess.com profile created with ID: {chesscom_profile_id}")

    # 4. Create Lichess profile
    logger.info(f"Creating Lichess profile for {TEST_USERNAME}")
    try:
        lichess_profile_json = user_client.create_chess_profile(
            user_token,
            platform=ChessPlatform.lichess_org.value,
            username=TEST_USERNAME,
        )
    except Exception as e:
        pytest.fail(f"Failed to create Lichess profile for {TEST_USERNAME}: {e}")

    lichess_profile_id = lichess_profile_json.get("id")
    if not lichess_profile_id:
        pytest.fail(f"User API did not return profile id after Lichess profile creation: {lichess_profile_json}")
    logger.info(f"Lichess profile created with ID: {lichess_profile_id}")

    yield user_id, lichess_profile_id, chesscom_profile_id, user_token

@pytest.fixture(scope="function")
def worker_container(docker_image, api_client_for_test):  # noqa: F811 - api_client_for_test is used implicitly
    """Runs the worker Docker container and yields its ID."""
    # We need api_client_for_test as a dependency to ensure the admin token is set in the environment
    # but we don't actually use the client object directly
    admin_token = os.environ.get(ADMIN_API_TOKEN_ENV_VAR)

    # Use host.docker.internal to reference the host machine from inside the Docker container
    # This works on macOS and Windows with Docker Desktop
    # For Linux, you might need to use the host's actual IP or add --add-host to the Docker run command
    api_base_url = "http://host.docker.internal:8080"
    log_level = "INFO"

    # Check if ADMIN_API_TOKEN is already set
    if not admin_token:
        # Generate a new admin token if not set
        try:
            admin_token = get_admin_token()
            logger.info(f"Generated admin token in worker_container fixture: {admin_token}")
            os.environ[ADMIN_API_TOKEN_ENV_VAR] = admin_token
        except Exception as e:
            logger.error(f"Failed to generate admin token in worker_container fixture: {e}", exc_info=True)
            pytest.fail(f"Failed to generate admin token in worker_container fixture: {e}")

    # Check if a container with the same name already exists and remove it
    logger.info(f"Checking for existing container with name: {DOCKER_CONTAINER_NAME}")
    try:
        # Check if the container exists
        check_cmd = ["docker", "ps", "-a", "-q", "-f", f"name={DOCKER_CONTAINER_NAME}"]
        result = subprocess.run(check_cmd, check=True, capture_output=True, text=True)
        existing_container_id = result.stdout.strip()

        if existing_container_id:
            logger.info(f"Found existing container {existing_container_id[:12]}, removing it...")
            # Stop the container if it's running
            subprocess.run(["docker", "stop", DOCKER_CONTAINER_NAME], check=False)
            # Remove the container
            subprocess.run(["docker", "rm", DOCKER_CONTAINER_NAME], check=True)
            logger.info(f"Removed existing container {existing_container_id[:12]}")
    except subprocess.CalledProcessError as e:
        logger.warning(f"Error checking for existing container: {e}")
        # Continue anyway, as this is just a precaution

    # Set environment variables for the worker container
    env_vars = [
        f"{ADMIN_API_TOKEN_ENV_VAR}={admin_token}",
        f"API_BASE_URL={api_base_url}",
        f"LOG_LEVEL={log_level}",
        f"MAX_GAMES_PER_FETCH={FETCH_SIZE}"  # Set the fetch size to exactly 5 games
    ]

    logger.info("Starting worker container...")
    run_cmd = [
        "docker", "run", "--rm", "-d",
        # Add host.docker.internal mapping for Linux compatibility
        "--add-host=host.docker.internal:host-gateway",
        # Name the container for easier management
        "--name", DOCKER_CONTAINER_NAME
    ]

    # Add environment variables
    for env_var in env_vars:
        run_cmd.extend(["-e", env_var])

    # Add the image name
    run_cmd.append(docker_image)

    try:
        logger.info(f"Running command: {' '.join(run_cmd)}")
        result = subprocess.run(
            run_cmd,
            check=True,
            capture_output=True,
            text=True
        )
        container_id = result.stdout.strip()
        logger.info(f"Worker container started with ID: {container_id[:12]} and name: {DOCKER_CONTAINER_NAME}")

        # Wait for container to initialize
        time.sleep(5)

        yield container_id

        # Cleanup: Stop the container
        logger.info(f"Stopping worker container {container_id[:12]} ({DOCKER_CONTAINER_NAME})...")
        subprocess.run(["docker", "stop", DOCKER_CONTAINER_NAME], check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to start or stop worker container: {e}")
        if e.stderr:
            logger.error(f"Error output: {e.stderr}")
        pytest.fail(f"Failed to start or stop worker container: {e}")

# --- Helper Functions ---

def run_command(command, check=True, capture_output=True, text=True):
    """Runs a shell command."""
    logger.info(f"Running command: {' '.join(command)}")
    try:
        result = subprocess.run(command, check=check, capture_output=capture_output, text=text)
        if result.stdout:
            logger.debug(f"Command stdout: {result.stdout.strip()}")
        if result.stderr:
            logger.debug(f"Command stderr: {result.stderr.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e}")
        if e.stdout:
            logger.error(f"Stdout: {e.stdout.strip()}")
        if e.stderr:
            logger.error(f"Stderr: {e.stderr.strip()}")
        raise

# --- Direct Test Function ---

@pytest.fixture(scope="function")
def worker_process(chess_api_emulator):  # noqa: F811 - chess_api_emulator is used implicitly
    """
    Runs the worker process directly (without Docker) and yields a handle to it.
    This is much faster for debugging than using Docker.

    Uses the test emulator to avoid hitting the real Chess.com and Lichess APIs.
    The chess_api_emulator fixture is used implicitly to set up the test environment.
    """
    # Check if ADMIN_API_TOKEN is already set
    admin_token = os.environ.get(ADMIN_API_TOKEN_ENV_VAR)
    if not admin_token:
        # Generate a new admin token if not set
        from tests.conftest import get_admin_token
        try:
            admin_token = get_admin_token()
            logger.info(f"Generated admin token in worker_process fixture: {admin_token}")
            os.environ[ADMIN_API_TOKEN_ENV_VAR] = admin_token
        except Exception as e:
            logger.error(f"Failed to generate admin token in worker_process fixture: {e}", exc_info=True)
            pytest.fail(f"Failed to generate admin token in worker_process fixture: {e}")

    # Set the env var doesn't work for direct mode. Set config directly.
    from chessticize_worker import config
    config.MAX_GAMES_PER_FETCH = FETCH_SIZE

    # Configure logging for the worker
    log_level = os.environ.get("LOG_LEVEL", "DEBUG")

    # The chess_api_emulator fixture is automatically applied
    logger.info("Using chess_api_emulator to avoid hitting real Chess.com and Lichess APIs")

    # Configure root logger to see all logs
    root_logger = logging.getLogger()

    # Convert string log level to numeric level
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        numeric_level = logging.DEBUG  # Default to DEBUG if invalid level

    root_logger.setLevel(numeric_level)

    # Make sure we have a stdout handler
    has_stdout_handler = False
    for handler in root_logger.handlers:
        if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stdout:
            has_stdout_handler = True
            break

    if not has_stdout_handler:
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        root_logger.addHandler(handler)

    # Import the main module
    from chessticize_worker import main

    # Define a function to run the worker in a separate thread
    def run_worker():
        try:
            # Create a simple worker that just processes tasks
            # We can't use main.run_worker() directly because it uses signal handlers
            # which only work in the main thread
            from chessticize_worker.worker import process_single_task
            from chessticize_worker.api.admin_client import AdminClient

            # Create an admin client
            admin_client = AdminClient(worker_id="direct-test-worker")

            # Process tasks in a loop
            while not main.shutdown_requested:
                try:
                    process_single_task(admin_client)
                    # Sleep a bit to avoid hammering the API
                    time.sleep(1)
                except Exception as e:
                    logger.error(f"Error processing task: {e}")
                    time.sleep(5)  # Wait a bit longer after an error
        except Exception as e:
            logger.error(f"Error in worker process: {e}")

    # Start the worker in a separate thread
    worker_thread = threading.Thread(target=run_worker)
    worker_thread.daemon = True  # Make the thread a daemon so it exits when the main thread exits

    logger.info("Starting worker thread...")
    worker_thread.start()

    # Wait for the worker to initialize
    time.sleep(2)

    yield worker_thread

    # Cleanup: Set the shutdown flag to stop the worker
    logger.info("Stopping worker thread...")
    main.shutdown_requested = True

    # Wait for the worker to exit (with timeout)
    worker_thread.join(timeout=5)
    if worker_thread.is_alive():
        logger.warning("Worker thread did not exit cleanly, forcing exit...")

# --- Main Tests ---

@direct_test
def test_direct_worker(worker_process, test_user_with_profiles, api_client_for_test):
    """
    Test that runs the worker directly (without Docker) for faster debugging.

    Uses the test emulator to avoid hitting the real Chess.com and Lichess APIs.
    """
    # Check if ADMIN_API_TOKEN is already set
    admin_token = os.environ.get(ADMIN_API_TOKEN_ENV_VAR)
    if not admin_token:
        # Generate a new admin token if not set
        from tests.conftest import get_admin_token
        try:
            admin_token = get_admin_token()
            logger.info(f"Generated admin token: {admin_token}")
            os.environ[ADMIN_API_TOKEN_ENV_VAR] = admin_token
        except Exception as e:
            logger.error(f"Failed to generate admin token: {e}", exc_info=True)
            pytest.fail(f"Failed to generate admin token: {e}")
    else:
        logger.info(f"Using existing admin token: {admin_token}")

    # Get the test user and profiles
    user_id, _, _, user_token = test_user_with_profiles
    admin_client = api_client_for_test
    user_client = UserClient(base_url=DEFAULT_API_BASE_URL)

    logger.info(f"Running direct worker test with user: {user_id}")
    logger.info(f"Running direct worker test with user token: {user_token}")
    logger.info(f"Using test emulator for Chess.com and Lichess APIs")

    # Verify the worker process is running
    logger.info(f"Running direct worker test with worker thread: {worker_process}")

    # Get the initial tasks created when the profiles were created
    user_tasks = admin_client.list_user_tasks(user_id)
    assert user_tasks is not None, "Failed to list tasks for user"

    # There should be two FetchChessGames tasks (one for each profile)
    fetch_tasks = [t for t in user_tasks if t.task_type == "FetchChessGames"]
    assert len(fetch_tasks) == 2, f"Expected 2 FetchChessGames tasks, found {len(fetch_tasks)}"

    # Wait for games to be downloaded and puzzles to be generated
    logger.info(f"Waiting for games to be downloaded and puzzles to be generated (max {MAX_WAIT_TIME} seconds)...")

    start_time = time.time()
    games_downloaded = False
    puzzles_generated = False

    while time.time() - start_time < MAX_WAIT_TIME:
        elapsed = time.time() - start_time
        logger.info(f"Wait loop iteration at {elapsed:.1f}s / {MAX_WAIT_TIME}s")
        # Check if games have been downloaded
        try:
            games_response = user_client.list_games(user_token)
            logger.info(f"Games response: {games_response}")

            # Get the games list from the response
            game_list = games_response.get("games", [])

            # Process the game list
            chesscom_games = []
            lichess_games = []

            for g in game_list:
                platform = g.get("platform")
                if platform == ChessPlatform.chess_dot_com.value:
                    chesscom_games.append(g)
                elif platform == ChessPlatform.lichess_org.value:
                    lichess_games.append(g)

            logger.info(f"Found {len(chesscom_games)} Chess.com games and {len(lichess_games)} Lichess games")
            logger.info(f"Total games count: {games_response.get('total_count', 0)}")

        except Exception as e:
            logger.error(f"Error listing games: {e}", exc_info=True)
            chesscom_games = []
            lichess_games = []

        if len(chesscom_games) >= FETCH_SIZE and len(lichess_games) >= FETCH_SIZE:
            games_downloaded = True
            logger.info("✅ Games downloaded successfully")

        # Check if puzzles have been generated
        try:
            puzzles_response = user_client.list_puzzles(user_token)
            logger.info(f"Puzzles response: {puzzles_response}")

            # Get the puzzles list from the response
            puzzles = puzzles_response.get("puzzles", [])

            logger.info(f"Found {len(puzzles)} puzzles")
            logger.info(f"Total puzzles count: {puzzles_response.get('total_count', 0)}")

            if len(puzzles) >= 1:
                puzzles_generated = True
                logger.info("✅ Puzzles generated successfully")
        except Exception as e:
            logger.error(f"Error listing puzzles: {e}", exc_info=True)
            puzzles = []

        # If both conditions are met, we're done
        if games_downloaded and puzzles_generated:
            break

        # Wait before checking again
        logger.info(f"Waiting {POLL_INTERVAL} seconds before checking again...")
        time.sleep(POLL_INTERVAL)

    # Verify the results
    assert games_downloaded, f"Failed to download at least {FETCH_SIZE} games from each platform within {MAX_WAIT_TIME} seconds"
    assert puzzles_generated, f"Failed to generate at least 1 puzzle within {MAX_WAIT_TIME} seconds"

    # Final verification
    try:
        games_response = user_client.list_games(user_token)
        logger.info(f"Final games response: {games_response}")

        # Get the games list from the response
        game_list = games_response.get("games", [])

        # Process the game list
        chesscom_games = []
        lichess_games = []

        for g in game_list:
            platform = g.get("platform")
            if platform == ChessPlatform.chess_dot_com.value:
                chesscom_games.append(g)
            elif platform == ChessPlatform.lichess_org.value:
                lichess_games.append(g)

        logger.info(f"Final count: {len(chesscom_games)} Chess.com games and {len(lichess_games)} Lichess games")
        logger.info(f"Final total games count: {games_response.get('total_count', 0)}")

    except Exception as e:
        logger.error(f"Error listing final games: {e}", exc_info=True)
        chesscom_games = []
        lichess_games = []

    try:
        puzzles_response = user_client.list_puzzles(user_token)
        logger.info(f"Final puzzles response: {puzzles_response}")

        # Get the puzzles list from the response
        puzzles = puzzles_response.get("puzzles", [])

        logger.info(f"Final puzzles count: {len(puzzles)}")
        logger.info(f"Final total puzzles count: {puzzles_response.get('total_count', 0)}")
    except Exception as e:
        logger.error(f"Error listing final puzzles: {e}", exc_info=True)
        puzzles = []

    logger.info(f"Final count: {len(chesscom_games)} Chess.com games, {len(lichess_games)} Lichess games, {len(puzzles)} puzzles")

    # Assertions for final verification
    assert len(chesscom_games) >= FETCH_SIZE, f"Expected at least {FETCH_SIZE} Chess.com games, found {len(chesscom_games)}"
    assert len(lichess_games) >= FETCH_SIZE, f"Expected at least {FETCH_SIZE} Lichess games, found {len(lichess_games)}"
    assert len(puzzles) >= 1, f"Expected at least 1 puzzle, found {len(puzzles)}"

@e2e_test
def test_e2e_worker(worker_container, test_user_with_profiles, api_client_for_test):
    """
    End-to-end test for the worker functionality.

    1. Uses the Makefile to build the Docker image
    2. Initializes the server environment
    3. Creates a test user with both Lichess and Chess.com profiles
    4. Configures the fetch size to exactly 5 games
    5. Starts the game fetcher process
    6. Waits for games to be downloaded and puzzles to be generated
    7. Verifies the results
    """
    # Get the test user and profiles
    user_id, _, _, user_token = test_user_with_profiles  # We don't need to use the profile IDs directly
    admin_client = api_client_for_test
    user_client = UserClient(base_url=DEFAULT_API_BASE_URL)

    # Verify the worker container is running
    container_id = worker_container
    logger.info(f"Running E2E test with worker container {container_id[:12]}")

    # Get the initial tasks created when the profiles were created
    user_tasks = admin_client.list_user_tasks(user_id)
    assert user_tasks is not None, "Failed to list tasks for user"

    # There should be two FetchChessGames tasks (one for each profile)
    fetch_tasks = [t for t in user_tasks if t.task_type == "FetchChessGames"]
    assert len(fetch_tasks) == 2, f"Expected 2 FetchChessGames tasks, found {len(fetch_tasks)}"

    # Wait for games to be downloaded and puzzles to be generated
    logger.info(f"Waiting for games to be downloaded and puzzles to be generated (max {MAX_WAIT_TIME} seconds)...")

    start_time = time.time()
    games_downloaded = False
    puzzles_generated = False

    while time.time() - start_time < MAX_WAIT_TIME:
        # Check if games have been downloaded
        try:
            games_response = user_client.list_games(user_token)
            logger.info(f"Games response: {games_response}")

            # Get the games list from the response
            game_list = games_response.get("games", [])

            # Process the game list
            chesscom_games = []
            lichess_games = []

            for g in game_list:
                platform = g.get("platform")
                if platform == ChessPlatform.chess_dot_com.value:
                    chesscom_games.append(g)
                elif platform == ChessPlatform.lichess_org.value:
                    lichess_games.append(g)

            logger.info(f"Found {len(chesscom_games)} Chess.com games and {len(lichess_games)} Lichess games")
            logger.info(f"Total games count: {games_response.get('total_count', 0)}")

        except Exception as e:
            logger.error(f"Error listing games: {e}", exc_info=True)
            chesscom_games = []
            lichess_games = []

        if len(chesscom_games) >= FETCH_SIZE and len(lichess_games) >= FETCH_SIZE:
            games_downloaded = True
            logger.info("✅ Games downloaded successfully")

        # Check if puzzles have been generated
        try:
            puzzles_response = user_client.list_puzzles(user_token)
            logger.info(f"Puzzles response: {puzzles_response}")

            # Get the puzzles list from the response
            puzzles = puzzles_response.get("puzzles", [])

            logger.info(f"Found {len(puzzles)} puzzles")
            logger.info(f"Total puzzles count: {puzzles_response.get('total_count', 0)}")

            if len(puzzles) >= 1:
                puzzles_generated = True
                logger.info("✅ Puzzles generated successfully")
        except Exception as e:
            logger.error(f"Error listing puzzles: {e}", exc_info=True)
            puzzles = []

        # If both conditions are met, we're done
        if games_downloaded and puzzles_generated:
            break

        # Wait before checking again
        logger.info(f"Waiting {POLL_INTERVAL} seconds before checking again...")
        time.sleep(POLL_INTERVAL)

    # Verify the results
    assert games_downloaded, f"Failed to download at least {FETCH_SIZE} games from each platform within {MAX_WAIT_TIME} seconds"
    assert puzzles_generated, f"Failed to generate at least 1 puzzle within {MAX_WAIT_TIME} seconds"

    # Final verification
    try:
        games_response = user_client.list_games(user_token)
        logger.info(f"Final games response: {games_response}")

        # Get the games list from the response
        game_list = games_response.get("games", [])

        # Process the game list
        chesscom_games = []
        lichess_games = []

        for g in game_list:
            platform = g.get("platform")
            if platform == ChessPlatform.chess_dot_com.value:
                chesscom_games.append(g)
            elif platform == ChessPlatform.lichess_org.value:
                lichess_games.append(g)

        logger.info(f"Final count: {len(chesscom_games)} Chess.com games and {len(lichess_games)} Lichess games")
        logger.info(f"Final total games count: {games_response.get('total_count', 0)}")

    except Exception as e:
        logger.error(f"Error listing final games: {e}", exc_info=True)
        chesscom_games = []
        lichess_games = []

    try:
        puzzles_response = user_client.list_puzzles(user_token)
        logger.info(f"Final puzzles response: {puzzles_response}")

        # Get the puzzles list from the response
        puzzles = puzzles_response.get("puzzles", [])

        logger.info(f"Final puzzles count: {len(puzzles)}")
        logger.info(f"Final total puzzles count: {puzzles_response.get('total_count', 0)}")
    except Exception as e:
        logger.error(f"Error listing final puzzles: {e}", exc_info=True)
        puzzles = []

    logger.info(f"Final count: {len(chesscom_games)} Chess.com games, {len(lichess_games)} Lichess games, {len(puzzles)} puzzles")

    # Assertions for final verification
    assert len(chesscom_games) >= FETCH_SIZE, f"Expected at least {FETCH_SIZE} Chess.com games, found {len(chesscom_games)}"
    assert len(lichess_games) >= FETCH_SIZE, f"Expected at least {FETCH_SIZE} Lichess games, found {len(lichess_games)}"
    assert len(puzzles) >= 1, f"Expected at least 1 puzzle, found {len(puzzles)}"