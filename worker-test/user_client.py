# Module docstring first, as required when using `__future__` imports.
"""tests/user_client.py
A minimal HTTP client for interacting with *public* (non-admin) user API
endpoints required by the integration tests.  Only the subset of features
needed by the test suite (login & chess-profile CRUD) is implemented.

This avoids adding user-facing APIs to the production `chessticize_worker`
codebase while still allowing integration tests to exercise realistic
workflows.
"""

from __future__ import annotations

from typing import Optional, Dict, Any, List
import requests

# Re-use the same configuration the worker relies on for base URL / timeouts
from chessticize_worker import config


class UserClient:
    """Lightweight wrapper around the public user APIs."""

    def __init__(self, base_url: Optional[str] = None, request_timeout: Optional[int] = None):
        self.base_url: str = base_url or config.API_BASE_URL
        self.timeout: int = request_timeout or config.REQUEST_TIMEOUT

    # ---------------------------------------------------------------------
    # Internal helpers
    # ---------------------------------------------------------------------

    def _make_request(
        self,
        method: str,
        path: str,
        *,
        json: Optional[Dict[str, Any]] = None,
        token: Optional[str] = None,
    ) -> Any:
        """Internal helper to perform an HTTP request.

        Args:
            method: HTTP method ("GET", "POST", ...).
            path: Path starting with `/`, relative to `self.base_url`.
            json: Optional JSON body to send.
            token: If provided, the request will include a Bearer token.

        Returns:
            Parsed JSON response (dict / list) or an empty dict for 204.

        Raises:
            requests.HTTPError: Propagated if server returns 4xx/5xx.
            ValueError: If response body is not JSON when JSON is expected.
        """
        url = f"{self.base_url}{path}"
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
        }
        if token:
            headers["Authorization"] = f"Bearer {token}"

        response = requests.request(
            method,
            url,
            headers=headers,
            json=json,
            timeout=self.timeout,
        )
        # Raise for bad status codes so tests fail loudly and with context
        response.raise_for_status()

        if response.status_code == 204:
            return {}

        content_type = response.headers.get("Content-Type", "")
        if "application/json" in content_type:
            return response.json()
        # Allow non-JSON responses but still return something useful
        return {"raw_response": response.text}

    # ------------------------------------------------------------------
    # Public API helpers
    # ------------------------------------------------------------------

    def login(self, email: str, password: str) -> str:
        """Obtains a JWT for the given user credentials.

        Returns the token string so callers can authenticate subsequent
        requests.  The server's response field name may vary (e.g. `token`,
        `access_token`, `jwt`).  We check a few common possibilities.
        """
        data = {"email": email, "password": password}
        resp = self._make_request("POST", "/api/v1/auth/login", json=data)

        token = (
            resp.get("token")
            or resp.get("access_token")
            or resp.get("jwt")
            or resp.get("id_token")
        )
        if not token or not isinstance(token, str):
            raise ValueError("Login response did not include a token field we understand.")
        return token

    def create_chess_profile(self, token: str, *, platform: str, username: str) -> Dict[str, Any]:
        """Creates a chess profile for the *authenticated* user.

        Args:
            token: Bearer token from `login`.
            platform: Platform identifier (e.g. "chess.com", "lichess.org").
            username: The username on the platform.
        """
        data = {"platform": platform, "username": username}
        return self._make_request("POST", "/api/v1/users/me/chess-profiles", json=data, token=token)

    def list_chess_profiles(self, token: str) -> List[Dict[str, Any]]:
        """Lists chess profiles for the *authenticated* user."""
        return self._make_request("GET", "/api/v1/users/me/chess-profiles", token=token)

    def delete_chess_profile(self, token: str, profile_id: str) -> None:
        """Deletes a chess profile for the *authenticated* user (fire-and-forget)."""
        self._make_request("DELETE", f"/api/v1/users/me/chess-profiles/{profile_id}", token=token)

    def list_games(self, token: str) -> Dict[str, Any]:
        """
        Lists games for the *authenticated* user.

        Returns:
            A dictionary with the following structure:
            {
                "games": List[Dict[str, Any]],  # Array of game objects
                "total_count": int,             # Total number of games
                "offset": int,                  # Pagination offset
                "limit": int                    # Pagination limit
            }
        """
        return self._make_request("GET", "/api/v1/users/me/games", token=token)

    def list_puzzles(self, token: str) -> Dict[str, Any]:
        """
        Lists puzzles for the *authenticated* user.

        Returns:
            A dictionary with the following structure:
            {
                "puzzles": List[Dict[str, Any]],  # Array of puzzle objects
                "total_count": int,               # Total number of puzzles
                "offset": int,                    # Pagination offset
                "limit": int                      # Pagination limit
            }
        """
        return self._make_request("GET", "/api/v1/users/me/puzzles", token=token)
